package main

import (
	"github.com/gofiber/fiber/v2"

	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/fn/api/env"
	"github.com/drumkitai/drumkit/fn/api/routes/appt"
	"github.com/drumkitai/drumkit/fn/api/routes/carriergroups"
	"github.com/drumkitai/drumkit/fn/api/routes/carrierverification"
	"github.com/drumkitai/drumkit/fn/api/routes/drumkit"
	"github.com/drumkitai/drumkit/fn/api/routes/email"
	"github.com/drumkitai/drumkit/fn/api/routes/integrations"
	"github.com/drumkitai/drumkit/fn/api/routes/load"
	"github.com/drumkitai/drumkit/fn/api/routes/metabase"
	"github.com/drumkitai/drumkit/fn/api/routes/metrics"
	"github.com/drumkitai/drumkit/fn/api/routes/order"
	"github.com/drumkitai/drumkit/fn/api/routes/outbox"
	privatequote "github.com/drumkitai/drumkit/fn/api/routes/quote/sidebar"
	publicquote "github.com/drumkitai/drumkit/fn/api/routes/quote/webpage"
	"github.com/drumkitai/drumkit/fn/api/routes/service"
	"github.com/drumkitai/drumkit/fn/api/routes/suggestions"
	tmsroutes "github.com/drumkitai/drumkit/fn/api/routes/tms"
	"github.com/drumkitai/drumkit/fn/api/routes/truck"
	"github.com/drumkitai/drumkit/fn/api/routes/user"
	"github.com/drumkitai/drumkit/fn/api/routes/usergroups"
)

// NOTE: in prod, API Gateway will prefix the stage name ("/v1/") to each of these routes
func buildApp() *fiber.App {
	app := fiber.New()

	app.Use(middleware.Sentry())
	app.Use(middleware.Tracer())
	app.Use(middleware.Zap(middleware.WithAppEnv(env.Vars.AppEnv)))

	// Required to allow localhost and gmail to query the API
	// https://docs.aws.amazon.com/apigateway/latest/developerguide/how-to-cors.html#apigateway-enable-cors-proxy
	app.Use(middleware.CORS(middleware.WithCORSOrigins("*")))

	// Open endpoints (no auth header required)
	app.Get("/cyclops/health", drumkit.PublicGetCyclopsHealth)

	app.Post("/user/signup/google", user.SignupGoogle)
	app.Post("/user/signup/microsoft", user.SignupMicrosoft)
	app.Post("/user/signup/front", user.SignupFront)

	app.Post("/user/login/google", user.LoginGoogle)
	app.Post("/user/login/microsoft", user.LoginMicrosoft)
	app.Post("/user/login/microsoft/portal", user.LoginMicrosoftPortal)
	app.Post("/user/login/front", user.LoginFront)

	// Open endpoint so that prospects (aka shippers) of Drumkit customers (aka brokers) can get quick quotes
	app.Get("/service/:nickname", service.PublicGetService)
	app.Post("/quote/:nickname", publicquote.GetQuickQuote)
	app.Post("/quote/:nickname/contact", publicquote.EmailLead)

	// OnPrem endpoints
	app.Post("onprem/user/signup/gmail", user.SignupGoogleFromOnPrem)
	app.Post("onprem/user/signup/outlook", user.SignupMicrosoftFromOnPrem)
	app.Post("onprem/user/login/gmail", user.LoginGoogleFromOnPrem)
	app.Post("onprem/user/login/outlook", user.LoginMicrosoftFromOnPrem)
	// Endpoint for manual OnPrem ingestion
	app.Post("onprem/thread/:id/ingest", email.IngestOnPremEmail)
	// Endpoint to update the user's webhook token expiration
	app.Post("onprem/user/update", user.UpdateOnPremUser)

	// Parse JWT from auth header
	app.Use(
		middleware.ParseToken(
			middleware.WithJWT(env.Vars.JWT),
			// TODO: prepend keys with `drumkit` not `beacon`
			middleware.WithContextKey("beacon-jwt-claims"),
			middleware.WithServiceIDContextKey("beacon-service-id"),
			middleware.WithUserIDContextKey("drumkit-user-id"),
		),
	)

	// Each route must use the token claims to authorize the request

	// Appointment routes
	app.Get("/appt/slots", appt.GetOpenSlots)
	app.Post("/appt/v2", appt.MakeV2)
	app.Post("/appt/validate", appt.ValidateAppt)
	app.Post("/appt/submit", appt.SubmitAppt)
	app.Get("/appt/:loadID/sops", appt.GetApptTemplate)
	app.Post("/appt/email-request", appt.SaveApptEmailRequest)

	// Fetch warehouses
	// Get all warehouses from integration by source
	app.Get("/warehouses/external", appt.GetWarehousesExternalBySource)
	// TODO: cleanup after Vulcan is released
	app.Get("/warehouse/search", appt.GetWarehousesBySearch)
	// Get warehouses matching search query
	app.Get("/warehouses/search", appt.GetWarehousesBySearch)
	// Get warehouses associated with recent loads
	app.Get("/warehouses/recent", appt.GetWarehousesRecent)

	// Fetch external warehouse data
	app.Get("/loadtypes", appt.GetLoadTypes) // Get Load Types (through Scheduler's API) for the Warehouse
	app.Get("/warehouse", appt.GetWarehouse) // Get info (from our DB, or API if needed) about the Warehouse
	app.Get("/carrier-scacs", appt.GetCarrierScacs)

	// Warehouse onboarding routes
	app.Post("/warehouses/onboard", appt.OnboardWarehousesBySource) // Add all warehouses from source to our DB

	// User routes
	app.Get("/user", user.GetUser)
	app.Get("/user/integrations", user.GetUserIntegrations) // Group-specific integrations for user
	app.Get("/user/dat", user.GetUserDATInfo)
	app.Patch("/user/dat", user.EnableDATIndividualAccess)
	app.Patch("/user/signature", user.UpdateUserSignature)
	app.Patch("/user/groups", user.UpdateUserGroups)
	app.Post("/user/webhooktoken", user.WebhookTokenGenerator)
	app.Post("/user/logout", user.Logout)
	app.Get("/user/tms-info", user.GetTMSInfo)
	app.Get("/user/revenue-code", user.GetTMSInfo) // TODO: Deprecate after 6/10/2025

	// User Configuration routes
	app.Patch("/user/config/price-margin", user.UpdateDefaultPriceMargin)

	// User Groups routes
	app.Get("/groups/:serviceID", usergroups.GetUserGroups)

	// Email routes
	app.Get("/email/thread/:id", email.GetEmailByThreadID)
	app.Get("/email/:external_id/attachments", email.GetEmailAttachments)
	app.Post("/email/ingest", email.DevIngestEmail)
	app.Get("/email/ingest/status/:id", email.GetIngestionStatusByThreadID)
	app.Post("/email/reprocess-content/:id", email.ReprocessContent)

	// Email Outbox routes
	app.Patch("/outbox/email/:id", outbox.UpdateEmail)
	app.Delete("/outbox/email/:id", outbox.DeleteEmail)
	app.Patch("/outbox/email/undo-delete/:id", outbox.UndoDeleteEmail)

	// Load routes
	app.Post("/load", load.CreateLoad)
	app.Patch("/load/:loadID", load.UpdateLoad)
	app.Get("/load/external/:externalTMSID", load.GetLoad)
	app.Get("/load/:loadID/checkCalls", load.GetCheckCallsHistory)
	app.Post("/load/:loadID/checkCall", load.PostCheckCall)
	app.Post("/load/:loadID/sops/carrier", load.SubmitCarrierSOPs)
	app.Get("/load/:loadID/sops/carrier", load.GetCarrierTemplates)
	app.Get("/load/:loadID/exceptions", load.GetExceptionsHistory)
	app.Post("/load/:loadID/exception", load.PostException)
	app.Post("/load/:loadID/note", load.PostNote)
	app.Get("/load/:loadID/orders", load.GetOrdersForLoad)

	// Search by query
	app.Get("/load", load.GetLoadBySearch)

	// Starred loads
	app.Get("/load/starred", load.GetStarredLoads)
	app.Put("/load/starred/:loadID", load.PutStarredLoad)
	app.Delete("/load/starred/:loadID", load.DeleteStarredLoad)

	// Viewed Loads
	app.Get("/load/viewed", load.GetViewedLoads)
	app.Put("/load/viewed/:loadID", load.PutViewedLoad)

	// Order routes
	app.Post("/orders", order.CreateOrder)
	app.Get("/orders/:id", order.GetOrder)
	app.Put("/orders/:id", order.UpdateOrder)
	app.Delete("/orders/:id", order.DeleteOrder)
	app.Get("/orders/reference", order.GetOrderByReference)
	app.Get("/orders/tracking/:trackingId", order.GetOrderByTrackingID)
	app.Get("/orders", order.ListOrders)

	// Order-Load relationship operations
	app.Post("/orders/:id/loads", order.AddLoadToOrder)
	app.Delete("/orders/:id/loads/:loadID", order.RemoveLoadFromOrder)

	// FreightTrackingID routes
	app.Get("/freight/:freightTrackingID/load", load.GetLoadByFreightID)

	// Truck List routes
	app.Get("/thread/:threadID/email/:emailID/trucks", truck.GetByEmailOrThreadID)
	app.Post("/trucklist/create", truck.CreateTruckList)
	app.Post("/trucklist/submit", truck.SubmitTruckList)
	app.Post("/trucklist/truck/create", truck.AddTruckToTruckList)
	app.Put("/trucklist/validate/carrier", truck.ValidateCarrier)
	app.Delete("/trucklist/truck/delete/:truckID", truck.DeleteTruckFromTruckList)

	// Suggestions routes
	app.Get("/thread/:threadID/quote/request/suggestions", suggestions.GetQuickQuoteSuggestionsByThreadID)
	app.Get("/thread/:threadID/suggestions/loadBuilding", suggestions.GetLoadBuildingSuggestionsByThreadID)
	app.Get("/thread/:threadID/load/:loadID/suggestions", suggestions.GetSuggestionsByLoadAndThreadID)
	app.Post("/suggestions/:ID/appt", suggestions.ApplyApptConfirmation) // remove once Drumkit v0.22.2 is released
	app.Post("/suggestions/:ID/carrier", suggestions.HandleCarrierInfo)  // remove once Drumkit v0.22.2 is released
	app.Patch("/suggestions/:ID/loadinfo", suggestions.ApplyLoadInfo)
	app.Post("/suggestions/quoting-portal/quote-request", suggestions.CreateQuoteRequestSuggestion)
	app.Patch("suggestions/:ID/quote-request", suggestions.UpdateQuoteRequestSuggestion)
	// NOTE: Order matters because Fiber uses the first route that matches the path, thus more specific paths
	// (with more static segments) should come before more general paths (with fewer static segments)
	app.Post("/suggestions/:ID", suggestions.SkipSuggestion)

	// Protected quote routes
	app.Get("/quote/request/thread/:threadID", privatequote.GetQuoteRequest)
	app.Post("/quote/request/carrierNetwork", privatequote.EmailCarriersForQuotes)

	// Email Templates
	app.Get("/email-templates/carrier-quote", privatequote.GetCarrierGroupTemplates)

	// Carrier Group routes
	app.Post("/carrier-groups", carriergroups.CreateCarrierGroups) // protected route
	app.Get("/carrier-groups/search", carriergroups.SearchCarrierGroups)

	app.Post("/quote/quickquote/v2", privatequote.GetQuickQuoteV2)
	app.Post("/quote/quickquote/dat/market-information", privatequote.GetDATMarketInformation)

	// Fetch lane history from quoting tools and/or TMS
	app.Post("/quote/private/laneHistory", privatequote.GetLaneHistory)
	// Submitting quotes via hyperlink/URL
	app.Get("/quote/private/quote-number/email/:emailID", privatequote.GetPrivateQuoteNumber)
	app.Post("/quote/private/url-submit/email/:emailID", privatequote.PostSubmitQuoteViaURL)

	// Send quoting information to service
	app.Post("/quote/private/send-to-service/greenscreens", privatequote.PostGreenscreensQuoteToService)
	app.Post("/quote/private/send-to-service/user", privatequote.PostUserQuoteToService)
	// TODO: Remove once unused
	app.Post("/quote/private/send-to-service/greenscreens/email/:emailID", privatequote.PostGreenscreensQuoteToService)
	app.Post("/quote/private/send-to-service/user/email/:emailID", privatequote.PostUserQuoteToService)

	// Fetch quoting information from service
	app.Post("/quote/private/get-from-service/lane-rate", privatequote.GetLaneRateFromService)
	app.Post("/quote/private/get-from-service/lane-history", privatequote.GetLaneHistoryFromService)
	// Create quote reply draft
	app.Post("/quote/private/quickquote/draft", privatequote.PostQuickQuoteReplyDraft)
	// Create quote on TMS
	app.Post("/quote/private/tms/create", privatequote.PostQuoteToTMS)
	// Fetch fuel price from EIA Gov website
	app.Get("/quote/fuel/recent-price", privatequote.GetFuelRecentPrice)

	app.Post("/batch-quote", privatequote.GetBatchQuote)

	// Fetch TMS objects
	app.Get("/customers", tmsroutes.GetCustomers)
	app.Get("/locations", tmsroutes.GetLocations)
	app.Get("/carriers", tmsroutes.GetCarriers)
	app.Get("/carriers/checkQualification", tmsroutes.GetCarrierQualification) // Implemented only for Mcleod rn
	app.Get("/operators", tmsroutes.GetOperators)
	app.Get("/customers/search", tmsroutes.SearchCustomers)
	app.Get("/locations/search", tmsroutes.SearchLocations)
	app.Get("/carriers/search", tmsroutes.SearchCarriers)
	app.Get("/operators/search", tmsroutes.SearchOperators)
	app.Post("/locations", tmsroutes.CreateLocation)

	app.Get("/carrierverification/getcarrier", carrierverification.GetCarrierVerification)
	app.Post("/carrierverification/invitecarrier", carrierverification.InviteCarrierVerification)

	// Services
	// Fetch service feature flags
	app.Get("/service/:serviceID/features", service.GetServiceFeatures)
	// Clear service from cache
	app.Post("/service/clear-cache", service.ClearServiceCache)
	// Fetch all services and their feature flags (<EMAIL> only)
	app.Get("/service/features/all", service.GetAllServiceFeatures)
	// Toggle feature flag for a service (<EMAIL> only)
	app.Post("/service/:serviceID/features/toggle", service.ToggleFeatureFlag)

	// Integration Onboarding
	app.Post("/integrations/tms/onboard", integrations.OnboardTMS)
	app.Post("/integrations/carrierverification/onboard", integrations.OnboardCarrierVerification)
	app.Post("/integrations/scheduling/onboard", integrations.OnboardScheduler)
	app.Post("/integrations/pricing/onboard", integrations.OnboardPricing)

	// Webhook handling
	app.Post("/integrations/tai/webhooks/load", integrations.TaiLoadWebhookHandler)
	app.Post("/integrations/tai/webhooks/customer", integrations.TaiCustomerWebhookHandler)
	app.Post("/integrations/webhooks/load", integrations.LoadWebhookHandler)

	// Metrics
	app.Get("/metrics/:serviceID/users", user.GetUsersByServiceID)
	app.Get("/metrics/:serviceID/customers", tmsroutes.GetCustomersByServiceID)
	app.Post("/metrics/:serviceID/dashboard/quotes", metrics.PostQuoteDashboard)
	app.Get("/metrics/:serviceID/quote-requests", metrics.GetQuoteRequests)
	app.Get("/metrics/:serviceID/quote-requests/download", metrics.ExportQuoteRequestsToCSV)
	app.Get("/metrics/:serviceID/quotes", metrics.GetQuotes)

	// Metabase dashboard routes
	app.Get("/metabase/:serviceID/dashboards", metabase.GetDashboards)
	app.Get("/metabase/:serviceID/dashboards/:id", metabase.GetDashboardByID)
	app.Post("/metabase/dashboard", metabase.AdminCreateDashboard)
	app.Put("/metabase/dashboard/:dashboardID", metabase.AdminUpdateDashboard)
	app.Delete("/metabase/dashboard/:dashboardID", metabase.AdminDeleteDashboard)

	return app
}
