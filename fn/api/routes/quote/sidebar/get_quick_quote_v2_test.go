package quoteprivate

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/integrations/pricing/dat"
	"github.com/drumkitai/drumkit/common/models"
	quote "github.com/drumkitai/drumkit/fn/api/routes/quote/common"
)

// Mock implementation
type MockQuickQuoteHandlers struct {
	mock.Mock
}

func (m *MockQuickQuoteHandlers) GetDATLaneRate(
	ctx context.Context,
	integration models.Integration,
	datEmailAddress string,
	req *[]dat.RateRequest,
) (*dat.GetLaneRateResponse, error) {
	args := m.Called(ctx, integration, datEmailAddress, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*dat.GetLaneRateResponse), args.Error(1)
}

func (m *MockQuickQuoteHandlers) CreateQuoteRecord(
	ctx context.Context,
	service models.Service,
	userID, emailID uint,
	threadID string, quoteRequestID uint,
	stops []models.Stop,
	transportType models.TransportType,
	pickupDate, deliveryDate time.Time,
	rateData quote.RateData,
	targetSellCost, minTargetSellCost, maxTargetSellCost float64,
	source models.QuoteSource,
	typeInSource models.QuoteTypeInSource,
	datMetadata *models.QuoteDATMetadata,
) *models.QuickQuote {

	args := m.Called(
		ctx, service, userID, emailID, threadID, quoteRequestID, stops,
		transportType, pickupDate, deliveryDate, rateData, targetSellCost,
		minTargetSellCost, maxTargetSellCost, source, typeInSource, datMetadata,
	)
	if args.Get(0) == nil {
		return nil
	}
	return args.Get(0).(*models.QuickQuote)
}

func (m *MockQuickQuoteHandlers) SetRedisMileDistanceBetweenLocations(
	ctx context.Context,
	originCity, originState, destinationCity, destinationState string,
	mileDistance float64,
) error {
	args := m.Called(ctx, originCity, originState, destinationCity, destinationState, mileDistance)
	return args.Error(0)
}

func TestHandleDATMultiStopLegToLeg(t *testing.T) {
	ctx := context.Background()

	// Setup test data
	mockService := &models.Service{
		Model: gorm.Model{ID: 1},
	}

	mockUser := &models.User{
		Model:           gorm.Model{ID: 100},
		DATEmailAddress: "<EMAIL>",
	}

	mockIntegration := &models.Integration{
		Model: gorm.Model{ID: 200},
	}

	body := &QuickQuotePrivateBody{
		LoadID:         "test-load-123",
		TransportType:  models.VanTransportType,
		EmailID:        300,
		ThreadID:       "test-thread-456",
		QuoteRequestID: 400,
		PickupDate:     time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC),
		DeliveryDate:   time.Date(2024, 1, 16, 0, 0, 0, 0, time.UTC),
	}

	stops := []models.Stop{
		{
			StopNumber: 0,
			Order:      0,
			Address: models.Address{
				City:  "New York",
				State: "NY",
				Zip:   "10001",
			},
		},
		{
			StopNumber: 1,
			Order:      1,
			Address: models.Address{
				City:  "Chicago",
				State: "IL",
				Zip:   "60601",
			},
		},
		{
			StopNumber: 2,
			Order:      2,
			Address: models.Address{
				City:  "Los Angeles",
				State: "CA",
				Zip:   "90001",
			},
		},
	}

	// Mock responses for each leg
	leg1Response := &dat.GetLaneRateResponse{
		RateResponses: []dat.RateResponseItem{
			{
				Response: dat.RateResponse{
					Rate: dat.Rate{
						Mileage:                        800.0,
						AverageFuelSurchargePerTripUsd: 120.0,
						AverageFuelSurchargePerMileUsd: 0.15,
						PerTrip: dat.PriceRange{
							RateUSD: 2400.0,
							LowUSD:  2200.0,
							HighUSD: 2600.0,
						},
						PerMile: dat.PriceRange{
							RateUSD: 3.0,
							LowUSD:  2.75,
							HighUSD: 3.25,
						},
						Reports:   10,
						Companies: 5,
					},
					Escalation: dat.Escalation{
						Timeframe: dat.SevenDays,
						Origin: dat.Location{
							Name: "New York, NY",
							Type: dat.State,
						},
						Destination: dat.Location{
							Name: "Chicago, IL",
							Type: dat.MarketArea,
						},
					},
				},
			},
		},
	}

	leg2Response := &dat.GetLaneRateResponse{
		RateResponses: []dat.RateResponseItem{
			{
				Response: dat.RateResponse{
					Rate: dat.Rate{
						Mileage:                        1200.0,
						AverageFuelSurchargePerTripUsd: 180.0,
						AverageFuelSurchargePerMileUsd: 0.15,
						PerTrip: dat.PriceRange{
							RateUSD: 3600.0,
							LowUSD:  3300.0,
							HighUSD: 3900.0,
						},
						PerMile: dat.PriceRange{
							RateUSD: 3.0,
							LowUSD:  2.75,
							HighUSD: 3.25,
						},
						Reports:   12,
						Companies: 6,
					},
					Escalation: dat.Escalation{
						Timeframe: dat.SevenDays,
						Origin: dat.Location{
							Name: "Chicago, IL",
							Type: "City",
						},
						Destination: dat.Location{
							Name: "Los Angeles, CA",
							Type: "City",
						},
					},
				},
			},
		},
	}

	leg1RespRate := leg1Response.RateResponses[0].Response.Rate
	leg2RespRate := leg2Response.RateResponses[0].Response.Rate

	// Define expected values
	expectedDistance := leg1RespRate.Mileage + leg2RespRate.Mileage

	expectedTarget := leg1RespRate.PerTrip.RateUSD +
		leg2RespRate.PerTrip.RateUSD +
		leg1RespRate.AverageFuelSurchargePerTripUsd +
		leg2RespRate.AverageFuelSurchargePerTripUsd

	expectedLow := leg1RespRate.PerTrip.LowUSD +
		leg2RespRate.PerTrip.LowUSD +
		leg1RespRate.AverageFuelSurchargePerTripUsd +
		leg2RespRate.AverageFuelSurchargePerTripUsd

	expectedHigh := leg1RespRate.PerTrip.HighUSD +
		leg2RespRate.PerTrip.HighUSD +
		leg1RespRate.AverageFuelSurchargePerTripUsd +
		leg2RespRate.AverageFuelSurchargePerTripUsd

	expectedTargetPerMile := expectedTarget / expectedDistance
	expectedLowPerMile := expectedLow / expectedDistance
	expectedHighPerMile := expectedHigh / expectedDistance

	// Metadata
	expectedReports := leg1RespRate.Reports + leg2RespRate.Reports
	expectedCompanies := leg1RespRate.Companies + leg2RespRate.Companies
	expectedFuelSurcharge := leg1RespRate.AverageFuelSurchargePerTripUsd + leg2RespRate.AverageFuelSurchargePerTripUsd
	expectedFuelSurchargePerMile := expectedFuelSurcharge / expectedDistance

	// Verify the returned Quote structure
	expectedQuote := Quote{
		ID:     uint(500),
		Source: models.DATSource,
		Type:   models.DATMultiStopLegToLeg,
		Rates: RateValues{
			Target:        expectedTarget,
			Low:           expectedLow,
			High:          expectedHigh,
			TargetPerMile: expectedTargetPerMile,
			LowPerMile:    expectedLowPerMile,
			HighPerMile:   expectedHighPerMile,
		},
		Distance: expectedDistance,
		Metadata: map[string]any{
			"reports":              expectedReports,
			"companies":            expectedCompanies,
			"fuelSurchargePerTrip": expectedFuelSurcharge,
			"fuelSurchargePerMile": expectedFuelSurchargePerMile,
			"legs": []RouteLeg{
				{
					Order:          0,
					StartStopIndex: 0,
					EndStopIndex:   1,
					StartCityState: "New York, NY",
					EndCityState:   "Chicago, IL",
					DistanceMiles:  leg1RespRate.Mileage,
					Rates: RateValues{
						Target:        leg1RespRate.PerTrip.RateUSD + leg1RespRate.AverageFuelSurchargePerTripUsd,
						Low:           leg1RespRate.PerTrip.LowUSD + leg1RespRate.AverageFuelSurchargePerTripUsd,
						High:          leg1RespRate.PerTrip.HighUSD + leg1RespRate.AverageFuelSurchargePerTripUsd,
						TargetPerMile: leg1RespRate.PerMile.RateUSD + leg1RespRate.AverageFuelSurchargePerMileUsd,
						LowPerMile:    leg1RespRate.PerMile.LowUSD + leg1RespRate.AverageFuelSurchargePerMileUsd,
						HighPerMile:   leg1RespRate.PerMile.HighUSD + leg1RespRate.AverageFuelSurchargePerMileUsd,
					},
				},
				{
					Order:          1,
					StartStopIndex: 1,
					EndStopIndex:   2,
					StartCityState: "Chicago, IL",
					EndCityState:   "Los Angeles, CA",
					DistanceMiles:  leg2RespRate.Mileage,
					Rates: RateValues{
						Target:        leg2RespRate.PerTrip.RateUSD + leg2RespRate.AverageFuelSurchargePerTripUsd,
						Low:           leg2RespRate.PerTrip.LowUSD + leg2RespRate.AverageFuelSurchargePerTripUsd,
						High:          leg2RespRate.PerTrip.HighUSD + leg2RespRate.AverageFuelSurchargePerTripUsd,
						TargetPerMile: leg2RespRate.PerMile.RateUSD + leg2RespRate.AverageFuelSurchargePerMileUsd,
						LowPerMile:    leg2RespRate.PerMile.LowUSD + leg2RespRate.AverageFuelSurchargePerMileUsd,
						HighPerMile:   leg2RespRate.PerMile.HighUSD + leg2RespRate.AverageFuelSurchargePerMileUsd,
					},
				},
			},
		},
	}

	t.Run("successful_multi_stop_quote", func(t *testing.T) {
		t.Parallel()
		ctx := context.Background()
		// Setup mocks
		mockHandlers := &MockQuickQuoteHandlers{}

		// Setup mock expectations for DAT lane rate calls
		mockHandlers.On(
			"GetDATLaneRate",
			mock.Anything,
			*mockIntegration,
			"<EMAIL>",
			mock.AnythingOfType("*[]dat.RateRequest"),
		).Return(leg1Response, nil).Once()

		mockHandlers.On(
			"GetDATLaneRate",
			mock.Anything,
			*mockIntegration,
			"<EMAIL>",
			mock.AnythingOfType("*[]dat.RateRequest"),
		).Return(leg2Response, nil).Once()

		// Mock Redis operations
		mockHandlers.On(
			"SetRedisMileDistanceBetweenLocations",
			mock.Anything,
			"New York",
			"NY",
			"Chicago",
			"IL",
			leg1RespRate.Mileage,
		).Return(nil).Once()
		mockHandlers.On(
			"SetRedisMileDistanceBetweenLocations",
			mock.Anything,
			"Chicago",
			"IL",
			"Los Angeles",
			"CA",
			leg2RespRate.Mileage,
		).Return(nil).Once()

		// Mock quote record creation
		expectedQuoteRecord := &models.QuickQuote{
			Model: gorm.Model{ID: 500},
		}
		mockHandlers.On(
			"CreateQuoteRecord",
			mock.Anything,
			*mockService,
			uint(100),
			uint(300),
			"test-thread-456",
			uint(400),
			mock.Anything,
			models.VanTransportType,
			body.PickupDate,
			body.DeliveryDate,
			mock.Anything,
			float64(0),
			float64(0),
			float64(0),
			models.DATSource,
			models.DATMultiStopLegToLeg,
			(*models.QuoteDATMetadata)(nil),
		).Return(func() *models.QuickQuote {
			// If needed for debugging, log the call
			return expectedQuoteRecord
		}()).Once()

		// Execute function
		result, err := handleDATMultiStopLegToLeg(
			ctx,
			mockService,
			mockUser,
			mockIntegration,
			body,
			stops,
			models.VanTransportType,
			mockHandlers,
		)

		// Assertions
		require.NoError(t, err)
		require.NotNil(t, result)
		assert.Equal(t, expectedQuote, *result)

		// Verify mock calls
		mockHandlers.AssertExpectations(t)

		// CreateQuoteRecord should be the last call
		createQuoteCall := mockHandlers.Calls[len(mockHandlers.Calls)-1]
		createQuoteRateData := createQuoteCall.Arguments[10].(quote.RateData)

		// Verify the RateData passed to CreateQuoteRecord
		expectedRateData := quote.RateData{
			TargetBuyRate: expectedTarget,
			LowBuyRate:    expectedLow,
			HighBuyRate:   expectedHigh,
			Distance:      result.Distance,
		}
		assert.Equal(t, expectedRateData, createQuoteRateData)
	})

	t.Run("dat_client_error", func(t *testing.T) {
		t.Parallel()
		mockHandlers := &MockQuickQuoteHandlers{}

		// Mock DAT client to return error
		mockHandlers.On(
			"GetDATLaneRate",
			mock.Anything,
			*mockIntegration,
			"<EMAIL>",
			mock.AnythingOfType("*[]dat.RateRequest"),
		).Return(nil, errors.New("DAT API error")).Once()

		// Execute function
		result, err := handleDATMultiStopLegToLeg(
			ctx,
			mockService,
			mockUser,
			mockIntegration,
			body,
			stops,
			models.VanTransportType,
			mockHandlers,
		)

		// Assertions
		require.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "failed to get quote for leg 0")

		mockHandlers.AssertExpectations(t)
		mockHandlers.AssertNotCalled(t, "CreateQuoteRecord")
	})

	// If there's a transient error inserting in DB, we should still fail-open and return the quote to FE
	t.Run("quote_record_creation_failure_fail_open", func(t *testing.T) {
		t.Parallel()
		mockHandlers := &MockQuickQuoteHandlers{}

		mockHandlers.On(
			"GetDATLaneRate",
			mock.Anything,
			*mockIntegration,
			"<EMAIL>",
			mock.AnythingOfType("*[]dat.RateRequest"),
		).Return(leg1Response, nil).Once()

		mockHandlers.On(
			"GetDATLaneRate",
			mock.Anything,
			*mockIntegration,
			"<EMAIL>",
			mock.AnythingOfType("*[]dat.RateRequest"),
		).Return(leg2Response, nil).Once()

		// Mock Redis operations
		mockHandlers.On(
			"SetRedisMileDistanceBetweenLocations",
			mock.Anything,
			"New York",
			"NY",
			"Chicago",
			"IL",
			leg1RespRate.Mileage,
		).Return(nil).Once()

		mockHandlers.On(
			"SetRedisMileDistanceBetweenLocations",
			mock.Anything,
			"Chicago",
			"IL",
			"Los Angeles",
			"CA",
			leg2RespRate.Mileage,
		).Return(nil).Once()

		// Mock quote record creation to return nil (simulating failure)
		mockHandlers.On(
			"CreateQuoteRecord",
			mock.Anything,
			*mockService,
			uint(100),
			uint(300),
			"test-thread-456",
			uint(400),
			mock.Anything,
			models.VanTransportType,
			body.PickupDate,
			body.DeliveryDate,
			mock.Anything,
			float64(0),
			float64(0),
			float64(0),
			models.DATSource,
			models.DATMultiStopLegToLeg,
			(*models.QuoteDATMetadata)(nil),
		).Return(nil).Once()

		// Execute function
		result, err := handleDATMultiStopLegToLeg(
			ctx,
			mockService,
			mockUser,
			mockIntegration,
			body,
			stops,
			models.VanTransportType,
			mockHandlers,
		)

		// Assertions
		expectedQuoteNoID := expectedQuote
		expectedQuoteNoID.ID = 0

		require.NoError(t, err)
		require.NotNil(t, result)
		assert.Equal(t, expectedQuoteNoID, *result)
		assert.Equal(t, uint(0), result.ID)

		mockHandlers.AssertExpectations(t)
	})

	t.Run("invalid_single_stop_route", func(t *testing.T) {
		t.Parallel()
		mockHandlers := &MockQuickQuoteHandlers{}

		// Test with only one stop (should not call DAT client)
		singleStop := []models.Stop{stops[0]}

		// Execute function
		result, err := handleDATMultiStopLegToLeg(
			ctx,
			mockService,
			mockUser,
			mockIntegration,
			body,
			singleStop,
			models.VanTransportType,
			mockHandlers,
		)

		// Assertions
		require.Error(t, err)
		assert.Contains(t, err.Error(), "invalid number of stops")
		require.Nil(t, result)

		// Verify no DAT client calls were made
		mockHandlers.AssertNotCalled(t, "GetDATLaneRate")
		mockHandlers.AssertNotCalled(t, "CreateQuoteRecord")
	})
}

func TestHandleDATMultiStopLongestLeg(t *testing.T) {
	ctx := context.Background()

	// Setup test data
	mockService := &models.Service{
		Model: gorm.Model{ID: 1},
		QuickQuoteConfig: &models.QuickQuoteConfig{
			IntermediateStopFeeUSDLow:    nil, // Use defaults
			IntermediateStopFeeUSDMedium: nil, // Use defaults
			IntermediateStopFeeUSDHigh:   nil, // Use defaults
		},
	}

	mockUser := &models.User{
		Model:           gorm.Model{ID: 100},
		DATEmailAddress: "<EMAIL>",
	}

	mockIntegration := &models.Integration{
		Model: gorm.Model{ID: 200},
	}

	body := &QuickQuotePrivateBody{
		LoadID:         "test-load-123",
		TransportType:  models.VanTransportType,
		EmailID:        300,
		ThreadID:       "test-thread-456",
		QuoteRequestID: 400,
		PickupDate:     time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC),
		DeliveryDate:   time.Date(2024, 1, 16, 0, 0, 0, 0, time.UTC),
	}

	stops := []models.Stop{
		{
			StopNumber: 0,
			Order:      0,
			Address: models.Address{
				City:  "New York",
				State: "NY",
				Zip:   "10001",
			},
		},
		{
			StopNumber: 1,
			Order:      1,
			Address: models.Address{
				City:  "Chicago",
				State: "IL",
				Zip:   "60601",
			},
		},
		{
			StopNumber: 2,
			Order:      2,
			Address: models.Address{
				City:  "Los Angeles",
				State: "CA",
				Zip:   "90001",
			},
		},
	}

	// Mock response for longest leg (first to last stop)
	longestLegResponse := &dat.GetLaneRateResponse{
		RateResponses: []dat.RateResponseItem{
			{
				Response: dat.RateResponse{
					Rate: dat.Rate{
						Mileage:                        2000.0,
						AverageFuelSurchargePerTripUsd: 300.0,
						AverageFuelSurchargePerMileUsd: 0.15,
						PerTrip: dat.PriceRange{
							RateUSD: 6000.0,
							LowUSD:  5500.0,
							HighUSD: 6500.0,
						},
						PerMile: dat.PriceRange{
							RateUSD: 3.0,
							LowUSD:  2.75,
							HighUSD: 3.25,
						},
						Reports:   15,
						Companies: 8,
					},
					Escalation: dat.Escalation{
						Timeframe: dat.SevenDays,
						Origin: dat.Location{
							Name: "New York, NY",
							Type: dat.State,
						},
						Destination: dat.Location{
							Name: "Los Angeles, CA",
							Type: dat.MarketArea,
						},
					},
				},
			},
		},
	}

	rateResp := longestLegResponse.RateResponses[0].Response.Rate

	// Define expected values
	expectedDistance := 2000.0               // Total distance from leg-to-leg calculation
	countIntermediateStops := len(stops) - 2 // 1 intermediate stop

	// Base rates from DAT response
	baseTarget := rateResp.PerTrip.RateUSD + rateResp.AverageFuelSurchargePerTripUsd
	baseLow := rateResp.PerTrip.LowUSD + rateResp.AverageFuelSurchargePerTripUsd
	baseHigh := rateResp.PerTrip.HighUSD + rateResp.AverageFuelSurchargePerTripUsd

	// Expected rates, including intermediate stop fees
	expectedTarget := baseTarget + float64(countIntermediateStops*IntermediateStopFeeUSDMedium)
	expectedLow := baseLow + float64(countIntermediateStops*IntermediateStopFeeUSDLow)
	expectedHigh := baseHigh + float64(countIntermediateStops*IntermediateStopFeeUSDHigh)

	expectedTargetPerMile := expectedTarget / rateResp.Mileage
	expectedLowPerMile := expectedLow / rateResp.Mileage
	expectedHighPerMile := expectedHigh / rateResp.Mileage

	// Metadata
	expectedReports := rateResp.Reports
	expectedCompanies := rateResp.Companies
	expectedFuelSurchargePerMile := rateResp.AverageFuelSurchargePerMileUsd

	// Verify the returned Quote structure
	expectedQuote := Quote{
		ID:     uint(500),
		Source: models.DATSource,
		Type:   models.DATMultiStopLongestLeg,
		Rates: RateValues{
			Target:        expectedTarget,
			Low:           expectedLow,
			High:          expectedHigh,
			TargetPerMile: expectedTargetPerMile,
			LowPerMile:    expectedLowPerMile,
			HighPerMile:   expectedHighPerMile,
		},
		Distance: expectedDistance,
		Metadata: map[string]any{
			"reports":              expectedReports,
			"companies":            expectedCompanies,
			"timeframe":            dat.SevenDays,
			"originName":           "New York, NY",
			"originType":           dat.State,
			"destinationName":      "Los Angeles, CA",
			"destinationType":      dat.MarketArea,
			"fuelSurchargePerMile": expectedFuelSurchargePerMile,
			"stopFeeUSDLow":        IntermediateStopFeeUSDLow,
			"stopFeeUSDMedium":     IntermediateStopFeeUSDMedium,
			"stopFeeUSDHigh":       IntermediateStopFeeUSDHigh,
		},
	}

	t.Run("successful_longest_leg_quote", func(t *testing.T) {
		t.Parallel()
		ctx := context.Background()
		// Setup mocks
		mockHandlers := &MockQuickQuoteHandlers{}

		// Setup mock expectations for DAT lane rate call
		mockHandlers.On(
			"GetDATLaneRate",
			mock.Anything,
			*mockIntegration,
			"<EMAIL>",
			mock.AnythingOfType("*[]dat.RateRequest"),
		).Return(longestLegResponse, nil).Once()

		// Mock Redis operations
		mockHandlers.On(
			"SetRedisMileDistanceBetweenLocations",
			mock.Anything,
			"New York",
			"NY",
			"Los Angeles",
			"CA",
			rateResp.Mileage,
		).Return(nil).Once()

		// Mock quote record creation
		expectedQuoteRecord := &models.QuickQuote{
			Model: gorm.Model{ID: 500},
		}
		mockHandlers.On(
			"CreateQuoteRecord",
			mock.Anything,
			*mockService,
			uint(100),
			uint(300),
			"test-thread-456",
			uint(400),
			mock.Anything,
			models.VanTransportType,
			body.PickupDate,
			body.DeliveryDate,
			mock.Anything,
			float64(0),
			float64(0),
			float64(0),
			models.DATSource,
			models.DATMultiStopLongestLeg,
			(*models.QuoteDATMetadata)(nil),
		).Return(func() *models.QuickQuote {
			// If needed for debugging, log the call
			return expectedQuoteRecord
		}()).Once()

		// Execute function
		result, err := handleDATMultiStopLongestLeg(
			ctx,
			mockService,
			mockUser,
			mockIntegration,
			body,
			stops,
			models.VanTransportType,
			expectedDistance,
			mockHandlers,
		)

		// Assertions
		require.NoError(t, err)
		require.NotNil(t, result)

		assert.Equal(t, expectedQuote, *result)

		// Verify mock calls
		mockHandlers.AssertExpectations(t)

		// Verify the CreateQuoteRecord was called with correct parameters
		createQuoteCalls := mockHandlers.Calls
		require.Len(t, createQuoteCalls, 3)

		createQuoteCall := createQuoteCalls[len(createQuoteCalls)-1]
		createQuoteRateData := createQuoteCall.Arguments[10].(quote.RateData)

		// Verify the RateData passed to CreateQuoteRecord
		expectedRateData := quote.RateData{
			TargetBuyRate: expectedTarget,
			LowBuyRate:    expectedLow,
			HighBuyRate:   expectedHigh,
			Distance:      rateResp.Mileage,
		}
		assert.Equal(t, expectedRateData, createQuoteRateData)
	})

	t.Run("dat_client_error", func(t *testing.T) {
		t.Parallel()
		mockHandlers := &MockQuickQuoteHandlers{}

		// Mock DAT client to return error
		mockHandlers.On(
			"GetDATLaneRate",
			mock.Anything,
			*mockIntegration,
			"<EMAIL>",
			mock.AnythingOfType("*[]dat.RateRequest"),
		).Return(nil, errors.New("DAT API error")).Once()

		// Execute function
		result, err := handleDATMultiStopLongestLeg(
			ctx,
			mockService,
			mockUser,
			mockIntegration,
			body,
			stops,
			models.VanTransportType,
			expectedDistance,
			mockHandlers,
		)

		// Assertions
		require.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "failed to get DAT longest leg quote")

		mockHandlers.AssertExpectations(t)
		mockHandlers.AssertNotCalled(t, "CreateQuoteRecord")
	})

	// If there's a transient error inserting in DB, we should still fail-open and return the quote to FE
	t.Run("quote_record_creation_failure_fail_open", func(t *testing.T) {
		t.Parallel()
		mockHandlers := &MockQuickQuoteHandlers{}

		mockHandlers.On(
			"GetDATLaneRate",
			mock.Anything,
			*mockIntegration,
			"<EMAIL>",
			mock.AnythingOfType("*[]dat.RateRequest"),
		).Return(longestLegResponse, nil).Once()

		// Mock Redis operations
		mockHandlers.On(
			"SetRedisMileDistanceBetweenLocations",
			mock.Anything,
			"New York",
			"NY",
			"Los Angeles",
			"CA",
			rateResp.Mileage,
		).Return(nil).Once()

		// Mock quote record creation to return nil (simulating failure)
		mockHandlers.On(
			"CreateQuoteRecord",
			mock.Anything,
			*mockService,
			uint(100),
			uint(300),
			"test-thread-456",
			uint(400),
			mock.Anything,
			models.VanTransportType,
			body.PickupDate,
			body.DeliveryDate,
			mock.Anything,
			float64(0),
			float64(0),
			float64(0),
			models.DATSource,
			models.DATMultiStopLongestLeg,
			(*models.QuoteDATMetadata)(nil),
		).Return(nil).Once()

		// Execute function
		result, err := handleDATMultiStopLongestLeg(
			ctx,
			mockService,
			mockUser,
			mockIntegration,
			body,
			stops,
			models.VanTransportType,
			expectedDistance,
			mockHandlers,
		)

		// Assertions
		expectedQuoteNoID := expectedQuote
		expectedQuoteNoID.ID = 0

		require.NoError(t, err)
		require.NotNil(t, result)
		assert.Equal(t, expectedQuoteNoID, *result)
		assert.Equal(t, uint(0), result.ID)

		mockHandlers.AssertExpectations(t)
	})

	t.Run("custom_service_config_stop_fees", func(t *testing.T) {
		t.Parallel()
		ctx := context.Background()

		// Service with custom stop fees
		lowFee := 75
		mediumFee := 150
		highFee := 225
		customService := &models.Service{
			Model: gorm.Model{ID: 1},
			QuickQuoteConfig: &models.QuickQuoteConfig{
				IntermediateStopFeeUSDLow:    &lowFee,
				IntermediateStopFeeUSDMedium: &mediumFee,
				IntermediateStopFeeUSDHigh:   &highFee,
			},
		}

		mockHandlers := &MockQuickQuoteHandlers{}

		mockHandlers.On(
			"GetDATLaneRate",
			mock.Anything,
			*mockIntegration,
			"<EMAIL>",
			mock.AnythingOfType("*[]dat.RateRequest"),
		).Return(longestLegResponse, nil).Once()

		// Mock Redis operations
		mockHandlers.On(
			"SetRedisMileDistanceBetweenLocations",
			mock.Anything,
			"New York",
			"NY",
			"Los Angeles",
			"CA",
			rateResp.Mileage,
		).Return(nil).Once()

		// Mock quote record creation
		expectedQuoteRecord := &models.QuickQuote{
			Model: gorm.Model{ID: 500},
		}
		mockHandlers.On(
			"CreateQuoteRecord",
			mock.Anything,
			*customService,
			uint(100),
			uint(300),
			"test-thread-456",
			uint(400),
			mock.Anything,
			models.VanTransportType,
			body.PickupDate,
			body.DeliveryDate,
			mock.Anything,
			float64(0),
			float64(0),
			float64(0),
			models.DATSource,
			models.DATMultiStopLongestLeg,
			(*models.QuoteDATMetadata)(nil),
		).Return(func() *models.QuickQuote {
			return expectedQuoteRecord
		}()).Once()

		// Calculate expected values with custom fees
		customBaseTarget := rateResp.PerTrip.RateUSD + rateResp.AverageFuelSurchargePerTripUsd
		customBaseLow := rateResp.PerTrip.LowUSD + rateResp.AverageFuelSurchargePerTripUsd
		customBaseHigh := rateResp.PerTrip.HighUSD + rateResp.AverageFuelSurchargePerTripUsd

		customExpectedTarget := customBaseTarget + float64(countIntermediateStops*150.0)
		customExpectedLow := customBaseLow + float64(countIntermediateStops*75.0)
		customExpectedHigh := customBaseHigh + float64(countIntermediateStops*225.0)

		customExpectedTargetPerMile := customExpectedTarget / rateResp.Mileage
		customExpectedLowPerMile := customExpectedLow / rateResp.Mileage
		customExpectedHighPerMile := customExpectedHigh / rateResp.Mileage

		// Execute function
		result, err := handleDATMultiStopLongestLeg(
			ctx,
			customService,
			mockUser,
			mockIntegration,
			body,
			stops,
			models.VanTransportType,
			expectedDistance,
			mockHandlers,
		)

		// Assertions
		require.NoError(t, err)
		require.NotNil(t, result)

		// Create expected quote with custom fees
		expectedCustomQuote := Quote{
			ID:     uint(500),
			Source: models.DATSource,
			Type:   models.DATMultiStopLongestLeg,
			Rates: RateValues{
				Target:        customExpectedTarget,
				Low:           customExpectedLow,
				High:          customExpectedHigh,
				TargetPerMile: customExpectedTargetPerMile,
				LowPerMile:    customExpectedLowPerMile,
				HighPerMile:   customExpectedHighPerMile,
			},
			Distance: expectedDistance,
			Metadata: map[string]any{
				"reports":              expectedReports,
				"companies":            expectedCompanies,
				"timeframe":            dat.SevenDays,
				"originName":           "New York, NY",
				"originType":           dat.State,
				"destinationName":      "Los Angeles, CA",
				"destinationType":      dat.MarketArea,
				"fuelSurchargePerMile": expectedFuelSurchargePerMile,
				"stopFeeUSDLow":        75,
				"stopFeeUSDMedium":     150,
				"stopFeeUSDHigh":       225,
			},
		}

		assert.Equal(t, expectedCustomQuote, *result)

		// Verify mock calls
		mockHandlers.AssertExpectations(t)

		// Verify the CreateQuoteRecord was called with correct parameters
		createQuoteCalls := mockHandlers.Calls
		require.Len(t, createQuoteCalls, 3)

		createQuoteCall := createQuoteCalls[len(createQuoteCalls)-1]
		createQuoteRateData := createQuoteCall.Arguments[10].(quote.RateData)

		// Verify the RateData passed to CreateQuoteRecord
		expectedCustomRateData := quote.RateData{
			TargetBuyRate: customExpectedTarget,
			LowBuyRate:    customExpectedLow,
			HighBuyRate:   customExpectedHigh,
			Distance:      rateResp.Mileage,
		}
		assert.Equal(t, expectedCustomRateData, createQuoteRateData)
	})
}
