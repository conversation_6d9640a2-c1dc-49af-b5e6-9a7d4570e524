package appt

import (
	"context"
	"fmt"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/integrations/scheduling"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	warehouseDB "github.com/drumkitai/drumkit/common/rds/warehouses"
)

type (
	ValidateApptBody struct {
		PONumbers       []string               `json:"poNumbers,omitempty"`
		Source          models.WarehouseSource `json:"source" validate:"required"`
		WarehouseID     string                 `json:"warehouseId"`
		RequestType     models.RequestType     `json:"requestType"`
		AppointmentType string                 `json:"appointmentType"`
		Company         string                 `json:"company"`
		Operation       string                 `json:"operation"`
		City            string                 `json:"city"`
		State           string                 `json:"state"`
		ZipCode         string                 `json:"zipCode"`
		Country         string                 `json:"country"`
	}

	ValidateApptResponse struct {
		ValidatedPONumbers []models.ValidatedPONumber `json:"validatedPONumbers"`
	}
)

// NOTE: Only supports E2open, Manhattan and Retalix now
func ValidateAppt(c *fiber.Ctx) error {
	var body ValidateApptBody
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.Any("requestBody", body))
	userServiceID := middleware.ServiceIDFromContext(c)
	userID := middleware.UserIDFromContext(c)

	integration, err := integrationDB.GetSchedulerByServiceUserIDsAndName(
		ctx,
		userID,
		userServiceID,
		string(body.Source),
	)
	if err != nil {
		log.Error(ctx, "failed to get scheduler integration", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	var warehouse *models.Warehouse

	if body.Source == models.RetalixSource {
		warehouse, err = warehouseDB.GetWarehouseByIDAndSource(
			ctx,
			userServiceID,
			body.Source,
			body.WarehouseID,
		)
		if err != nil {
			log.Error(
				ctx,
				"error getting warehouse name",
				zap.Error(err),
				zap.String("warehouseID", body.WarehouseID),
			)
			return c.SendStatus(http.StatusInternalServerError)
		}
	}

	var validatedAppt ValidateApptResponse
	if len(body.PONumbers) > 0 {
		validatedAppt.ValidatedPONumbers, err = validatePONumbers(
			ctx,
			integration,
			body,
			warehouse,
		)
		if err != nil {
			log.Error(ctx, "failed to validate PO numbers", zap.Error(err))
			return err
		}
	} else {
		log.WarnNoSentry(ctx, "empty list of PO numbers, skipping validation")
	}

	return c.Status(http.StatusOK).JSON(validatedAppt)
}

func validatePONumbers(
	ctx context.Context,
	integration models.Integration,
	body ValidateApptBody,
	warehouse *models.Warehouse,
) (validatedPOs []models.ValidatedPONumber, err error) {

	requestType := body.RequestType
	poNumbers := body.PONumbers

	client, err := scheduling.GetCachedClient(ctx, integration)
	if err != nil {
		if body.Source == models.RetalixSource {
			log.Warn(ctx, "failed to build scheduling client", zap.Error(err))
		} else {
			log.Error(ctx, "failed to build scheduling client", zap.Error(err))
		}

		return nil, fmt.Errorf("failed to build scheduling client: %w", err)
	}

	var wh models.Warehouse
	if warehouse != nil {
		wh = *warehouse
	}

	var opts []models.SchedulingOption

	if body.AppointmentType != "" {
		opts = append(opts, models.WithAppointmentType(body.AppointmentType))
	}

	if requestType != "" {
		opts = append(opts, models.WithRequestType(requestType))
	}

	if body.Company != "" {
		opts = append(opts, models.WithCompany(body.Company))
	}

	if body.Operation != "" {
		opts = append(opts, models.WithOperation(body.Operation))
	}

	if body.City != "" {
		opts = append(opts, models.WithCity(body.City))
	}

	if body.State != "" {
		opts = append(opts, models.WithState(body.State))
	}

	if body.ZipCode != "" {
		opts = append(opts, models.WithZipCode(body.ZipCode))
	}

	if body.Country != "" {
		opts = append(opts, models.WithCountry(body.Country))
	}

	validatedPOs, err = client.ValidateAppointment(ctx, poNumbers, wh, opts...)
	if err != nil {
		return nil, fmt.Errorf("error validating PO numbers: %w", err)
	}

	return validatedPOs, nil
}
