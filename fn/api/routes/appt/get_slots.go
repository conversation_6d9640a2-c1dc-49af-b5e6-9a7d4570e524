package appt

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/integrations/scheduling"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	warehouseDB "github.com/drumkitai/drumkit/common/rds/warehouses"
	"github.com/drumkitai/drumkit/common/redis"
)

type (
	GetOpenSlotsQuery struct {
		FreightTrackingID string `json:"freightTrackingID"`
		LoadTypeID        string `json:"loadTypeID" validate:"required"`
		TrailerType       string `json:"trailerType"`
		DockID            string `json:"dockID"`
		models.GetOpenSlotsRequest
		Source string `json:"source"` // TODO: limit type to scheduling integrations
	}

	GetOpenSlotsResponse struct {
		GetOpenSlotsQuery
		Warehouse models.Warehouse `json:"warehouse"`
		LoadType  models.LoadType  `json:"loadType"`
		Slots     []models.Slot    `json:"slots"`
	}
)

func GetOpenSlots(c *fiber.Ctx) error {
	var query GetOpenSlotsQuery
	if err := api.Parse(c, nil, &query, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.Any("requestQuery", query))

	// TODO: remove this when the frontend passes in a nonempty source
	if query.Source == "" {
		query.Source = string(models.OpendockSource)
	}

	userID := middleware.UserIDFromContext(c)
	userServiceID := middleware.ServiceIDFromContext(c)

	integration, err := integrationDB.GetSchedulerByServiceUserIDsAndName(ctx, userID, userServiceID, query.Source)
	if err != nil {
		log.Error(ctx, "error fetching integration id for warehouse", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	ctx = log.With(
		ctx,
		zap.String("schedulingUsername", integration.Username),
		zap.Uint("integrationID", integration.ID),
	)

	resp, err := getSlots(ctx, &query, query.WarehouseID, integration)
	if err != nil {
		errStr := strings.ToLower(err.Error())

		isNotFound := strings.Contains(errStr, "not found") || strings.Contains(errStr, "404")
		if isNotFound {
			log.Warn(ctx, "getSlots failed - warehouse not found", zap.String("err", errStr))
			return c.Status(http.StatusNotFound).SendString(errStr)
		}

		if strings.Contains(errStr, "not supported") {
			log.Warn(ctx, "getSlots failed", zap.String("err", errStr))
			return c.Status(http.StatusUnprocessableEntity).SendString(errStr)
		}

		if strings.Contains(errStr, "no available appointment dates found") {
			log.WarnNoSentry(
				ctx,
				"getSlots failed - no available appointment dates found",
				zap.String("err", errStr),
			)
			return c.Status(http.StatusUnprocessableEntity).SendString(errStr)
		}

		log.Error(ctx, "getSlots failed", zap.Error(err))

		var cyclopsErr *models.CyclopsError
		if errors.As(err, &cyclopsErr) {
			return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
				"message": cyclopsErr.Message,
				"errors":  cyclopsErr.Errors,
			})
		}

		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
			"message": "Live appointments are not available at this time",
		})
	}

	// Handle warehouse address associations creation
	if err := handleWarehouseAddressAssociations(ctx, resp, &query, integration); err != nil {
		log.Warn(ctx, "failed to handle warehouse address associations", zap.Error(err))
	}

	return c.Status(http.StatusOK).JSON(resp)
}

func getSlots(
	ctx context.Context,
	query *GetOpenSlotsQuery,
	warehouseID string,
	integration models.Integration,
) (*GetOpenSlotsResponse, error) {

	client, err := scheduling.GetCachedClient(ctx, integration)
	if err != nil {
		return nil, fmt.Errorf("unable to connect to %s: %w", query.Source, err)
	}

	var warehouse models.Warehouse
	var loadType models.LoadType
	var slots []models.Slot

	switch integration.Name {
	case models.E2open:
		if query.FreightTrackingID == "" {
			return nil, errors.New("PRO ID is required for E2open")
		}

		slots, err = client.GetOpenSlots(ctx, query.FreightTrackingID, query.GetOpenSlotsRequest)
		if err != nil {
			return nil, fmt.Errorf("%s.GetOpenSlots failed: %w", integration.Name, err)
		}

		key := fmt.Sprintf("%s-%s-%s", models.E2openSource, query.FreightTrackingID, query.RequestType)

		whName, ok, err := redis.GetKey[string](ctx, key)
		if err == nil && ok && whName != "" {
			warehouse = models.Warehouse{
				WarehouseID:   whName,
				WarehouseName: whName,
				Source:        models.E2openSource,
			}
		} else {
			log.WarnNoSentry(
				ctx,
				"error getting warehouse in redis - falling back to db",
				zap.String("source", string(models.E2openSource)),
				zap.String("freightTrackingID", query.FreightTrackingID),
				zap.Error(err),
			)

			// NOTE: E2open warehouses have no IDs so we use freightTrackingID for storing and lookups
			wh, err := warehouseDB.GetWarehouseByIDAndSource(
				ctx,
				integration.ServiceID,
				models.E2openSource,
				query.FreightTrackingID,
			)
			if err != nil {
				return nil, fmt.Errorf("warehouseDB.GetWarehouseByIDAndSource failed: %w", err)
			}

			if err := redis.SetKey(ctx, key, wh.WarehouseName, 24*time.Hour); err != nil {
				log.WarnNoSentry(
					ctx,
					"error setting warehouse in redis",
					zap.String("source", string(models.E2openSource)),
					zap.String("freightTrackingID", query.FreightTrackingID),
					zap.String("name", wh.WarehouseName),
					zap.Error(err),
				)
			}
		}

	case models.Manhattan:
		slots, err = client.GetOpenSlots(ctx, query.LoadTypeID, query.GetOpenSlotsRequest)
		if err != nil {
			return nil, fmt.Errorf("%s.GetOpenSlots failed: %w", integration.Name, err)
		}

		// Check if slots are empty before accessing the first element
		if len(slots) == 0 {
			return nil, errors.New("no available slots found")
		}

		key := fmt.Sprintf("%s-%s", models.ManhattanSource, slots[0].Warehouse.WarehouseID)

		whName, ok, err := redis.GetKey[string](ctx, key)
		if err == nil && ok && whName != "" {
			warehouse = models.Warehouse{
				WarehouseID:   whName,
				WarehouseName: whName,
				Source:        models.ManhattanSource,
			}
		} else {
			log.WarnNoSentry(
				ctx,
				"error getting warehouse in redis - falling back to db",
				zap.String("source", string(models.ManhattanSource)),
				zap.Error(err),
			)

			// NOTE: E2open warehouses have no IDs so we use freightTrackingID for storing and lookups
			wh, err := warehouseDB.GetWarehouseByIDAndSource(
				ctx,
				integration.ServiceID,
				models.ManhattanSource,
				slots[0].Warehouse.WarehouseID,
			)
			if err != nil {
				return nil, fmt.Errorf("warehouseDB.GetWarehouseByIDAndSource failed: %w", err)
			}

			if err := redis.SetKey(ctx, key, wh.WarehouseName, 24*time.Hour); err != nil {
				log.WarnNoSentry(
					ctx,
					"error setting warehouse in redis",
					zap.String("source", string(models.ManhattanSource)),
					zap.String("freightTrackingID", query.FreightTrackingID),
					zap.String("name", wh.WarehouseName),
					zap.Error(err),
				)
			}
		}

	case models.OneNetwork:
		slots, err = client.GetOpenSlots(ctx, query.LoadTypeID, query.GetOpenSlotsRequest)
		if err != nil {
			return nil, fmt.Errorf("%s.GetOpenSlots failed: %w", integration.Name, err)
		}

		key := fmt.Sprintf("%s-%s-%s", models.OneNetworkSource, query.LoadTypeID, query.RequestType)

		warehouse, _, err = redis.GetKey[models.Warehouse](ctx, key)
		if err != nil {
			log.WarnNoSentry(
				ctx,
				"error getting warehouse in redis",
				zap.String("source", string(models.OneNetworkSource)),
				zap.String("poNum", query.LoadTypeID),
				zap.Error(err),
			)
		}

	case models.Opendock:
		warehouse, err = client.GetWarehouse(ctx, warehouseID)
		if err != nil {
			return nil, fmt.Errorf("%s.GetWarehouse failed: %w", query.Source, err)
		}

		loadTypes, err := client.GetLoadTypes(ctx, models.GetLoadTypesRequest{
			WarehouseID: warehouseID,
			Search:      `{"allowCarrierScheduling":true}`,
		})
		if err != nil {
			return nil, fmt.Errorf("%s.GetLoadTypes failed: %w", query.Source, err)
		}

		// Find the loadType with the matching name
		for _, lt := range loadTypes {
			if strings.EqualFold(lt.ID, query.LoadTypeID) {
				loadType = lt
				break
			}
		}

		if loadType.ID == "" {
			return nil, fmt.Errorf("loadType '%s' not found for warehouse %s", query.LoadTypeID, warehouseID)
		}

		query.IncludeStartTimes = true
		log.Info(ctx, "get open slots request object", zap.Any("query", query.GetOpenSlotsRequest))

		slots, err = client.GetOpenSlots(ctx, loadType.ID, query.GetOpenSlotsRequest)
		if err != nil {
			return nil, fmt.Errorf("%s.GetOpenSlots failed: %w", integration.Name, err)
		}

	case models.Retalix:
		// Validate Retalix-specific requirements
		if len(query.PONumbers) == 0 {
			return nil, errors.New("PO numbers are required for Retalix")
		}

		if query.Warehouse.WarehouseID == "" {
			return nil, errors.New("warehouse ID is required for Retalix")
		}

		if query.Warehouse.WarehouseName == "" {
			return nil, errors.New("warehouse name is required for Retalix")
		}

		// Retalix warehouses are available in the database after onboarding flow
		wh, err := warehouseDB.GetWarehouseByIDAndSource(
			ctx,
			integration.ServiceID,
			models.RetalixSource,
			query.Warehouse.WarehouseID,
		)
		if err != nil {
			return nil, fmt.Errorf("warehouseDB.GetWarehouseByIDAndSource failed: %w", err)
		}

		warehouse = *wh

		// Retalix doesn't need warehouse/loadtype calls
		slots, err = client.GetOpenSlots(ctx, "", query.GetOpenSlotsRequest)
		if err != nil {
			return nil, fmt.Errorf("%s.GetOpenSlots failed: %w", integration.Name, err)
		}

	case models.YardView:
		slots, err = client.GetOpenSlots(ctx, query.LoadTypeID, query.GetOpenSlotsRequest)
		if err != nil {
			return nil, fmt.Errorf("%s.GetOpenSlots failed: %w", integration.Name, err)
		}

	default:
		return nil, fmt.Errorf("unsupported integration: %s", integration.Name)
	}

	// Filter slots by dock if specified
	excludedIntegrations := map[models.IntegrationName]bool{
		models.E2open:     true,
		models.OneNetwork: true,
		models.YardView:   true,
	}

	slotsResponse := make([]models.Slot, 0)
	if query.DockID != "" && !excludedIntegrations[integration.Name] {
		for _, s := range slots {
			if s.Dock.ID == query.DockID {
				slotsResponse = append(slotsResponse, s)
			}
		}
	} else {
		slotsResponse = slots
	}

	// Ensure warehouse info is set on all slots
	for i := range slotsResponse {
		slotsResponse[i].Warehouse = warehouse
	}

	return &GetOpenSlotsResponse{
		GetOpenSlotsQuery: *query,
		Warehouse:         warehouse,
		LoadType:          loadType,
		Slots:             slotsResponse,
	}, nil
}

// handleWarehouseAddressAssociations creates warehouse address associations if needed
func handleWarehouseAddressAssociations(
	ctx context.Context,
	resp *GetOpenSlotsResponse,
	query *GetOpenSlotsQuery,
	integration models.Integration,
) error {
	// Only process if we have address information in the request
	if query.City == "" && query.State == "" && query.ZipCode == "" {
		return nil // No address information to process
	}

	warehouseID := query.WarehouseID
	if warehouseID == "" {
		warehouseID = resp.FreightTrackingID
	}

	// Check if warehouse exists in database
	existingWarehouse, err := warehouseDB.GetWarehouseByIDAndSource(
		ctx,
		integration.ServiceID,
		models.WarehouseSource(integration.Name),
		warehouseID,
	)

	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return fmt.Errorf("failed to check existing warehouse: %w", err)
	}

	// Create address model from request
	// Extract address lines from query if available, otherwise use empty strings
	addressLine1 := ""
	addressLine2 := ""
	if query.Warehouse != nil {
		addressLine1 = query.Warehouse.WarehouseAddressLine1
		addressLine2 = query.Warehouse.WarehouseAddressLine2
	}

	address := models.WarehouseAddressReference{
		AddressLine1: addressLine1,
		AddressLine2: addressLine2,
		City:         query.City,
		State:        query.State,
		Zip:          query.ZipCode,
		Country:      query.Country,
	}

	if existingWarehouse != nil && existingWarehouse.ID != 0 {
		// Warehouse exists - check if it has address associations
		hasAssociation, err := warehouseDB.CheckWarehouseAddressAssociation(ctx, existingWarehouse.ID)
		if err != nil {
			return fmt.Errorf("failed to check warehouse address association: %w", err)
		}

		if !hasAssociation {
			// Check if the exact same address already exists
			existingAddress, err := warehouseDB.FindExistingWarehouseAddress(
				ctx,
				addressLine1,
				addressLine2,
				query.City,
				query.State,
				query.ZipCode,
			)
			if err != nil {
				return fmt.Errorf("failed to find existing warehouse address: %w", err)
			}

			if existingAddress != nil && existingAddress.ID != 0 {
				// Address exists - create association with existing address
				err = warehouseDB.AssociateExistingWarehouseAddress(ctx, existingWarehouse.ID, existingAddress.ID)
				if err != nil {
					return fmt.Errorf("failed to associate existing warehouse address: %w", err)
				}

				log.Info(ctx, "associated existing warehouse address with warehouse",
					zap.String("warehouseID", existingWarehouse.WarehouseID),
					zap.Uint("addressID", existingAddress.ID))
			} else {
				// Address doesn't exist - create new address and association
				err = warehouseDB.CreateWarehouseAddressFromAddress(ctx, existingWarehouse.ID, address)
				if err != nil {
					return fmt.Errorf("failed to create warehouse address association: %w", err)
				}

				log.Info(ctx, "created new warehouse address association for existing warehouse",
					zap.String("warehouseID", existingWarehouse.WarehouseID),
					zap.String("source", string(existingWarehouse.Source)))
			}
		}
	} else {
		// Warehouse doesn't exist - check if address exists first
		existingAddress, err := warehouseDB.FindExistingWarehouseAddress(
			ctx,
			addressLine1,
			addressLine2,
			query.City,
			query.State,
			query.ZipCode,
		)
		if err != nil {
			return fmt.Errorf("failed to find existing warehouse address: %w", err)
		}

		// Create warehouse address lines for display purposes
		warehouseAddressLine1 := addressLine1
		if warehouseAddressLine1 == "" {
			warehouseAddressLine1 = fmt.Sprintf("%s, %s", query.City, query.State)
			if query.ZipCode != "" {
				warehouseAddressLine1 = fmt.Sprintf("%s, %s %s", query.City, query.State, query.ZipCode)
			}
		}

		if existingAddress != nil && existingAddress.ID != 0 {

			warehouseFullIdentifier := fmt.Sprintf("%s %s %s",
				resp.Warehouse.WarehouseName,
				warehouseAddressLine1,
				addressLine2,
			)

			// Address exists - create warehouse and associate with existing address
			warehouse := &models.Warehouse{
				WarehouseID:             warehouseID,
				WarehouseName:           resp.Warehouse.WarehouseName,
				WarehouseAddressLine1:   warehouseAddressLine1,
				WarehouseAddressLine2:   addressLine2,
				WarehouseFullAddress:    fmt.Sprintf("%s %s", warehouseAddressLine1, addressLine2),
				WarehouseFullIdentifier: warehouseFullIdentifier,
				Source:                  models.WarehouseSource(integration.Name),
			}

			// Create warehouse first
			err = rds.WithContext(ctx).Create(warehouse).Error
			if err != nil {
				return fmt.Errorf("failed to create warehouse: %w", err)
			}

			// Associate with existing address
			err = warehouseDB.AssociateExistingWarehouseAddress(ctx, warehouse.ID, existingAddress.ID)
			if err != nil {
				return fmt.Errorf("failed to associate existing address with new warehouse: %w", err)
			}

			log.Info(ctx, "created new warehouse and associated with existing address",
				zap.String("warehouseID", warehouse.WarehouseID),
				zap.Uint("addressID", existingAddress.ID))
		} else {
			// Address doesn't exist - create new warehouse with new address
			newWarehouse, err := warehouseDB.CreateWarehouseWithAddress(
				ctx,
				warehouseID,
				resp.Warehouse.WarehouseName,
				warehouseAddressLine1,
				addressLine2,
				models.WarehouseSource(integration.Name),
				address,
			)
			if err != nil {
				return fmt.Errorf("failed to create warehouse with address: %w", err)
			}

			log.Info(ctx, "created new warehouse with new address association",
				zap.String("warehouseID", newWarehouse.WarehouseID),
				zap.String("source", string(newWarehouse.Source)))
		}
	}

	return nil
}
