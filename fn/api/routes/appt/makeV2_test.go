package appt

import (
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"github.com/drumkitai/drumkit/common/models"
	apitesting "github.com/drumkitai/drumkit/fn/api/testing"
)

func TestMakeV2(t *testing.T) {
	validator := apitesting.NewValidationTest(t)

	t.Run("valid request body", func(t *testing.T) {
		body := BodyV2{
			LoadID:            1,
			FreightTrackingID: "12345",
			DryRun:            false,
			StopType:          "pickup",
			StartTime:         time.Now(),
			WarehouseID:       "warehouse-1",
			WarehouseTimezone: "America/New_York",
			DockID:            "dock-1",
			LoadTypeID:        "test-load",
			SubscribedEmail:   "<EMAIL>",
			CcEmails: []string{
				"<EMAIL>",
			},
			TrailerType: "Cargo Van",
			Notes:       "this is a note",
			PONums:      "PRO 2080005",
			RefNumber:   "",
			CustomApptFieldsTemplate: []models.CustomApptField{
				{
					Name:                 "",
					Type:                 "",
					Label:                "",
					Value:                "",
					Description:          "",
					Placeholder:          "",
					DropDownValues:       []string{},
					HiddenFromCarrier:    false,
					RequiredForCarrier:   false,
					RequiredForCheckIn:   false,
					RequiredForWarehouse: false,
					MaxLengthOrValue:     0,
					MinLengthOrValue:     0,
				},
			},
		}

		if err := validator.TestBody(body); err != nil {
			t.Errorf("Expected no error, got: %v", err)
		}
	})

	t.Run("missing required LoadID", func(t *testing.T) {
		body := BodyV2{
			FreightTrackingID: "12345",
			DryRun:            false,
			StopType:          "pickup",
			StartTime:         time.Now(),
			WarehouseID:       "warehouse-1",
			WarehouseTimezone: "America/New_York",
			DockID:            "dock-1",
			LoadTypeID:        "test-load",
			SubscribedEmail:   "<EMAIL>",
			CcEmails: []string{
				"<EMAIL>",
			},
			TrailerType: "Cargo Van",
			Notes:       "this is a note",
			PONums:      "PRO 2080005",
			RefNumber:   "",
			CustomApptFieldsTemplate: []models.CustomApptField{
				{
					Name:                 "",
					Type:                 "",
					Label:                "",
					Value:                "",
					Description:          "",
					Placeholder:          "",
					DropDownValues:       []string{},
					HiddenFromCarrier:    false,
					RequiredForCarrier:   false,
					RequiredForCheckIn:   false,
					RequiredForWarehouse: false,
					MaxLengthOrValue:     0,
					MinLengthOrValue:     0,
				},
			},
		}

		err := validator.TestBody(body)
		if err == nil {
			t.Error("Expected error for missing LoadID, got none")
			return
		}

		var validationErr *apitesting.ValidationError
		if !errors.As(err, &validationErr) {
			t.Errorf("Expected ValidationError, got %T: %v", err, err)
			return
		}

		// Verify we got exactly one body error
		if validationErr.NumBodyErrors() != 1 {
			t.Errorf("Expected exactly 1 body validation error, got %d body errors: %v",
				validationErr.NumBodyErrors(), validationErr)
			return
		}

		// Verify it's the specific body error we expect
		if !validationErr.Contains("loadID is a required field") {
			t.Errorf("Expected body error about LoadID field, got: %v", validationErr)
		}
	})

	t.Run("missing required StartTime", func(t *testing.T) {
		body := BodyV2{
			LoadID:            1,
			FreightTrackingID: "12345",
			DryRun:            false,
			StopType:          "pickup",
			WarehouseID:       "warehouse-1",
			WarehouseTimezone: "America/New_York",
			DockID:            "dock-1",
			LoadTypeID:        "test-load",
			SubscribedEmail:   "<EMAIL>",
			CcEmails: []string{
				"<EMAIL>",
			},
			TrailerType: "Cargo Van",
			Notes:       "this is a note",
			PONums:      "PRO 2080005",
			RefNumber:   "",
			CustomApptFieldsTemplate: []models.CustomApptField{
				{
					Name:                 "",
					Type:                 "",
					Label:                "",
					Value:                "",
					Description:          "",
					Placeholder:          "",
					DropDownValues:       []string{},
					HiddenFromCarrier:    false,
					RequiredForCarrier:   false,
					RequiredForCheckIn:   false,
					RequiredForWarehouse: false,
					MaxLengthOrValue:     0,
					MinLengthOrValue:     0,
				},
			},
		}

		err := validator.TestBody(body)
		if err == nil {
			t.Error("Expected error for missing StartTime, got none")
			return
		}

		var validationErr *apitesting.ValidationError
		if !errors.As(err, &validationErr) {
			t.Errorf("Expected ValidationError, got %T: %v", err, err)
			return
		}

		// Verify we got exactly one body error
		if validationErr.NumBodyErrors() != 1 {
			t.Errorf("Expected exactly 1 body validation error, got %d body errors: %v",
				validationErr.NumBodyErrors(), validationErr)
			return
		}

		// Verify it's the specific body error we expect
		if !validationErr.Contains("start is a required field") {
			t.Errorf("Expected body error about StartTime field, got: %v", validationErr)
		}
	})

	t.Run("missing required DockID", func(t *testing.T) {
		body := BodyV2{
			LoadID:            1,
			FreightTrackingID: "12345",
			DryRun:            false,
			StopType:          "pickup",
			StartTime:         time.Now(),
			WarehouseID:       "warehouse-1",
			WarehouseTimezone: "America/New_York",
			LoadTypeID:        "test-load",
			SubscribedEmail:   "<EMAIL>",
			CcEmails: []string{
				"<EMAIL>",
			},
			TrailerType: "Cargo Van",
			Notes:       "this is a note",
			PONums:      "PRO 2080005",
			RefNumber:   "",
			CustomApptFieldsTemplate: []models.CustomApptField{
				{
					Name:                 "",
					Type:                 "",
					Label:                "",
					Value:                "",
					Description:          "",
					Placeholder:          "",
					DropDownValues:       []string{},
					HiddenFromCarrier:    false,
					RequiredForCarrier:   false,
					RequiredForCheckIn:   false,
					RequiredForWarehouse: false,
					MaxLengthOrValue:     0,
					MinLengthOrValue:     0,
				},
			},
		}

		err := validator.TestBody(body)
		if err == nil {
			t.Error("Expected error for missing DockID, got none")
			return
		}

		var validationErr *apitesting.ValidationError
		if !errors.As(err, &validationErr) {
			t.Errorf("Expected ValidationError, got %T: %v", err, err)
			return
		}

		// Verify we got exactly one body error
		if validationErr.NumBodyErrors() != 1 {
			t.Errorf("Expected exactly 1 body validation error, got %d body errors: %v",
				validationErr.NumBodyErrors(), validationErr)
			return
		}

		// Verify it's the specific body error we expect
		if !validationErr.Contains("dockId is a required field") {
			t.Errorf("Expected body error about DockID field, got: %v", validationErr)
		}
	})

	t.Run("missing required LoadTypeID", func(t *testing.T) {
		body := BodyV2{
			LoadID:            1,
			FreightTrackingID: "12345",
			DryRun:            false,
			StopType:          "pickup",
			StartTime:         time.Now(),
			WarehouseID:       "warehouse-1",
			WarehouseTimezone: "America/New_York",
			DockID:            "dock-1",
			SubscribedEmail:   "<EMAIL>",
			CcEmails: []string{
				"<EMAIL>",
			},
			TrailerType: "Cargo Van",
			Notes:       "this is a note",
			PONums:      "PRO 2080005",
			RefNumber:   "",
			CustomApptFieldsTemplate: []models.CustomApptField{
				{
					Name:                 "",
					Type:                 "",
					Label:                "",
					Value:                "",
					Description:          "",
					Placeholder:          "",
					DropDownValues:       []string{},
					HiddenFromCarrier:    false,
					RequiredForCarrier:   false,
					RequiredForCheckIn:   false,
					RequiredForWarehouse: false,
					MaxLengthOrValue:     0,
					MinLengthOrValue:     0,
				},
			},
		}

		err := validator.TestBody(body)
		if err == nil {
			t.Error("Expected error for missing LoadTypeID, got none")
			return
		}

		var validationErr *apitesting.ValidationError
		if !errors.As(err, &validationErr) {
			t.Errorf("Expected ValidationError, got %T: %v", err, err)
			return
		}

		// Verify we got exactly one body error
		if validationErr.NumBodyErrors() != 1 {
			t.Errorf("Expected exactly 1 body validation error, got %d body errors: %v",
				validationErr.NumBodyErrors(), validationErr)
			return
		}

		// Verify it's the specific body error we expect
		if !validationErr.Contains("loadTypeId is a required field") {
			t.Errorf("Expected body error about LoadTypeID field, got: %v", validationErr)
		}
	})
}

func TestFormatOpendockMultiselect(t *testing.T) {
	// Test that dropdownmultiselect fields are correctly converted from strings to arrays
	// to meet OpenDock API requirements. This prevents the "invalid value" error when
	// creating appointments with multiselect dropdown fields.
	t.Run("dropdownmultiselect string to array conversion", func(t *testing.T) {
		fields := models.CustomApptFieldsTemplate{
			{
				Name:  "regularDropdown",
				Type:  "dropdown",
				Label: "Regular Dropdown",
				Value: "Some Value",
			},
			{
				Name:  "dropdownmultiselect",
				Type:  "dropdownmultiselect",
				Label: "Dropdown Multiselect",
				Value: "Some Value",
			},
			{
				Name:  "emptyDropdownmultiselect",
				Type:  "dropdownmultiselect",
				Label: "Empty Multi",
				Value: "",
			},
			{
				Name:  "nilDropdownmultiselect",
				Type:  "dropdownmultiselect",
				Label: "Nil Multi",
				Value: nil,
			},
		}

		processed := formatOpendockMultiselect(fields)

		// Check that regular dropdown values remain unchanged
		assert.Equal(t, "Some Value", processed[0].Value)

		// Check that dropdownmultiselect values are converted to arrays
		assert.Equal(t, []string{"Some Value"}, processed[1].Value)

		// Check that empty string becomes empty array
		assert.Equal(t, []string{}, processed[2].Value)

		// Check that nil becomes empty array
		assert.Equal(t, []string{}, processed[3].Value)
	})

	// Test that non-dropdownmultiselect fields are left unchanged
	t.Run("non-dropdownmultiselect fields unchanged", func(t *testing.T) {
		fields := models.CustomApptFieldsTemplate{
			{
				Name:  "textField",
				Type:  "str",
				Label: "Text Field",
				Value: "Some text",
			},
			{
				Name:  "numberField",
				Type:  "int",
				Label: "Number Field",
				Value: 42,
			},
		}

		processed := formatOpendockMultiselect(fields)

		// Check that non-dropdownmultiselect fields are unchanged
		assert.Equal(t, "Some text", processed[0].Value)
		assert.Equal(t, 42, processed[1].Value)
	})
}
