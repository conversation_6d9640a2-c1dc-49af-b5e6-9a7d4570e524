package appt

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/integrations/scheduling"
	"github.com/drumkitai/drumkit/common/integrations/scheduling/retalix"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	apptDB "github.com/drumkitai/drumkit/common/rds/appt"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	loadDB "github.com/drumkitai/drumkit/common/rds/load"
	warehouseDB "github.com/drumkitai/drumkit/common/rds/warehouses"
)

type (
	SubmitApptBody struct {
		PONumbers       []string               `json:"poNumbers" validate:"required"`
		Source          models.WarehouseSource `json:"source" validate:"required"`
		WarehouseID     string                 `json:"warehouseId" validate:"required"`
		LumperRequested bool                   `json:"lumperRequested"`
		RequestedDate   time.Time              `json:"requestedDate"`
		Note            string                 `json:"note"`
		TimePreference  models.TimePreference  `json:"timePreference"`
		RequestType     models.RequestType     `json:"requestType"`
	}
)

// NOTE: Only supports E2open, Manhattan and Retalix now
func SubmitAppt(c *fiber.Ctx) error {
	var body SubmitApptBody
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	email := middleware.ClaimsFromContext(c).Email
	userID := middleware.UserIDFromContext(c)
	userServiceID := middleware.ServiceIDFromContext(c)

	ctx := log.With(
		c.UserContext(),
		zap.String("emailAddress", email),
		zap.Uint("userID", userID),
		zap.Uint("serviceID", userServiceID),
		zap.String("integration", string(body.Source)),
		zap.Any("requestBody", body),
	)

	user, err := rds.GetUserByID(ctx, userID)
	if err != nil {
		log.Error(ctx, "GetUserByID failed", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	integration, err := integrationDB.GetSchedulerByServiceUserIDsAndName(
		ctx,
		userID,
		userServiceID,
		string(body.Source),
	)
	if err != nil {
		log.Error(ctx, "failed to get scheduler integration", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	var warehouse *models.Warehouse

	if body.Source == models.RetalixSource {
		warehouse, err = warehouseDB.GetWarehouseByIDAndSource(
			ctx,
			userServiceID,
			body.Source,
			body.WarehouseID,
		)
		if err != nil {
			log.Error(
				ctx,
				"error getting warehouse name",
				zap.Error(err),
				zap.String("warehouseID", body.WarehouseID),
			)

			return c.SendStatus(http.StatusInternalServerError)
		}
	}

	appt := models.Appointment{
		Account:     email,
		UserID:      user.ID,
		ServiceID:   user.ServiceID,
		Source:      models.IntegrationName(body.Source),
		WarehouseID: body.WarehouseID,
		Notes:       body.Note,
		PONums:      fmt.Sprint(body.PONumbers),
	}

	// Build options only when date or time preference provided
	var opts []models.SchedulingOption

	if !body.RequestedDate.IsZero() {
		opts = append(opts, models.WithRequestedDate(body.RequestedDate))
		appt.Date = body.RequestedDate.Format(time.DateOnly)
	}

	if body.TimePreference != "" {
		opts = append(opts, models.WithTimePreference(body.TimePreference))
		appt.TimePreference = string(body.TimePreference)
	}

	if body.RequestType != "" {
		opts = append(opts, models.WithRequestType(body.RequestType))
	}

	err = submitAppt(ctx, integration, body.PONumbers, warehouse, body.LumperRequested, body.Note, opts...)
	if err != nil {
		log.Error(ctx, "failed to submit appointment", zap.Error(err))

		var retalixErr *retalix.Error
		if errors.As(err, &retalixErr) &&
			retalixErr.Type == retalix.ValidationError &&
			retalixErr.Message == "request for review appointment type selection not available" {

			return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
				"message": "Live appointments are not supported at this time.",
			})
		}

		return c.SendStatus(http.StatusInternalServerError)
	}

	if err := apptDB.Create(ctx, &appt); err != nil {
		log.Error(ctx, "failed to create appt record in DB", zap.Error(err))
		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to save appointment"})
	}

	return c.SendStatus(http.StatusOK)
}

func submitAppt(
	ctx context.Context,
	integration models.Integration,
	poNumbers []string,
	warehouse *models.Warehouse,
	lumperRequested bool,
	note string,
	opts ...models.SchedulingOption,
) error {

	client, err := scheduling.GetCachedClient(ctx, integration)
	if err != nil {
		log.Error(ctx, "failed to build scheduling client", zap.Error(err))
	}

	err = client.SubmitAppointment(ctx, poNumbers, *warehouse, lumperRequested, note, opts...)
	if err != nil {
		return fmt.Errorf("error submitting appointment: %w", err)
	}

	log.Info(ctx, "appointment created successfully")

	options := &models.SchedulingOptions{}
	options.Apply(opts...)

	requestType := options.RequestType

	if requestType == "" {
		log.Error(ctx, "request type is required")
		return nil
	}

	load, err := loadDB.GetLoadByWarehouseID(
		ctx,
		integration.ServiceID,
		warehouse.ID,
		requestType,
	)
	if err != nil {
		log.Error(ctx, "failed to get load for warehouse-address association", zap.Error(err))
		return nil
	}

	err = warehouseDB.CreateWarehouseAddress(
		ctx,
		*warehouse,
		load,
		requestType,
	)
	if err != nil {
		log.Error(ctx, "failed to create warehouse address association", zap.Error(err))
		return nil
	}

	return nil
}
