package suggestions

import (
	"errors"
	"fmt"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/helpers/perms"
	"github.com/drumkitai/drumkit/common/integrations/tms"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	loadDB "github.com/drumkitai/drumkit/common/rds/load"
	suggestionsDB "github.com/drumkitai/drumkit/common/rds/suggestedloadchanges"
)

type (
	ApplyApptConfirmationPath struct {
		ID uint
	}

	ApplyApptConfirmationBody struct {
		Type    string                  `json:"type"`
		Changes models.Changes          `json:"changes"`
		Status  models.SuggestionStatus `json:"status" validate:"required,oneof=accepted rejected"`
	}

	ChangesResponse struct {
		FreightTrackingID string                `json:"freightTrackingID"`
		Load              models.Load           `json:"load"`
		LoadAttributes    models.LoadAttributes `json:"loadAttributes"`
	}

	ApplyApptConfirmationResponse struct {
		Changes ChangesResponse `json:"changes"`
		Message string          `json:"message,omitempty"`
	}
)

func ApplyApptConfirmation(c *fiber.Ctx) error {
	userServiceID := middleware.ServiceIDFromContext(c)

	var path ApplyApptConfirmationPath
	var body ApplyApptConfirmationBody

	if err := api.Parse(c, &path, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}
	ctx := c.UserContext()
	suggestion, err := suggestionsDB.GetSuggestionByID(ctx, path.ID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Status(http.StatusNotFound).SendString(
				fmt.Sprintf("suggestion ID %d not found", path.ID))
		}

		log.WarnNoSentry(ctx, "get suggestions endpoint - suggestion query error", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	claims := middleware.ClaimsFromContext(c)
	if claims.Email != suggestion.Account {
		log.ErrorNoSentry(ctx, "unauthorized: email from token does not match DB",
			zap.String("dbSuggestionAccount", suggestion.Account), zap.String("claimsEmail", claims.Email))

		return c.SendStatus(http.StatusUnauthorized)
	}

	if body.Status == models.Rejected {
		suggestion.Status = body.Status
		if err = suggestionsDB.UpdateSuggestion(ctx, suggestion); err != nil {
			errMsg := fmt.Sprintf("Suggestion %d was not saved in the DB with status %s."+
				" Please check the DB and make the change manually if necessary.",
				suggestion.ID, body.Status)
			log.Error(ctx, errMsg, zap.Error(err))

			response := ApplyApptConfirmationResponse{
				Changes: ChangesResponse{},
				Message: "Uh oh. Something went wrong trying to skip the suggestion.",
			}

			return c.Status(http.StatusUnprocessableEntity).JSON(response)
		}

		response := ApplyApptConfirmationResponse{
			Changes: ChangesResponse{},
			Message: "Suggestion skipped",
		}

		return c.Status(http.StatusOK).JSON(response)
	}

	var tmsIntegration *models.Integration
	var isGormRecordNotFound bool
	isLoadOnDB := false

	// looking up load on DB to get TMS Model
	loadFromDB, err := loadDB.GetLoadByFreightIDAndService(ctx, userServiceID, suggestion.FreightTrackingID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			isGormRecordNotFound = true
		}

		// fallback: if load is not on DB, match TMS by FreightID
		tmsIntegration, err = integrationDB.MatchTMSByServiceAndFreightID(
			ctx, userServiceID, suggestion.FreightTrackingID)
		if err != nil {
			return fmt.Errorf("could not get tms integration: %w", err)
		}
	} else {
		isLoadOnDB = true
		tmsIntegration = &loadFromDB.TMS
	}

	tmsClient, err := tms.New(ctx, *tmsIntegration)
	if err != nil {
		log.Error(ctx, "error creating Aljex client", zap.Error(err))

		return c.Status(http.StatusServiceUnavailable).SendString("error connecting to TMS")
	}

	existingLoad, existingAttrs, err := tmsClient.GetLoad(ctx, suggestion.FreightTrackingID)
	if err != nil {
		log.WarnNoSentry(ctx, "error fetching load from TMS, falling back to DB", zap.Error(err))
		if !isLoadOnDB {
			// we have already tried fetching from the DB, so just check if error was gorm.ErrRecordNotFound
			if isGormRecordNotFound {
				return c.Status(http.StatusNotFound).SendString(
					fmt.Sprintf("load with ID %d not found", path.ID))
			}

			log.Error(ctx, "loadDB query error", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}

		existingLoad = loadFromDB
	}

	if !perms.HasLoadReadWritePermissions(c, existingLoad, tmsClient) {
		const msg = "not allowed to modify production PRO"
		log.WarnNoSentry(ctx, msg)
		return c.Status(http.StatusForbidden).SendString(msg)
	}

	newLoad := existingLoad

	if body.Type == "pickup" {
		if existingLoad.Pickup.ApptStartTime.Equal(body.Changes.PickupApptTime) {
			changesResp := ChangesResponse{
				FreightTrackingID: suggestion.FreightTrackingID,

				Load:           existingLoad,
				LoadAttributes: existingAttrs,
			}

			response := ApplyApptConfirmationResponse{
				Changes: changesResp,
				Message: "No changes detected, TMS not updated",
			}

			return c.Status(http.StatusOK).JSON(response)
		}

		newLoad.Pickup.ApptStartTime = body.Changes.PickupApptTime
	}

	if body.Type == "dropoff" {
		if existingLoad.Consignee.ApptStartTime.Equal(body.Changes.DropoffApptTime) {
			changesResp := ChangesResponse{
				FreightTrackingID: suggestion.FreightTrackingID,

				Load:           existingLoad,
				LoadAttributes: existingAttrs,
			}

			response := ApplyApptConfirmationResponse{
				Changes: changesResp,
				Message: "No changes detected, TMS not updated",
			}

			return c.Status(http.StatusOK).JSON(response)
		}

		newLoad.Consignee.ApptStartTime = body.Changes.DropoffApptTime
	}

	var updatedAttrs models.LoadAttributes
	var updatedLoad models.Load
	if updatedLoad, updatedAttrs, err = tmsClient.UpdateLoad(ctx,
		&existingLoad,
		&newLoad,
	); err != nil {
		log.Error(ctx, "error updating TMS", zap.Error(err))
		return c.Status(http.StatusServiceUnavailable).SendString("error while updating TMS")
	}

	if err = loadDB.UpsertLoad(ctx, &updatedLoad, nil); err != nil {
		// Fail-open
		log.Error(ctx, "error updating load in DB", zap.Error(err))
	}

	changesResp := ChangesResponse{
		FreightTrackingID: suggestion.FreightTrackingID,
		Load:              updatedLoad,
		LoadAttributes:    updatedAttrs,
	}

	suggestion.Applied = models.SuggestedChanges{Changes: &body.Changes}
	suggestion.Status = models.Accepted
	if err = suggestionsDB.UpdateSuggestion(ctx, suggestion); err != nil {
		log.Error(ctx, "suggestion update error", zap.Error(err))

		response := ApplyApptConfirmationResponse{
			Changes: changesResp,
			Message: "Updated TMS successfully, but Drumkit failed to log this action.",
		}

		return c.Status(http.StatusUnprocessableEntity).JSON(response)
	}

	response := ApplyApptConfirmationResponse{
		Changes: changesResp,
		Message: "Updating load succeeded",
	}

	return c.Status(http.StatusOK).JSON(response)
}
