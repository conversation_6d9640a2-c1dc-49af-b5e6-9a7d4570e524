package main

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/integrations/tms"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	loadDB "github.com/drumkitai/drumkit/common/rds/load"
	"github.com/drumkitai/drumkit/fn/poller/env"
)

// Time to consider loads as "recently updated" in minutes
const recentlyUpdatedMinutes = 20

// Polls new loads from the last `defaultDuration`. If none are found, backfills last `env.Vars.BackfillHours`
// (default=6) hours of loads. Also accepts a `forceBackfill` flag to force backfill even if loads exist.
// If startTime is provided, backfill will start from that time instead of current time.
// This polling approach is best used for TMSes that either
// a) have native search APIs, or
// b) the TMS' load object exposes a CreatedAt/OrderedDate by which we can filter the loads manually.
func pollByTime(
	ctx context.Context,
	tmsVar models.Integration,
	forceBackfill bool,
	startTime models.NullTime,
) (err error) {

	ctx, attrs := otel.LogWithIntegrationAttrs(ctx, tmsVar)
	ctx, metaSpan := otel.StartSpan(ctx, "pollByTime", attrs)
	defer func() {
		if err != nil {
			log.Error(ctx, string(tmsVar.Name)+" polling job failed", zap.Error(err))
		}
		metaSpan.End(err)
	}()

	baseTime := time.Now()
	if startTime.Valid {
		baseTime = startTime.Time
	}

	searchFrom := baseTime.Add(defaultFromDuration)
	// If only startTime is provided, some TMSes will return all loads from that start time to now
	// Other TMSes will return loads only from that day, so we explicitly set searchTo to now for consistency
	searchTo := time.Now()

	log.Info(
		ctx,
		"Polling loads from date:",
		zap.String("baseTime", baseTime.Format(time.RFC3339)),
		zap.String("startTimeParam", startTime.Time.Format(time.RFC3339)),
		zap.Bool("startTimeParamValid", startTime.Valid),
		zap.String("searchFrom", searchFrom.Format(time.RFC3339)),
		zap.String("searchTo", searchTo.Format(time.RFC3339)),
	)

	client, err := tms.New(ctx, tmsVar)
	if err != nil {
		return fmt.Errorf("error building TMS client: %w", err)
	}

	service, err := rds.GetServiceByID(ctx, tmsVar.ServiceID)
	if err != nil {
		log.WarnNoSentry(ctx, "error getting service for warehouse matching check", zap.Error(err))
	}

	// Get existing load IDs to track which are new vs updates
	loadSet, err := GetFreightTrackingIDSet(ctx, tmsVar.ID)
	if err != nil {
		return fmt.Errorf("error getting freightTrackingID set: %w", err)
	}

	// Get load IDs for loads that don't have carrier cost populated
	loadsWithEmptyCarrierCost, err := GetLoadsWithEmptyCarrierCost(ctx, tmsVar.ID, tmsVar.Name)
	if err != nil {
		return fmt.Errorf("error getting loads with empty carrier cost: %w", err)
	}

	// Get load IDs for loads that were updated recently to avoid excessive processing
	recentlyUpdatedLoads, err := GetRecentlyUpdatedLoadIDSet(ctx, tmsVar.ID, recentlyUpdatedMinutes)
	if err != nil {
		return fmt.Errorf("error getting recently updated loads: %w", err)
	}
	log.Info(ctx, "found recently updated loads", zap.Int("count", len(recentlyUpdatedLoads)))

	// Backfill when no loads exist or when `forceBackfill` is true
	if len(loadSet) == 0 || forceBackfill {
		log.Info(ctx, "no loads detected, backfilling", zap.Int("backfillHours", env.Vars.BackfillHours))
		searchFrom = time.Now().Add(-time.Duration(env.Vars.BackfillHours) * time.Hour)
	}

	loadIDs, err := client.GetLoadIDs(ctx, models.SearchLoadsQuery{
		FromDate: models.ToValidNullTime(searchFrom),
		ToDate:   models.ToValidNullTime(searchTo),
	})
	if err != nil {
		return fmt.Errorf("error getting load list: %w", err)
	}

	log.Info(ctx, "fetched load IDs (may include duplicates)", zap.Int("countTotal", len(loadIDs)))

	var countSuccess, processedLoadIDs, newLoads, updatedLoads, skippedRecentlyUpdated int
	totalLoadsToProcess := len(loadIDs)
	log.Info(ctx, "starting load processing loop", zap.Int("totalLoadsToProcess", totalLoadsToProcess))

	for i, loadID := range loadIDs {
		logFields := []zap.Field{zap.String("loadID", loadID), zap.Int("loopIndex", i)}
		log.Info(ctx, "evaluating load", logFields...)

		isExistingLoad := false
		needsCarrierCostUpdate := false
		wasRecentlyUpdated := false

		if _, ok := loadSet[loadID]; ok {
			isExistingLoad = true
			// Check if this load was recently updated in our DB
			if _, ok := recentlyUpdatedLoads[loadID]; ok {
				wasRecentlyUpdated = true
				log.Debug(ctx, "load was recently updated in DB", logFields...)
			}

			// Check if this load needs carrier cost update
			if _, ok := loadsWithEmptyCarrierCost[loadID]; ok {
				needsCarrierCostUpdate = true
				log.Debug(ctx, "existing load needs carrier cost update", logFields...)
			}
		}

		shouldSkipLoad := shouldSkipLoad(
			isExistingLoad,
			forceBackfill,
			needsCarrierCostUpdate,
			wasRecentlyUpdated,
			tmsVar.Name,
		)

		if shouldSkipLoad {
			if wasRecentlyUpdated {
				log.Debug(ctx, "skipping recently updated load", logFields...)
				skippedRecentlyUpdated++
			} else {
				log.Debug(
					ctx,
					"skipping (already exists with carrier cost or not Turvo/McleodEnterprise)",
					logFields...,
				)
			}
			continue // Skip to the next loadID
		}

		if isExistingLoad {
			log.Debug(ctx, "processing existing load (needs carrier cost update)", logFields...)
			updatedLoads++
		} else {
			log.Debug(ctx, "processing new load ID", logFields...)
			newLoads++
		}

		processedLoadIDs++

		load, _, err := client.GetLoad(ctx, loadID)
		if err != nil {
			log.Warn(ctx, "error getting load from TMS", append(logFields, zap.Error(err))...)
			continue
		}

		err = loadDB.UpsertLoad(ctx, &load, &service)
		if err != nil {
			// Gorm callback will send to Sentry
			log.WarnNoSentry(ctx, "error upserting load in DB", append(logFields, zap.Error(err))...)
		} else {
			loadSet[loadID] = struct{}{}
			countSuccess++
			log.Debug(ctx, "successfully upserted load", logFields...)
		}

		// Sleep to account for rate limits.
		// TODO: Rate limit by TMS
		time.Sleep(100 * time.Millisecond)
	}

	log.Info(
		ctx,
		"finished load processing loop",
		zap.Int("countSuccess", countSuccess),
		zap.Int("processedLoadIDs", processedLoadIDs),
		zap.Int("newLoads", newLoads),
		zap.Int("updatedLoads", updatedLoads),
		zap.Int("skippedRecentlyUpdated", skippedRecentlyUpdated),
		zap.Int("totalLoadsToProcess", totalLoadsToProcess),
	)

	if processedLoadIDs > 0 {
		log.Infof(ctx, "successfully upserted loads to DB",
			zap.Int("countSuccess", countSuccess),
			zap.Int("processedLoadIDs", processedLoadIDs),
			zap.Int("newLoads", newLoads),
			zap.Int("updatedLoads", updatedLoads),
			zap.Int("skippedRecentlyUpdated", skippedRecentlyUpdated))
	}
	return nil
}

// Process the load if:
// 1. It's a new load, or
// 2. forceBackfill is true, or
// 3. It's Turvo, Mcleod Enterprise, or Aljex AND the load needs carrier cost update AND wasn't recently updated
// 4. It's GlobalTranzTMS AND the load needs carrier cost update AND pickup is within the last 4 weeks
//
// Note: GlobalTranzTMS pickup filter is added within the GetLoadsWithEmptyCarrierCost function
func shouldSkipLoad(
	isExistingLoad,
	forceBackfill,
	needsCarrierCostUpdate,
	wasRecentlyUpdated bool,
	tmsName models.IntegrationName,
) bool {
	if !isExistingLoad || forceBackfill {
		return false
	}
	if tmsName == models.Turvo || tmsName == models.McleodEnterprise {
		return !needsCarrierCostUpdate || wasRecentlyUpdated
	}
	if tmsName == models.GlobalTranzTMS {
		return !needsCarrierCostUpdate
	}
	return true
}
