package main

import (
	"context"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"github.com/drumkitai/drumkit/common/helpers/crypto"
	"github.com/drumkitai/drumkit/common/log"
	"go.uber.org/zap"
	"io"
	"os"
)

func main() {
	if len(os.Args) < 2 {
		fmt.Println("Usage: go run encrypt_password.go <password>")
		os.Exit(1)
	}

	password := os.Args[1]
	// Development key - same as in your codebase
	key := []byte("abcdefghijklmnopqrstuvwxyz123456")

	encrypted, err := encryptAESGCM(password, key)
	if err != nil {
		fmt.Printf("Error encrypting password: %v\n", err)
		os.Exit(1)
	}

	password_new, err_new := crypto.DecryptAESGCM(context.Background(), string(encrypted), &key)
	//password_new, err_new := crypto.DecryptAESGCM(context.Background(), string(encrypted), []byte("abcdefghijklmnopqrstuvwxyz123456"))
	if err_new != nil {
		fmt.Printf("Error decrypting password: %v\n", err_new)
		os.Exit(1)
	}
	log.Info(context.Background(), "password_+++++=====new", zap.String("password_new", password_new))

	fmt.Printf("Encrypted password: %s\n", encrypted)
}

func encryptAESGCM(plaintext string, key []byte) (string, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}

	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return "", err
	}

	cipherBytes := gcm.Seal(nonce, nonce, []byte(plaintext), nil)

	return base64.StdEncoding.EncodeToString(cipherBytes), nil
}
