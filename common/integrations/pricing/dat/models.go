package dat

import (
	"time"

	"github.com/drumkitai/drumkit/common/models"
)

type (
	// Auth
	OrgAuthRequestBody struct {
		Username string `json:"username"`
		Password string `json:"password"`
	}

	UserAuthRequestBody struct {
		Username string `json:"username"`
	}

	TokenResponse struct {
		AccessToken string    `json:"accessToken"`
		ExpiresWhen time.Time `json:"expiresWhen"`
	}

	// Lane Rate
	GetLaneRateResponse struct {
		Transaction   string             `json:"transaction"`
		Created       string             `json:"created"`
		RateResponses []RateResponseItem `json:"rateResponses"`
	}

	RateResponseItem struct {
		Response RateResponse `json:"response"`
		Request  RateRequest  `json:"request"`
	}

	RateResponse struct {
		Rate       Rate       `json:"rate"`
		Escalation Escalation `json:"escalation"`
	}

	RateRequest struct {
		Origin      InputLocation        `json:"origin"`
		Destination InputLocation        `json:"destination"`
		Equipment   models.TransportType `json:"equipment"`
		RateType    RateType             `json:"rateType"`
		// We don't allow customization of these fields yet
		// IncludeMyRate    bool                 `json:"includeMyRate"`
		// TargetEscalation InputEscalation      `json:"targetEscalation"`
		// RateTimePeriod   RateTimePeriod       `json:"rateTimePeriod"`
	}

	InputLocation struct {
		PostalCode      string `json:"postalCode,omitempty"`
		City            string `json:"city,omitempty"`
		StateOrProvince string `json:"stateOrProvince,omitempty"`
	}

	Rate struct {
		Mileage           float64 `json:"mileage"`
		Reports           int     `json:"reports"`
		Companies         int     `json:"companies"`
		StandardDeviation float64 `json:"standardDeviation"`
		// Line haul only, excludes FSC so Drumkit adds it to generate all-in quote
		PerMile PriceRange `json:"perMile"`
		// Line haul only, excludes FSC so Drumkit adds it to generate all-in quote
		PerTrip PriceRange `json:"perTrip"`

		AverageFuelSurchargePerMileUsd float64 `json:"averageFuelSurchargePerMileUsd"`
		AverageFuelSurchargePerTripUsd float64 `json:"averageFuelSurchargePerTripUsd"`
	}

	Escalation struct {
		Timeframe   RateTimeframe `json:"timeframe"`
		Origin      Location      `json:"origin"`
		Destination Location      `json:"destination"`
	}

	Location struct {
		Name string       `json:"name"`
		Type LocationType `json:"type"`
	}

	PriceRange struct {
		RateUSD float64 `json:"rateUsd"`
		HighUSD float64 `json:"highUsd"`
		LowUSD  float64 `json:"lowUsd"`
	}

	// MCI Shared Request Struct
	GetMarketInformationRequest struct {
		AreaType        MarketInformationLocationType `json:"areaType"`
		TransportType   models.TransportType          `json:"transportType"`
		Direction       MarketInformationDirection    `json:"direction"`
		Timeframe       MarketInformationTimeframe    `json:"timeframe"`
		City            string                        `json:"city"`
		StateOrProvince string                        `json:"stateOrProvince"`
		PostalCode      string                        `json:"postalCode"`
	}

	// MCI - Load to Truck ratio
	GetLoadToTruckRatioRequest struct {
		GetMarketInformationRequest
		StartDate string `json:"startDate"`
		EndDate   string `json:"endDate"`
	}

	LoadToTruckRatio struct {
		Date   string `json:"date"`
		Loads  int    `json:"loads"`
		Trucks int    `json:"trucks"`
		Ratio  string `json:"ratio"`
	}

	GetLoadToTruckRatioResponse struct {
		GeoCode           string             `json:"geoCode"`
		LoadToTruckRatios []LoadToTruckRatio `json:"loadTruckRatios"`
	}

	LocationLoadToTruckRatios struct {
		Inbound  LoadToTruckRatio `json:"inbound"`
		Outbound LoadToTruckRatio `json:"outbound"`
	}

	// MCI - Market Conditions Forecast
	MarketConditionsForecast struct {
		Date                        string `json:"date"`
		PredictedIntervalLowerBound int    `json:"predictedIntervalLowerBound"`
		PredictedIntervalUpperBound int    `json:"predictedIntervalUpperBound"`
		MciScore                    int    `json:"mciScore"`
	}

	GetMarketConditionsForecastResponse struct {
		GeoCode                  string                     `json:"geoCode"`
		MarketConditionsForecast []MarketConditionsForecast `json:"marketConditionsForecasts"`
	}

	LocationMarketConditionsForecast struct {
		Inbound  []MarketConditionsForecast `json:"inbound"`
		Outbound []MarketConditionsForecast `json:"outbound"`
	}

	// MCI - Market Conditions Score
	MarketConditionsIndex struct {
		Date              string `json:"date"`
		LoadSearchCounts  int    `json:"loadSearchCounts"`
		TruckSearchCounts int    `json:"truckSearchCounts"`
		MciScore          int    `json:"mciScore"`
	}

	GetMarketConditionsScoreResponse struct {
		Date                        string                  `json:"date"`
		GeoCode                     string                  `json:"geoCode"`
		SummarizedLoadSearchCounts  int                     `json:"summarizedLoadSearchCounts"`
		SummarizedTruckSearchCounts int                     `json:"summarizedTruckSearchCounts"`
		SummarizedMciScore          int                     `json:"summarizedMciScore"`
		MarketConditionsIndexes     []MarketConditionsIndex `json:"marketConditionsIndexes"`
	}

	LocationMarketConditionsIndex struct {
		Inbound            []MarketConditionsIndex `json:"inbound"`
		InboundSummarized  int                     `json:"inboundSummarized"`
		Outbound           []MarketConditionsIndex `json:"outbound"`
		OutboundSummarized int                     `json:"outboundSummarized"`
	}
)
