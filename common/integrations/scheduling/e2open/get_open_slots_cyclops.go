package e2open

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/redis"
)

type (
	CyclopsGetSlotsRequest struct {
		models.CyclopsGetSlotsRequest
		ProID       string                      `json:"proId"`
		RequestType string                      `json:"requestType"`
		FilterType  string                      `json:"filterType,omitempty"`
		Operation   string                      `json:"operation"`
		CompanyName string                      `json:"companyName"`
		Warehouse   models.CyclopsWarehouseInfo `json:"warehouse"`
	}
)

func (e *E2open) GetOpenSlotsWithCyclops(
	ctx context.Context,
	loadTypeID string,
	req models.GetOpenSlotsRequest,
) ([]models.Slot, error) {

	if req.RequestType == "" {
		req.RequestType = models.RequestTypePickup
	}

	if !req.RequestType.IsValid() {
		return nil, fmt.Errorf("invalid request type: %s", req.RequestType)
	}

	cyclopsReq := CyclopsGetSlotsRequest{
		CyclopsGetSlotsRequest: models.CyclopsGetSlotsRequest{
			CyclopsBaseRequest: models.CyclopsBaseRequest{
				Integration: models.CyclopsSchedulingIntegration,
				Platform:    E2openPlatform,
				Action:      models.ActionGetOpenSlots,
				UserID:      e.scheduler.Username,
				Credentials: models.CyclopsCredentials{
					Username: e.creds.Username,
					Password: e.creds.Password,
				},
			},
			StartDate: req.Start.Format("2006-01-02"),
			EndDate:   req.End.Format("2006-01-02"),
		},
		ProID:       loadTypeID,
		RequestType: req.AppointmentType,
		Operation:   req.Operation,
		CompanyName: req.Company,
		Warehouse: models.CyclopsWarehouseInfo{
			City:     req.City,
			State:    req.State,
			Country:  req.Country,
			ZipCode:  req.ZipCode,
			StopType: string(req.RequestType),
		},
	}

	reqBody, err := json.Marshal(cyclopsReq)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	cyclopsURL, err := helpers.GetCyclopsURL()
	if err != nil {
		return nil, fmt.Errorf("failed to get url: %w", err)
	}

	httpReq, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		cyclopsURL,
		bytes.NewBuffer(reqBody),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Accept", "application/json")

	resp, err := otel.TracingHTTPClient().Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	var slotsResp models.CyclopsGetSlotsResponse
	if err = json.Unmarshal(body, &slotsResp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal body: %w", err)
	}

	if !slotsResp.Success {
		return nil, &models.CyclopsError{
			Message: slotsResp.Message,
			Errors:  slotsResp.Errors,
		}
	}

	return convertToSlots(ctx, loadTypeID, slotsResp.Appointments), nil
}

func convertToSlots(
	ctx context.Context,
	loadTypeID string,
	appointments []models.CyclopsAppointmentData,
) []models.Slot {

	ctx = log.With(ctx, zap.String("source", string(models.E2openSource)))

	slots := make([]models.Slot, 0)
	for _, appt := range appointments {
		if appt.Status != "AVAILABLE" || appt.Warehouse == nil {
			continue
		}

		var times []time.Time
		for _, slot := range appt.Warehouse.OpenSlots {
			t, err := time.Parse("2006-01-02T15:04:05", slot.ScheduledTime)
			if err != nil {
				log.Infof(
					ctx,
					"Invalid time format for warehouse %s, ScheduledTime %s: %v",
					appt.Warehouse.Name,
					slot.ScheduledTime,
					zap.Error(err),
				)

				continue
			}
			times = append(times, t)
		}

		if len(times) > 0 {
			slot := models.Slot{
				Dock: models.Dock{
					ID: appt.Warehouse.Name,
				},
				StartTimes: times,
			}
			slots = append(slots, slot)
		}

		// Cache warehouse name in Redis for later retrieval
		key := fmt.Sprintf("%s-%s-%s", models.E2openSource, loadTypeID, appt.Warehouse.StopType)
		if err := redis.SetKey(ctx, key, appt.Warehouse.Name, 24*time.Hour); err != nil {
			log.WarnNoSentry(
				ctx,
				"error setting warehouse in redis",
				zap.String("name", appt.Warehouse.Name),
				zap.Error(err),
			)
		}
	}

	return slots
}
