package manhattan

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/models"
)

type (
	CyclopsMakeAppointmentRequest struct {
		models.CyclopsBaseRequest
		Appointments []CyclopsAppointmentData `json:"appointments"`
	}

	CyclopsAppointmentData struct {
		AppointmentID   string                       `json:"appointmentId"`
		PoID            string                       `json:"poId"`
		AppointmentTime string                       `json:"appointmentTime"`
		Duration        int                          `json:"duration"`
		Status          string                       `json:"status"`
		Notes           string                       `json:"notes"`
		Warehouse       *models.CyclopsWarehouseInfo `json:"warehouse,omitempty"`
		FacilityID      string                       `json:"facilityId"`
		FacilityText    string                       `json:"facilityText"`
		AppointmentType string                       `json:"appointmentType"`
	}
)

func (m *Manhattan) MakeAppointment(
	ctx context.Context,
	req models.MakeAppointmentRequest,
	_ ...models.SchedulingOption,
) (models.Appointment, error) {

	if req.RequestType == "" {
		req.RequestType = string(models.RequestTypePickup)
	}

	reqType := models.RequestType(req.RequestType)
	if !reqType.IsValid() {
		return models.Appointment{}, fmt.Errorf("invalid request type: %s", req.RequestType)
	}

	// Handle multiple appointments if provided
	var appointments []CyclopsAppointmentData
	if len(req.Appointments) > 0 {
		// Process multiple appointments
		for _, apptReq := range req.Appointments {
			appointmentData, err := m.buildAppointmentData(apptReq)
			if err != nil {
				return models.Appointment{}, fmt.Errorf(
					"failed to build appointment data for %s: %w",
					apptReq.FreightTrackingID,
					err,
				)
			}
			appointments = append(appointments, appointmentData)
		}
	} else {
		return models.Appointment{}, errors.New("appointments not received")
	}

	cyclopsReq := CyclopsMakeAppointmentRequest{
		CyclopsBaseRequest: models.CyclopsBaseRequest{
			Integration: models.CyclopsSchedulingIntegration,
			Platform:    ManhattanPlatform,
			Action:      models.ActionMakeAppointment,
			UserID:      m.scheduler.Username,
			Credentials: models.CyclopsCredentials{
				Username: m.creds.Username,
				Password: m.creds.Password,
			},
		},
		Appointments: appointments,
	}

	reqBody, err := json.Marshal(cyclopsReq)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to marshal request: %w", err)
	}

	cyclopsURL, err := helpers.GetCyclopsURL()
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to get url: %w", err)
	}

	httpReq, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		cyclopsURL,
		bytes.NewBuffer(reqBody),
	)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Accept", "application/json")

	resp, err := otel.TracingHTTPClient().Do(httpReq)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to read response: %w", err)
	}

	if len(req.Appointments) > 0 {
		// Multiple appointments response
		var multiApptResp CyclopsMultipleAppointmentResponse
		if err = json.Unmarshal(body, &multiApptResp); err != nil {
			return models.Appointment{}, fmt.Errorf("failed to unmarshal multiple appointments response: %w", err)
		}

		if !multiApptResp.Success {
			return models.Appointment{}, &models.CyclopsError{
				Message: multiApptResp.Message,
				Errors:  multiApptResp.Errors,
			}
		}

		// Return the first appointment for backward compatibility with the interface
		appointments, err := convertToAppointments(multiApptResp)
		if err != nil {
			return models.Appointment{}, fmt.Errorf("failed to convert appointments: %w", err)
		}

		if len(appointments) == 0 {
			return models.Appointment{}, errors.New("no appointments returned from cyclops")
		}

		return appointments[0], nil
	}

	return models.Appointment{}, errors.New("appointments not received")
}

// buildAppointmentData builds appointment data for multiple appointments
func (m *Manhattan) buildAppointmentData(
	apptReq models.AppointmentData,
) (CyclopsAppointmentData, error) {

	// Parse the start time from the appointment request
	startTime, err := time.Parse("2006-01-02T15:04:05", apptReq.Start)
	if err != nil {
		// Try alternative time formats
		if startTime, err = time.Parse(time.RFC3339, apptReq.Start); err != nil {
			return CyclopsAppointmentData{}, fmt.Errorf("invalid start time format: %s", apptReq.Start)
		}
	}

	appointmentData := CyclopsAppointmentData{
		PoID:            apptReq.FreightTrackingID,
		AppointmentID:   apptReq.AppointmentID,
		AppointmentTime: startTime.Format("2006-01-02T15:04:05"),
		FacilityID:      apptReq.FacilityID,
		FacilityText:    apptReq.FacilityText,
		AppointmentType: apptReq.AppointmentType,
	}

	return appointmentData, nil
}

func (m *Manhattan) MakeAppointmentWithLoad(
	_ context.Context,
	_ models.MakeAppointmentRequest,
	_ models.Load,
) (models.Appointment, error) {

	return models.Appointment{}, errtypes.NotImplemented(models.Manhattan, "MakeAppointmentWithLoad")
}
