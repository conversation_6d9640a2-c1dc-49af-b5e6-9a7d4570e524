package scheduling

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/integrations/scheduling/datadocks"
	"github.com/drumkitai/drumkit/common/integrations/scheduling/daysmart"
	"github.com/drumkitai/drumkit/common/integrations/scheduling/e2open"
	"github.com/drumkitai/drumkit/common/integrations/scheduling/manhattan"
	"github.com/drumkitai/drumkit/common/integrations/scheduling/onenetwork"
	"github.com/drumkitai/drumkit/common/integrations/scheduling/opendock"
	"github.com/drumkitai/drumkit/common/integrations/scheduling/retalix"
	"github.com/drumkitai/drumkit/common/integrations/scheduling/turvo"
	"github.com/drumkitai/drumkit/common/integrations/scheduling/velostics"
	"github.com/drumkitai/drumkit/common/integrations/scheduling/yardview"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/redis"
)

type Interface interface {
	// Simple queries
	GetUser(ctx context.Context) (res models.SchedulingUser, err error)
	GetAppointment(ctx context.Context, id string) (models.Appointment, error)
	GetWarehouse(ctx context.Context, id string) (warehouse models.Warehouse, err error)
	GetAllWarehouses(
		ctx context.Context,
		opts ...models.SchedulingOption,
	) (warehouses []models.Warehouse, err error)

	// Complex queries
	GetDocks(ctx context.Context, req models.GetDocksRequest) (docks []models.Dock, err error)
	GetLoadTypes(ctx context.Context, req models.GetLoadTypesRequest) (types []models.LoadType, err error)
	GetOpenSlots(ctx context.Context, loadTypeID string, req models.GetOpenSlotsRequest) ([]models.Slot, error)

	// Appointment operations
	MakeAppointment(
		ctx context.Context,
		req models.MakeAppointmentRequest,
		opts ...models.SchedulingOption,
	) (appt models.Appointment, err error)

	MakeAppointmentWithLoad(
		ctx context.Context,
		req models.MakeAppointmentRequest,
		_ models.Load,
	) (appt models.Appointment, err error)

	CancelAppointment(ctx context.Context, id, reason string) (models.Appointment, error)

	// Validation and submission
	SubmitAppointment(
		ctx context.Context,
		poNumbers []string,
		warehouse models.Warehouse,
		lumperRequested bool,
		note string,
		opts ...models.SchedulingOption,
	) error

	ValidateAppointment(
		ctx context.Context,
		poNumbers []string,
		warehouse models.Warehouse,
		opts ...models.SchedulingOption,
	) (validatedPoNumbers []models.ValidatedPONumber, err error)

	// Onboarding
	OnboardScheduler(ctx context.Context) (resp models.OnboardSchedulerResponse, err error)
}

type CachedClient struct {
	client      Interface
	integration models.Integration
}

// GetCachedClient returns a cached scheduling client or creates a new one if needed.
func GetCachedClient(ctx context.Context, source models.Integration) (Interface, error) {
	key := fmt.Sprintf("%s-%s", source.Name, source.Username)

	cached, exists, err := redis.GetKey[*CachedClient](ctx, key)
	if err != nil {
		log.WarnNoSentry(ctx, "cached client not found", zap.Error(err))
	}

	if exists {
		if cached.isValid() {
			log.Info(
				ctx,
				"re-using cached scheduler client",
				zap.String("name", string(source.Name)),
			)

			return cached.client, nil
		}
	}

	if source.Name == models.Opendock {
		if source.AccessToken != "" && !source.NeedsRefresh() {
			client, err := opendock.NewWithToken(ctx, source)
			if err != nil {
				return nil, fmt.Errorf("creating opendock client with token: %w", err)
			}

			cached := &CachedClient{
				client:      client,
				integration: source,
			}

			if err := redis.SetKey(ctx, key, cached, 1*time.Hour); err != nil {
				log.Warn(
					ctx,
					"failed to store scheduler client in cache",
					zap.String("name", string(source.Name)),
				)
			}

			return client, nil
		}
	}

	client, err := New(ctx, source)
	if err != nil {
		return nil, fmt.Errorf("creating new client: %w", err)
	}

	cached = &CachedClient{
		client:      client,
		integration: source,
	}

	if err := redis.SetKey(ctx, key, cached, 1*time.Hour); err != nil {
		log.Info(
			ctx,
			"failed to store scheduler client in cache",
			zap.String("name", string(source.Name)),
		)
	}

	return client, nil
}

func (c *CachedClient) isValid() bool {
	return c.client != nil && c.integration.AccessToken != "" && !c.integration.NeedsRefresh()
}

func New(ctx context.Context, source models.Integration) (Interface, error) {
	switch source.Name {
	case models.DataDocks:
		return datadocks.New(ctx, source)

	case models.DaySmart:
		return daysmart.New(ctx, source)

	case models.E2open:
		return e2open.New(ctx, source)

	case models.Manhattan:
		return manhattan.New(ctx, source)

	case models.OneNetwork:
		return onenetwork.New(ctx, source)

	case models.Opendock:
		return opendock.New(ctx, source)

	case models.Retalix:
		return retalix.New(ctx, source)

	case models.Turvo:
		return turvo.New(ctx, source)

	case models.Velostics:
		return velostics.New(ctx, source)

	case models.YardView:
		return yardview.New(ctx, source)

	default:
		return nil, fmt.Errorf("unknown source %s", source.Name)
	}
}

// MustNew panics if the source aka integration name is not recognized.
func MustNew(ctx context.Context, source models.Integration) Interface {
	result, err := New(ctx, source)
	if err != nil {
		panic(err)
	}

	return result
}
