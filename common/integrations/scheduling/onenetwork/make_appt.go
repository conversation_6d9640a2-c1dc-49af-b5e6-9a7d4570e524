package onenetwork

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/models"
)

type (
	Contact struct {
		Phone string `json:"phone"`
		Email string `json:"email"`
	}

	Warehouse struct {
		Name     string `json:"name"`
		City     string `json:"city"`
		State    string `json:"state"`
		ZipCode  string `json:"zipCode"`
		Country  string `json:"country"`
		StopType string `json:"stopType"`
	}

	MakeAppointmentRequestData struct {
		RefNo         string    `json:"refNo"`
		ShipNo        []string  `json:"shipNo" validate:"required"`
		ScheduledTime string    `json:"scheduledTime" validate:"required"`
		Duration      int       `json:"duration" validate:"required"`
		Notes         string    `json:"notes"`
		Status        string    `json:"status" validate:"required"`
		Contact       Contact   `json:"contact"`
		Warehouse     Warehouse `json:"warehouse"`
	}

	MakeAppointmentRequest struct {
		models.CyclopsBaseRequest
		Appointments []MakeAppointmentRequestData `json:"appointments"`
	}

	MakeAppointmentResponse struct {
		models.CyclopsBaseResponse
		Appointment []models.CyclopsAppointment `json:"appointment"`
	}
)

func (o *OneNetwork) MakeAppointmentWithLoad(
	_ context.Context,
	_ models.MakeAppointmentRequest,
	_ models.Load,
) (models.Appointment, error) {

	return models.Appointment{}, errtypes.NotImplemented(models.OneNetwork, "MakeAppointmentWithLoad")
}

func (o *OneNetwork) MakeAppointment(
	ctx context.Context,
	req models.MakeAppointmentRequest,
	_ ...models.SchedulingOption,
) (models.Appointment, error) {

	return o.MakeAppointmentWithCyclops(ctx, req)
}

func (o *OneNetwork) MakeAppointmentWithCyclops(
	ctx context.Context,
	req models.MakeAppointmentRequest,
) (models.Appointment, error) {

	cyclopsReq := MakeAppointmentRequest{
		CyclopsBaseRequest: models.CyclopsBaseRequest{
			Integration: models.CyclopsSchedulingIntegration,
			Platform:    OneNetworkPlatform,
			Action:      models.ActionMakeAppointment,
			UserID:      o.scheduler.Username,
			Credentials: models.CyclopsCredentials{
				Username: o.creds.Username,
				Password: o.creds.Password,
			},
		},
		Appointments: []MakeAppointmentRequestData{
			{
				ShipNo:        []string{req.LoadTypeID},
				ScheduledTime: req.StartTime.Format("2006-01-02T15:04:05.000Z07:00"),
				Duration:      60,
				Notes:         req.Notes,
				Status:        "Scheduled",
				Contact: Contact{
					Phone: req.ContactDetails.Phone,
					Email: req.ContactDetails.Email,
				},
			},
		},
	}

	reqBody, err := json.Marshal(cyclopsReq)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to marshal request: %w", err)
	}

	url, err := helpers.GetCyclopsURL()
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to get url: %w", err)
	}

	httpReq, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		url,
		bytes.NewBuffer(reqBody),
	)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Accept", "application/json")

	resp, err := otel.TracingHTTPClient().Do(httpReq)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to read response: %w", err)
	}

	var res MakeAppointmentResponse
	if err = json.Unmarshal(body, &res); err != nil {
		return models.Appointment{}, fmt.Errorf("failed to unmarshal body: %w", err)
	}

	if !res.Success {
		return models.Appointment{}, &models.CyclopsError{
			Message: res.Message,
			Errors:  res.Errors,
		}
	}

	return convertToAppointments(res.Appointment)
}
