package onenetwork

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	warehouseDB "github.com/drumkitai/drumkit/common/rds/warehouses"
	"github.com/drumkitai/drumkit/common/redis"
)

type (
	GetSlotsRequest struct {
		models.CyclopsGetSlotsRequest
		PONums []string `json:"shipNo"`
	}
)

func (o *OneNetwork) GetOpenSlotsWithCyclops(
	ctx context.Context,
	loadTypeID string,
	req models.GetOpenSlotsRequest,
) ([]models.Slot, error) {

	cyclopsReq := GetSlotsRequest{
		CyclopsGetSlotsRequest: models.CyclopsGetSlotsRequest{
			CyclopsBaseRequest: models.CyclopsBaseRequest{
				Integration: models.CyclopsSchedulingIntegration,
				Platform:    OneNetworkPlatform,
				Action:      models.ActionGetOpenSlots,
				UserID:      o.scheduler.Username,
				Credentials: models.CyclopsCredentials{
					Username: o.creds.Username,
					Password: o.creds.Password,
				},
			},
			StartDate: req.Start.Format("2006-01-02T15:04:05.0000Z"),
			EndDate:   req.End.Format("2006-01-02T15:04:05.0000Z"),
		},
		PONums: []string{loadTypeID},
	}

	reqBody, err := json.Marshal(cyclopsReq)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	url, err := helpers.GetCyclopsURL()
	if err != nil {
		return nil, fmt.Errorf("failed to get url: %w", err)
	}

	httpReq, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		url,
		bytes.NewBuffer(reqBody),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Accept", "application/json")

	resp, err := otel.TracingHTTPClient().Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	var res models.CyclopsGetSlotsResponse
	if err = json.Unmarshal(body, &res); err != nil {
		return nil, fmt.Errorf("failed to unmarshal body: %w", err)
	}

	if !res.Success {
		return nil, &models.CyclopsError{
			Message: res.Message,
			Errors:  res.Errors,
		}
	}

	return convertToSlots(ctx, loadTypeID, req.RequestType, res.Appointments), nil
}

func convertToSlots(
	ctx context.Context,
	loadTypeID string,
	requestType models.RequestType,
	appointments []models.CyclopsAppointmentData,
) []models.Slot {

	ctx = log.With(ctx, zap.String("source", string(models.OneNetworkSource)))

	slots := make([]models.Slot, 0)
	for _, appt := range appointments {
		if appt.Status != "AVAILABLE" || appt.Warehouse == nil {
			continue
		}

		var times []time.Time
		for _, slot := range appt.Warehouse.OpenSlots {
			t, err := time.Parse("2006-01-02T15:04:05", slot.ScheduledTime)
			if err != nil {
				log.Infof(
					ctx,
					"Invalid time format for OneNetwork warehouse, ScheduledTime %s: %v",
					appt.Warehouse.Name,
					slot.ScheduledTime,
					zap.Error(err),
				)

				continue
			}
			times = append(times, t)
		}

		if len(times) > 0 {
			slot := models.Slot{
				Dock: models.Dock{
					ID: appt.Warehouse.Name,
				},
				StartTimes: times,
			}
			slots = append(slots, slot)
		}

		wh := models.Warehouse{
			Source:                  models.OneNetworkSource,
			WarehouseID:             appt.Warehouse.Name,
			WarehouseName:           appt.Warehouse.Name,
			WarehouseFullIdentifier: appt.Warehouse.Name,
		}

		// TODO: associate warehouse models with services
		err := warehouseDB.Upsert(ctx, &wh)
		if err != nil {
			log.Warn(
				ctx,
				"error saving warehouse",
				zap.String("name", appt.Warehouse.Name),
				zap.Error(err),
			)
		}

		key := fmt.Sprintf("%s-%s-%s", models.OneNetworkSource, loadTypeID, requestType)
		if err := redis.SetKey(ctx, key, wh, 24*time.Hour); err != nil {
			log.WarnNoSentry(
				ctx,
				"error setting warehouse in redis",
				zap.String("name", wh.WarehouseName),
				zap.Error(err),
			)
		}
	}

	return slots
}
