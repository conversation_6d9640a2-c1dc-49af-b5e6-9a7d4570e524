package llm

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/integrations/llm/braintrustsdk"
	"github.com/drumkitai/drumkit/common/integrations/llm/extractor"
	"github.com/drumkitai/drumkit/common/integrations/llm/openai"
	"github.com/drumkitai/drumkit/common/integrations/llm/textract"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/prompts"
)

//nolint:lll
type (
	IsBatchQuoteRequestOutput struct {
		IsBatch bool `json:"is_batch" jsonschema_description:"True if the email contains multiple quote requests making it a batch quote. False if the email only has one quote request."`
	}

	BatchQuoteRequestOutput struct {
		QuoteRequests []QuoteRequest `json:"quote_requests" jsonschema_description:"Collection of quote requests extracted from the email content."`
	}
)

func ExtractBatchQuoteRequestSuggestions(
	ctx context.Context,
	email models.Email,
	openaiService openai.Service,
	textractClient textract.Client,
	rds RDSInterface,
	opts ...Option,
) (res []models.QuoteRequest, err error) {

	attrs := otel.EmailAttrs(&email)
	ctx, metaSpan := otel.StartSpan(ctx, "ExtractBatchQuoteRequestSuggestions", attrs)
	defer func() { metaSpan.End(err) }()

	var quoteRequestsFromAttachments []models.QuoteRequest

	// Collect quote requests from PDFs
	if email.HasPDFs {
		// TODO: batch quote specific logic for extracting quote requests from attachments support spreadsheets etc
		quoteRequestsFromAttachments, err = extractQRSuggestionsFromAttachments(
			ctx, email, openaiService, textractClient, rds, opts...,
		)
		if err != nil {
			return res, fmt.Errorf("LLM error extracting quote request from attachments: %w", err)
		}
	}

	if len(quoteRequestsFromAttachments) > 0 {
		return quoteRequestsFromAttachments, nil
	}

	// If no attachments OR no quotes found in attachments, try to extract quotes from email body
	quoteRequestsFromBody, err := promptBatchQuoteRequestLLM(
		ctx,
		email,
		nil,
		models.Attachment{},
		openaiService,
		false,
		rds,
		opts...,
	)

	if err != nil {
		return res, fmt.Errorf("LLM error extracting quote request info: %w", err)
	}

	return quoteRequestsFromBody, nil
}

func promptBatchQuoteRequestLLM(
	ctx context.Context,
	email models.Email,
	attachmentContent any, // nil, Markdown string or Textract
	attachment models.Attachment,
	openaiService openai.Service,
	hasAttachments bool,
	rds RDSInterface,
	opts ...Option,
) (res []models.QuoteRequest, err error) {

	options := &Options{
		Config: models.QuickQuoteConfig{},
	}

	for _, opt := range opts {
		opt(options)
	}

	integrations, err := rds.GetTMSListByServiceID(ctx, email.ServiceID)
	if err != nil {
		return res, fmt.Errorf("error getting TMS list for service: %w", err)
	}

	var tmsID uint
	if len(integrations) > 0 {
		tmsID = integrations[0].ID
	}

	// Ignore service ID 1 (Drumkit) for this error
	if len(integrations) > 1 && email.ServiceID != 1 {
		log.Warn(
			ctx,
			"multiple TMS integrations found for batch quote request service, defaulting to first",
			zap.Uint("serviceID", email.ServiceID),
			zap.Int("countTMS", len(integrations)),
		)
	}

	emailID := strconv.FormatUint(uint64(email.ID), 10)

	subject := email.Subject
	// remove subject if we've already used the subject from this thread in a previous email
	emailCount, err := rds.GetNumberOfEmailsByThreadIDAndUserID(ctx, email.ThreadID, email.UserID)
	if err != nil {
		log.WarnNoSentry(ctx, "error getting email count", zap.Error(err))
	}

	if emailCount > 1 {
		subject = ""
		log.Info(ctx, "email subject removed for thread with multiple emails", zap.Int("emailCount", emailCount))
	}

	currentYear := strconv.Itoa(time.Now().Year())

	// TODO: still need to update BatchQuoteRequestSystemPrompt to be more specific to batch quote requests
	systemPrompt := strings.ReplaceAll(prompts.BatchQuoteRequestSystemPrompt, "{CURRENT_YEAR}", currentYear)

	var content any = email.BodyWithoutSignature

	if attachmentContent != nil {
		content = attachmentContent
	}

	estTimezone, err := time.LoadLocation("America/New_York")
	if err != nil {
		log.Warn(ctx, "failed to load America/New_York location, falling back to UTC", zap.Error(err))
		estTimezone = time.UTC
	}

	userPrompt := fmt.Sprintf(
		`
		Email sent at: %s

		Subject: %s

		Email body:
		%s
		`,
		email.SentAt.In(estTimezone).Format("01/02/2006, 03:04PM"),
		subject,
		content,
	)

	response, err := openaiService.GetResponse(
		ctx,
		email,
		braintrustsdk.CreateProjectDetails(braintrustsdk.QRBeginConversation, hasAttachments),
		openai.ResponseOptions{
			DeveloperPrompt: systemPrompt,
			UserPrompt:      userPrompt,
			Schema:          extractor.GenerateSchema[QuoteRequestOutput](),
		},
	)
	if err != nil {
		return res, fmt.Errorf("error getting response from LLM: %w", err)
	}

	btQuoteConversationLogID := response.BraintrustLogID
	result, err := extractor.StructExtractor[QuoteRequestOutput](response.Content)
	if err != nil {
		return res, fmt.Errorf("failed to unmarshal JSON: %w", err)
	}

	if len(result.QuoteRequests) == 0 {
		log.WarnNoSentry(ctx, "no quote requests found for email:", zap.String("emailID", emailID))
		return res, nil
	}

	mappedCustomer, btCustomerLogID, err := promptLLMForCustomer(
		ctx,
		tmsID,
		email,
		attachmentContent,
		openaiService,
		braintrustsdk.CreateProjectDetails(braintrustsdk.QRGetCustomer, hasAttachments),
		rds,
	)
	if err != nil {
		log.WarnNoSentry(ctx, "error mapping customer", zap.Error(err))
	} else {
		log.Debug(ctx, "mapped customer", zap.Any("customer", mappedCustomer))
	}

	for _, qr := range result.QuoteRequests {
		log.Debug(ctx, "transportType", zap.String("transportType", qr.TruckType))
		stops := make([]models.Stop, len(qr.Stops))

		for i, stop := range qr.Stops {
			dateTime, err := parseDate(
				ctx,
				stop.DateTime,
				Address{
					City:  stop.Location.City,
					State: stop.Location.State,
				},
			)
			if err != nil {
				log.WarnNoSentry(ctx, "error parsing date", zap.Error(err))
			}

			stops[i] = models.Stop{
				StopType:   stop.Type,
				StopNumber: i,
				Order:      i, // For backward compatibility
				Address: models.Address{
					City:  stop.Location.City,
					State: stop.Location.State,
					Zip:   stop.Location.Zip,
				},
			}

			// Set time fields based on stop type
			switch stop.Type {
			case "pickup":
				stops[i].ReadyTime = models.NullTime{
					Time:  dateTime,
					Valid: err == nil && !dateTime.IsZero(),
				}

			case "dropoff":
				stops[i].MustDeliver = models.NullTime{
					Time:  dateTime,
					Valid: err == nil && !dateTime.IsZero(),
				}

			// For intermediate stops, use must deliver
			default:
				stops[i].MustDeliver = models.NullTime{
					Time:  dateTime,
					Valid: err == nil && !dateTime.IsZero(),
				}
			}
		}

		var firstPickup, lastDropoff *models.Stop
		for i := 0; i < len(stops); i++ {
			if stops[i].StopType == "pickup" && firstPickup == nil {
				firstPickup = &stops[i]
			}
		}
		for i := len(stops) - 1; i >= 0; i-- {
			if stops[i].StopType == "dropoff" && lastDropoff == nil {
				lastDropoff = &stops[i]
			}
		}

		var pickupLocation, dropoffLocation models.Address
		var pickupDate, dropoffDate models.NullTime
		if firstPickup != nil {
			pickupLocation = firstPickup.Address
			pickupDate = firstPickup.ReadyTime
		}
		if lastDropoff != nil {
			dropoffLocation = lastDropoff.Address
			dropoffDate = lastDropoff.MustDeliver
		}

		if pickupLocation == (models.Address{}) && dropoffLocation == (models.Address{}) {
			log.Debug(ctx, "Skipping quote request with no pickup or dropoff location in stops")
			continue
		}

		if !isValidQuoteRequest(ctx, userPrompt, &pickupLocation, &dropoffLocation) {
			log.Info(ctx, "LLM hallucinated, skipping quote request",
				zap.Any("pickup", pickupLocation),
				zap.Any("dropoff", dropoffLocation))
			continue
		}

		transportType := validateTransportType(
			ctx,
			email,
			attachmentContent,
			models.TransportType(qr.TruckType),
			options.Config,
		)

		suggestedRequest := models.QuoteLoadInfo{
			TransportType:    transportType,
			PickupLocation:   pickupLocation,
			PickupDate:       pickupDate,
			DeliveryLocation: dropoffLocation,
			DeliveryDate:     dropoffDate,
			Stops:            stops,
		}
		if mappedCustomer != nil {
			suggestedRequest.CustomerID = mappedCustomer.ID
			suggestedRequest.Customer = mappedCustomer.CompanyCoreInfo
		}

		res = append(res, models.QuoteRequest{
			UserID:           email.UserID,
			EmailID:          email.ID,
			ThreadID:         email.ThreadID,
			ServiceID:        email.ServiceID,
			RawLLMOutput:     suggestedRequest, // This is the LLM output
			SuggestedRequest: suggestedRequest, // This may be edited by our smart suggestion engine
			Status:           models.Pending,
			Attachment:       attachment,
			SourceCategory:   models.EmailSourceCategory,
			SourceExternalID: email.ExternalID,
			BraintrustLogIDs: createBraintrustQRLogRecordList(btQuoteConversationLogID, btCustomerLogID),
		})
	}

	return res, nil
}

// IsBatchQuoteRequestEmail returns true if an email is identified as containing 2 or more quote requests.
//
// Future consideration: An alternative approach is to first ask LLM to break up a multiple QRs/loads in the email body
// into multiple chunks, then run ExtractQuoteRequestSuggestions on each chunk.
// This requires better HTML/Markdown parsing and is currently out of scope.
func IsBatchQuoteRequestEmail(ctx context.Context, email models.Email) (bool, error) {

	openaiService, err := openai.NewService(ctx)
	if err != nil {
		return false, fmt.Errorf("error initializing OpenAI service: %w", err)
	}

	userPrompt := fmt.Sprintf(
		`
		Here is this email's data:
			Subject: %s
			Email Body Type: %s
			Email Body: %s
		`, email.Subject, email.BodyType, email.BodyWithoutSignature)

	response, err := openaiService.GetResponse(
		ctx,
		models.Email{},
		braintrustsdk.CreateProjectDetails(braintrustsdk.QRIsMultiQREmail, false),
		openai.ResponseOptions{
			DeveloperPrompt: prompts.IsBatchQuoteRequestSystemPrompt,
			UserPrompt:      userPrompt,
			Schema:          extractor.GenerateSchema[IsBatchQuoteRequestOutput](),
		},
	)
	if err != nil {
		return false, fmt.Errorf("error getting response: %w", err)
	}

	result, err := extractor.StructExtractor[IsBatchQuoteRequestOutput](response.Content)
	if err != nil {
		return false, fmt.Errorf("failed to parse LLM response as JSON: %w; content: %s", err, response.Content)
	}

	return result.IsBatch, nil
}
