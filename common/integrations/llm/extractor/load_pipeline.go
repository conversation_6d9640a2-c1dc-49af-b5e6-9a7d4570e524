package extractor

import (
	"context"
	"fmt"
	"strings"
	"sync"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers"
	email_helpers "github.com/drumkitai/drumkit/common/helpers/emails"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3fetcher"
	"github.com/drumkitai/drumkit/common/integrations/llm/braintrustsdk"
	"github.com/drumkitai/drumkit/common/integrations/llm/openai"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

// ExtractLoadInfoFromAttachments processes attachments using the OpenAI chat completions API.
func ExtractLoadInfoFromAttachments(
	ctx context.Context,
	email models.Email,
	openaiService openai.Service,
	tms models.Integration,
	options Options,
) (results []models.SuggestedLoadChange, err error) {

	attrs := otel.EmailAttrs(&email)
	ctx, metaSpan := otel.StartSpan(ctx, "extractSuggestionsFromAttachmentsWithAssistant", attrs)
	defer func() { metaSpan.End(err) }()

	if len(email.Attachments) == 0 || !email.HasPDFs {
		return results, nil
	}

	// Rate contracts may be in separate attachments, so we need to map them to the correct load
	mapRateConfirmations := map[string]models.RateData{}
	var mu sync.Mutex
	var wg sync.WaitGroup
	const maxConcurrent = 3
	sem := make(chan struct{}, maxConcurrent)

	resultChan := make(chan models.SuggestedLoadChange, len(email.Attachments))

	// create S3 client for fetching attachments
	s3Client, err := s3fetcher.New(ctx)
	if err != nil {
		log.Error(ctx, "error creating S3 client", zap.Error(err))
		return results, err
	}

	for _, attachment := range email.Attachments {
		sem <- struct{}{}
		wg.Add(1)

		go func(attachment models.Attachment) {
			defer func() {
				<-sem
				wg.Done()
			}()

			ctx = log.With(
				ctx,
				zap.String("attachmentName", attachment.OriginalFileName),
				zap.String("attachmentURL", attachment.S3URL),
			)

			if !email_helpers.IsPDF(attachment.MimeType, attachment.OriginalFileName) {
				log.Info(ctx, "skipping non-PDF attachment")
				return
			}

			// download the attachment
			fileData, err := s3Client.FetchObjectFromS3(ctx, attachment.S3URL)
			if err != nil {
				log.WarnNoSentry(ctx, "error fetching file from S3. skipping attachment.", zap.Error(err))
				return
			}

			markdown, err := GetZeroxMarkdown(ctx, attachment.OriginalFileName, fileData)
			if err != nil {
				log.WarnNoSentry(
					ctx,
					"error getting zerox markdown, falling back to gofitz",
					zap.Error(err),
				)

				// Fallback to using GoFitz conversion when Zerox fails
				markdown, err = S3PDFToMarkdown(fileData)
				if err != nil || strings.TrimSpace(markdown) == "" {
					log.Error(ctx, "gofitz conversion to markdown failed", zap.Error(err))
					return
				}
			}

			userPrompt := fmt.Sprintf(
				"<email_sender>%s</email_sender>\n"+
					"<email_subject>%s</email_subject>\n"+
					"<email_body>%s</email_body>\n"+
					"<pdf_content>%s</pdf_content>",
				email.Sender,
				email.Subject,
				email.Body,
				markdown,
			)

			suggestion := processUserPromptWithExtractors(
				ctx,
				openaiService,
				tms,
				email,
				userPrompt,
				&attachment,
				true,
				options,
			)

			// Generate and store embedding for the load building attachment content
			func() {
				embedding, embErr := openaiService.GetEmbedding(ctx, markdown)
				if embErr != nil {
					log.WarnNoSentry(ctx, "Failed to generate embedding for load building attachment",
						zap.Error(embErr),
						zap.String("fileName", attachment.OriginalFileName))
					return
				}

				vectorRepo := rds.GetVectorRepository(ctx)
				if vectorRepo == nil {
					return
				}

				embErr = vectorRepo.StoreAttachmentEmbedding(
					ctx,
					&email,
					attachment.ExternalID,
					embedding,
				)
				if embErr != nil {
					log.WarnNoSentry(ctx, "Failed to store load building attachment embedding",
						zap.Error(embErr),
						zap.String("fileName", attachment.OriginalFileName))
					return
				}

				log.Info(ctx, "Successfully stored load building attachment embedding",
					zap.String("fileName", attachment.OriginalFileName))
			}()

			// Handle Common Special Case: MJB includes rate confirmations in separate attachments.
			// Instead of duplicating suggestions, map the rate con to its order
			if check := checkForStringInUserPrompt("Rate Confirmation", userPrompt); check {
				if check := checkForStringInUserPrompt("MJB", userPrompt); check {
					refKey := suggestion.Suggested.LoadChanges.Customer.RefNumber
					if refKey != "" {
						mu.Lock()
						mapRateConfirmations[refKey] = suggestion.Suggested.LoadChanges.RateData
						mu.Unlock()

						return
					}
				}
			}

			// Skip suggestion if both pickup and dropoff address are missing to reduce false positives
			if IsInsufficientAddressData(suggestion.Suggested.LoadChanges.Pickup.CompanyCoreInfo) &&
				IsInsufficientAddressData(suggestion.Suggested.LoadChanges.Consignee.CompanyCoreInfo) {
				log.Info(
					ctx,
					"Skipping suggestion missing both pickup and dropoff addresses",
					zap.String("attachmentURL", attachment.S3URL),
				)
				return
			}

			mu.Lock()
			defer mu.Unlock()
			resultChan <- suggestion
		}(attachment)
	}

	// Start a goroutine to close the channel once all processing is done.
	go func() {
		wg.Wait()
		close(resultChan)
	}()

	// Then consume from the channel
	for result := range resultChan {
		// Ensure all results have properly initialized LoadChanges
		if result.Suggested.LoadChanges == nil {
			result.Suggested.LoadChanges = &models.LoadChanges{
				Pickup: models.Pickup{
					CompanyCoreInfo: models.CompanyCoreInfo{},
				},
				Consignee: models.Consignee{
					CompanyCoreInfo: models.CompanyCoreInfo{},
				},
				Customer: models.Customer{
					CompanyCoreInfo: models.CompanyCoreInfo{},
				},
				RateData:       models.RateData{},
				Specifications: models.SuggestedSpecifications{},
			}
		}

		results = append(results, result)
	}

	// Apply rate confirmations if found
	if len(mapRateConfirmations) > 0 {
		log.Infof(ctx, "Found rate confirmations: %#v", mapRateConfirmations)

		for i := range results {
			sug := results[i].Suggested.LoadChanges
			// If found, map the rate confirmation data to the suggested load data
			if rateCon, ok := mapRateConfirmations[sug.Customer.RefNumber]; ok {
				log.Infof(ctx, "matched rate confirmation for %s", sug.Customer.RefNumber)
				sug.RateData.CustomerLineHaulRate = helpers.Or(
					rateCon.CustomerLineHaulRate, sug.RateData.CustomerLineHaulRate,
				)
				sug.RateData.CustomerLineHaulCharge = helpers.Or(
					rateCon.CustomerLineHaulCharge, sug.RateData.CustomerLineHaulCharge,
				)
				sug.RateData.CustomerRateType = helpers.Or(
					rateCon.CustomerRateType, sug.RateData.CustomerRateType,
				)
			}
			results[i].Suggested.LoadChanges = sug
		}
	}

	return results, nil
}

// ExtractLoadInfoFromEmailBody extracts load information from email body using the OpenAI chat completions API.
func ExtractLoadInfoFromEmailBody(
	ctx context.Context,
	email models.Email,
	openaiService openai.Service,
	tms models.Integration,
	options Options,
) (result models.SuggestedLoadChange, err error) {

	ctx, metaSpan := otel.StartSpan(ctx, "extractLoadInfoFromEmailBody", otel.EmailAttrs(&email))
	defer func() { metaSpan.End(err) }()

	// Initialize result with empty structures to avoid nil pointer dereferences
	result.Suggested.LoadChanges = &models.LoadChanges{
		Pickup: models.Pickup{
			CompanyCoreInfo: models.CompanyCoreInfo{},
		},
		Consignee: models.Consignee{
			CompanyCoreInfo: models.CompanyCoreInfo{},
		},
		Customer: models.Customer{
			CompanyCoreInfo: models.CompanyCoreInfo{},
		},
		RateData:       models.RateData{},
		Specifications: models.SuggestedSpecifications{},
	}

	userPrompt := fmt.Sprintf(
		"<email_sender>%s</email_sender>\n"+
			"<email_subject>%s</email_subject>\n"+
			"<email_body>%s</email_body>",
		email.Sender,
		email.Subject,
		email.Body,
	)

	result = processUserPromptWithExtractors(
		ctx,
		openaiService,
		tms,
		email,
		userPrompt,
		nil,
		false,
		options,
	)

	return result, nil
}

// processUserPromptWithExtractors runs all extraction functions on the user prompt.
func processUserPromptWithExtractors(
	ctx context.Context,
	openaiService openai.Service,
	tms models.Integration,
	email models.Email,
	userPrompt string,
	attachment *models.Attachment,
	hasAttachment bool,
	options Options,
) models.SuggestedLoadChange {

	ctx, metaSpan := otel.StartSpan(ctx, "processUserPromptWithExtractors", otel.EmailAttrs(&email))
	defer func() { metaSpan.End(nil) }()

	// Truncate the user prompt to 3000 tokens to avoid hitting the context length limit - 128k tokens is 4.0 mini max.
	userPrompt = helpers.TokenTruncater(userPrompt, 3000)

	customer, rawLLMCustomer, initialResponseID, btCustomerLogID, err := extractCustomer(
		ctx,
		openaiService,
		tms.ID,
		email,
		userPrompt,
		braintrustsdk.CreateProjectDetails(braintrustsdk.LBGetCustomer, hasAttachment),
	)
	if err != nil {
		logFn := log.Error
		if tms.ID == 0 {
			logFn = log.WarnNoSentry
		}

		logFn(ctx, "Error extracting customer info", zap.Error(err))
	}

	if customer == nil {
		log.WarnNoSentry(ctx, "no customer found")
		customer = &models.TMSCustomer{}
	}

	// Normally in conversation state, each response is associated with the previous response ID. However,
	// we only care about getting the shipment information into the LLM context. That is done in the first extraction.
	// So we will now re-use this responseID for all subsequent extractions.
	// If we don't do this, the LLM context will get too large and polluted with data about previous extractions.
	// Meaning we will start seeing hallucinations, hitting context length limits and hitting
	// rate limits (TPM specifically).
	var shipmentContextID string
	if initialResponseID != "" {
		shipmentContextID = initialResponseID
	}

	basicInfo, rawLLMBasicInfo, btBasicInfoLogID, err := extractBasicInfo(
		ctx,
		openaiService,
		email,
		tms,
		customer.ID,
		shipmentContextID,
		userPrompt,
		braintrustsdk.CreateProjectDetails(braintrustsdk.LBGetBasicInfo, hasAttachment),
	)
	if err != nil {
		basicInfo, rawLLMBasicInfo = handleExtractionError(ctx, err, "basic info", BasicInfo{})
	}

	pickup, rawLLMPickup, btPickupLogID, err := extractPickup(
		ctx,
		openaiService,
		email,
		tms,
		customer.ID,
		shipmentContextID,
		braintrustsdk.CreateProjectDetails(braintrustsdk.LBGetPickup, hasAttachment),
	)
	if err != nil {
		pickup, rawLLMPickup = handleExtractionError(ctx, err, "pickup info", PickupInfo{})
	}

	consignee, rawLLMConsignee, btConsigneeLogID, err := extractConsignee(
		ctx,
		openaiService,
		email,
		tms,
		customer.ID,
		shipmentContextID,
		braintrustsdk.CreateProjectDetails(braintrustsdk.LBGetConsignee, hasAttachment),
	)
	if err != nil {
		consignee, rawLLMConsignee = handleExtractionError(ctx, err, "consignee info", ConsigneeInfo{})
	}

	rateData, rawLLMRateData, btRateDataLogID, err := extractRateData(
		ctx,
		openaiService,
		email,
		tms,
		customer.ID,
		shipmentContextID,
		braintrustsdk.CreateProjectDetails(braintrustsdk.LBGetRateData, hasAttachment),
	)
	if err != nil {
		rateData, rawLLMRateData = handleExtractionError(ctx, err, "rate data", RateDataInfo{})
	}

	specs, rawLLMSpecs, btSpecsLogID, err := extractSpecifications(
		ctx,
		openaiService,
		email,
		tms,
		customer.ID,
		shipmentContextID,
		userPrompt,
		braintrustsdk.CreateProjectDetails(braintrustsdk.LBGetSpecifications, hasAttachment),
		options,
	)
	if err != nil {
		specs, rawLLMSpecs = handleExtractionError(ctx, err, "specifications", SpecificationsInfo{})
	}

	return createSuggestionFromExtractedData(
		ctx,
		email,
		basicInfo, rawLLMBasicInfo,
		customer, rawLLMCustomer,
		rateData, rawLLMRateData,
		pickup, rawLLMPickup,
		consignee, rawLLMConsignee,
		specs, rawLLMSpecs,
		attachment,
		createBraintrustLBLogRecordList(
			btCustomerLogID,
			btBasicInfoLogID,
			btPickupLogID,
			btConsigneeLogID,
			btRateDataLogID,
			btSpecsLogID,
		),
	)
}

// Helper function to create a full suggestion from the extracted data
func createSuggestionFromExtractedData(
	ctx context.Context,
	email models.Email,
	basicInfo BasicInfo,
	rawLLMBasicInfo BasicInfo,
	customer *models.TMSCustomer,
	rawLLMCustomer CustomerInfo,
	rateData RateDataInfo,
	rawLLMRateData RateDataInfo,
	pickup PickupInfo,
	rawLLMPickup PickupInfo,
	consignee ConsigneeInfo,
	rawLLMConsignee ConsigneeInfo,
	specs SpecificationsInfo,
	rawLLMSpecs SpecificationsInfo,
	attachment *models.Attachment,
	braintrustLogRecordList models.LogRecordList,
) models.SuggestedLoadChange {

	_, metaSpan := otel.StartSpan(ctx, "createSuggestionFromExtractedData", otel.EmailAttrs(&email))
	defer func() { metaSpan.End(nil) }()

	result := models.SuggestedLoadChange{}

	// Initialize all fields to avoid nil pointer dereferences
	result.Suggested.LoadChanges = &models.LoadChanges{
		// Default values
		Mode:             models.TLMode,
		MoreThanTwoStops: basicInfo.MoreThanTwoStops,
		Customer: models.Customer{
			CompanyCoreInfo: models.CompanyCoreInfo{},
			RefNumber:       basicInfo.RefNumber,
		},
		RateData: models.RateData{},
		Pickup: models.Pickup{
			CompanyCoreInfo: models.CompanyCoreInfo{},
		},
		Consignee: models.Consignee{
			CompanyCoreInfo: models.CompanyCoreInfo{},
		},
		Specifications: models.SuggestedSpecifications{},
	}

	result.Suggested.RawLLMOutput = &models.LoadChanges{
		// Default values
		Mode:             models.TLMode,
		MoreThanTwoStops: rawLLMBasicInfo.MoreThanTwoStops,
		Customer: models.Customer{
			RefNumber: rawLLMBasicInfo.RefNumber,
		},
		RateData: models.RateData{},
		Pickup: models.Pickup{
			CompanyCoreInfo: models.CompanyCoreInfo{},
		},
		Consignee: models.Consignee{
			CompanyCoreInfo: models.CompanyCoreInfo{},
		},
		Specifications: models.SuggestedSpecifications{},
	}

	if customer != nil {
		// Final Output
		result.Suggested.LoadChanges.Customer.CompanyCoreInfo = customer.CompanyCoreInfo

		// Raw LLM Output
		result.Suggested.RawLLMOutput.Customer.Name = rawLLMCustomer.ShipperName
		result.Suggested.RawLLMOutput.Customer.Email = rawLLMCustomer.OriginalSenderEmail
	}

	// Raw LLM Output
	result.Suggested.RawLLMOutput.RateData.CollectionMethod = rawLLMRateData.RateData.CollectionMethod
	result.Suggested.RawLLMOutput.RateData.CustomerRateType = rawLLMRateData.RateData.CustomerRateType
	result.Suggested.RawLLMOutput.RateData.CustomerLineHaulRate = rawLLMRateData.RateData.CustomerLineHaulRate
	result.Suggested.RawLLMOutput.RateData.RevenueCode = rawLLMRateData.RateData.RevenueCode

	result.Suggested.RawLLMOutput.Pickup = rawLLMPickup.Pickup
	result.Suggested.RawLLMOutput.Consignee = rawLLMConsignee.Consignee
	result.Suggested.RawLLMOutput.Specifications = rawLLMSpecs.Specifications

	// Final Output
	result.Suggested.LoadChanges.RateData.CollectionMethod = rateData.RateData.CollectionMethod
	result.Suggested.LoadChanges.RateData.CustomerRateType = rateData.RateData.CustomerRateType
	result.Suggested.LoadChanges.RateData.CustomerLineHaulRate = rateData.RateData.CustomerLineHaulRate
	result.Suggested.LoadChanges.RateData.CustomerLineHaulCharge = rateData.RateData.CustomerLineHaulCharge
	result.Suggested.LoadChanges.RateData.RevenueCode = rateData.RateData.RevenueCode

	result.Suggested.LoadChanges.Pickup = pickup.Pickup
	result.Suggested.LoadChanges.Consignee = consignee.Consignee
	result.Suggested.LoadChanges.Specifications = specs.Specifications

	tempLoad := &models.Load{
		LoadCoreInfo: models.LoadCoreInfo{
			Pickup:    pickup.Pickup,
			Consignee: consignee.Consignee,
		},
	}

	//nolint:lll
	// TODO(https://linear.app/drumkit/issue/ENG-4059/refactor-and-unify-datetime-handling-for-ai-suggestions):
	//
	// This date-handling mechanism is technical debt that needs to be resolved.
	//
	// The core issue is a mismatch between two different "PickupDate" concepts:
	//
	// 1. `LoadCoreInfo.PickupDate`: This is a true `date` type that maps to a `date`-only column in the `loads` table.
	//    It is correctly populated by the `UpdateDates()` function, which *intentionally strips the time component* for reporting and filtering.
	//
	// 2. `SuggestedLoadChanges.PickupDate`: This is a `NullTime` field inside a JSON blob. It is *required* to hold the full `datetime`
	//    so that the front-end can display the exact suggested appointment time. It is incorrectly named and should be `PickupDateTime`.
	//
	// This function uses the `UpdateDates()` method as a pragmatic, short-term solution to populate the date field for the UI,
	// knowingly discarding the time. This is acceptable for now because the `LoadBuildingCard` on the front-end currently only displays
	// the date anyway.
	//
	// The proper long-term fix is a coordinated effort (outlined in a separate ticket) to:
	//  a) Rename the `PickupDate` and `DropoffDate` fields in `SuggestedLoadChanges` to `PickupDateTime` and `DropoffDateTime`.
	//  b) Update all front-end consumers (`vesta`, `vulcan`, etc.) to use these new, correctly-named fields.
	//  c) Refactor this pipeline and the `quote_request` pipeline to use a single, unified approach for date/time handling.
	err := tempLoad.UpdateDates()
	if err != nil {
		log.WarnNoSentry(ctx, "error updating load dates", zap.Error(err))
	}

	result.Suggested.LoadChanges.PickupDate = models.NullTime{
		Time:  tempLoad.PickupDate.ToTime(),
		Valid: !tempLoad.PickupDate.IsZero(),
	}
	result.Suggested.LoadChanges.DropoffDate = models.NullTime{
		Time:  tempLoad.DropoffDate.ToTime(),
		Valid: !tempLoad.DropoffDate.IsZero(),
	}

	// Metadata
	result.Account = email.Account
	result.ServiceID = email.ServiceID
	result.EmailID = email.ID
	result.ThreadID = email.ThreadID
	result.Category = models.LoadBuilding
	result.Pipeline = models.LoadBuildingPipeline
	result.Suggested.Pipeline = models.LoadBuildingPipeline
	result.Status = models.Pending
	result.BraintrustLogIDs = braintrustLogRecordList
	if attachment != nil {
		result.S3Attachment = *attachment
	}

	return result
}

// handleExtractionError logs extraction errors appropriately based on error type
// and returns default values for the extraction result
func handleExtractionError[T any](
	ctx context.Context,
	err error,
	extractionName string,
	defaultValue T,
) (T, T) {

	errMsg := err.Error()
	switch {
	case strings.Contains(errMsg, "OpenAI API timeout") || strings.Contains(errMsg, "HTTP request timed out"):
		log.WarnNoSentry(ctx, "Error extracting "+extractionName, zap.Error(err))
	case strings.Contains(errMsg, "failed to unmarshal JSON"):
		// Special case for JSON unmarshal errors
		log.ErrorNoSentry(ctx, "Error extracting "+extractionName, zap.Error(err))
	default:
		log.Error(ctx, "Error extracting "+extractionName, zap.Error(err))
	}

	return defaultValue, defaultValue
}

func createBraintrustLBLogRecordList(
	btCustomerLogID string,
	btBasicInfoLogID string,
	btPickupLogID string,
	btConsigneeLogID string,
	btRateDataLogID string,
	btSpecsLogID string,
) models.LogRecordList {
	var recordList models.LogRecordList

	if btCustomerLogID != "" {
		recordList = append(recordList, models.LogRecord{
			ID:              btCustomerLogID,
			ProjectStepName: string(braintrustsdk.LBGetCustomer),
		})
	}

	if btBasicInfoLogID != "" {
		recordList = append(recordList, models.LogRecord{
			ID:              btBasicInfoLogID,
			ProjectStepName: string(braintrustsdk.LBGetBasicInfo),
		})
	}

	if btPickupLogID != "" {
		recordList = append(recordList, models.LogRecord{
			ID:              btPickupLogID,
			ProjectStepName: string(braintrustsdk.LBGetPickup),
		})
	}

	if btConsigneeLogID != "" {
		recordList = append(recordList, models.LogRecord{
			ID:              btConsigneeLogID,
			ProjectStepName: string(braintrustsdk.LBGetConsignee),
		})
	}

	if btRateDataLogID != "" {
		recordList = append(recordList, models.LogRecord{
			ID:              btRateDataLogID,
			ProjectStepName: string(braintrustsdk.LBGetRateData),
		})
	}

	if btSpecsLogID != "" {
		recordList = append(recordList, models.LogRecord{
			ID:              btSpecsLogID,
			ProjectStepName: string(braintrustsdk.LBGetSpecifications),
		})
	}

	return recordList
}
