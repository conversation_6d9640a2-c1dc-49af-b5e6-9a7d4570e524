package aljex

import (
	"context"
	"fmt"
	"regexp"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/timezone"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/sentry"
)

type (
	LoadData struct {
		Status         StringValue `json:"status"`
		OutPalletCount FloatValue  `json:"outPalletCount"`
		PONums         StringValue `json:"poNums"`
		Operator       StringValue `json:"operator"`

		AdditionalReferences models.AdditionalReferences `json:"additionalReferences"`

		TotalPieces    FloatValue      `json:"totalPieces,omitempty"`
		TotalWeight    FloatValue      `json:"totalWeight,omitempty"`
		EquipmentType  StringValue     `json:"equipmentType"`
		Commodities    []CommodityItem `json:"commodities,omitempty"`
		Carrier        Carrier         `json:"carrier"`
		Customer       Customer        `json:"customer"`
		BillTo         BillTo          `json:"billTo"`
		PickUp         PickUp          `json:"pickUp"`
		Consignee      Consignee       `json:"consignee"`
		RateData       RateData        `json:"rateData"`
		Specifications Specifications  `json:"specifications"`
		Notes          []Note          `json:"notes"`
	}
	CommodityItem struct {
		Pieces       StringValue `json:"pieces"`
		FreightClass StringValue `json:"freightClass"`
		Weight       StringValue `json:"weight"`
		Length       StringValue `json:"length"`
		Width        StringValue `json:"width"`
		Height       StringValue `json:"height"`
		ProductCode  StringValue `json:"productCode"`
		Description  StringValue `json:"description"`
	}
	Carrier struct {
		Carrier          StringValue `json:"carrier"`
		Dot              StringValue `json:"dot#"`
		Mc               StringValue `json:"mc#"`
		Scac             StringValue `json:"scac"`
		Seal             StringValue `json:"seal#"`
		Phone            StringValue `json:"phone"`
		Email            StringValue `json:"email"`
		FirstDriver      StringValue `json:"ref/driver"`
		FirstDriverCell  StringValue `json:"firstDriverCell"`
		SecondDriver     StringValue `json:"2ndRef/Driver"`
		SecondDriverCell StringValue `json:"secondDriverCell"`
		DispCity         StringValue `json:"dispCity"`
		DispState        StringValue `json:"dispState"`
		Dispatcher       StringValue `json:"dispatcher"`
		Trailer          StringValue `json:"trailer#"`
		Truck            StringValue `json:"truck#"`
		ConfSentDate     StringValue `json:"confSentDate"`
		ConfSentTime     StringValue `json:"confSentTime"`
		ConfRecdDate     StringValue `json:"confRecdDate"`
		ConfRecdTime     StringValue `json:"confRecdTime"`
		DispatchedDate   StringValue `json:"dispatchedDate"`
		DispatchedTime   StringValue `json:"dispatchedTime"`
		WillPuDate       StringValue `json:"willPuDate"`
		WillPuTime       StringValue `json:"willPuTime"`
		ArrivedPuDate    StringValue `json:"arrivedPuDate"`
		ArrivedPuTime    StringValue `json:"arrivedPuTime"`
		LoadedDate       StringValue `json:"loadedDate"`
		LoadedTime       StringValue `json:"loadedTime"`
		DelEtaDate       StringValue `json:"delEtaDate"`
		DelEtaTime       StringValue `json:"delEtaTime"`
		ArrivedConsDate  StringValue `json:"arrivedConsDate"`
		ArrivedConsTime  StringValue `json:"arrivedConsTime"`
		DeliveredDate    StringValue `json:"deliveredDate"`
		DeliveredTime    StringValue `json:"deliveredTime"`
		SignedBy         StringValue `json:"signedBy"`
	}
	Customer struct {
		Name    StringValue `json:"name"`
		Address StringValue `json:"address"`
		City    StringValue `json:"city"`
		State   StringValue `json:"state"`
		Zip     StringValue `json:"zip"`
		Country StringValue `json:"country"`
		Contact StringValue `json:"contact"`
		Email   StringValue `json:"email"`
		Email2  StringValue `json:"email2"`
		Email3  StringValue `json:"email3"`
		Email4  StringValue `json:"email4"`
		Email5  StringValue `json:"email5"`
		Email6  StringValue `json:"email6"`
		Phone   StringValue `json:"phone"`
		Ref     StringValue `json:"ref#"`
	}
	BillTo struct {
		Name    StringValue `json:"name"`
		Address StringValue `json:"address"`
		City    StringValue `json:"city"`
		State   StringValue `json:"state"`
		Zip     StringValue `json:"zip"`
		Country StringValue `json:"country"`
		Contact StringValue `json:"contact"`
		Phone   StringValue `json:"phone"`
	}
	PickUp struct {
		Name          StringValue `json:"name"`
		Address       StringValue `json:"address"`
		Address2      StringValue `json:"address2"`
		City          StringValue `json:"city"`
		State         StringValue `json:"state"`
		Zip           StringValue `json:"zip"`
		Country       StringValue `json:"country"`
		Contact       StringValue `json:"contact"`
		Email         StringValue `json:"email"`
		Phone         StringValue `json:"phone"`
		ReadyDate     StringValue `json:"readyDate"`
		ReadyTime     StringValue `json:"readyTime"`
		ApptDate      StringValue `json:"apptDate"`
		ApptNote      StringValue `json:"apptNote"`
		ApptStartTime StringValue `json:"apptTime"`
		BusinessHours StringValue `json:"businessHours"`
		Ref           StringValue `json:"ref#"`
	}
	Consignee struct {
		Name          StringValue `json:"name"`
		Address       StringValue `json:"address"`
		Address2      StringValue `json:"address2"`
		State         StringValue `json:"state"`
		Zip           StringValue `json:"zip"`
		Country       StringValue `json:"country"`
		Appt          StringValue `json:"appt"`
		ApptNote      StringValue `json:"apptNote"`
		ApptStartTime StringValue `json:"apptTime"`
		City          StringValue `json:"city"`
		Contact       StringValue `json:"contact"`
		Email         StringValue `json:"email"`
		BusinessHours StringValue `json:"businessHours"`
		MustDeliver   StringValue `json:"mustDeliver"` // Unclear what this is
		Phone         StringValue `json:"phone"`
		Ref           StringValue `json:"ref#"`
	}
	RateData struct {
		CustomerRateType       StringValue `json:"customerRateType"`
		CarrierRateType        StringValue `json:"carrierRateType"`
		CarrierNumHours        FloatValue  `json:"carrierNumHours"`
		CustomerNumHours       FloatValue  `json:"customerNumHours"`
		CarrierLHRateUSD       FloatValue  `json:"carrierLHRateUSD"`
		CustomerLHRateUSD      FloatValue  `json:"customerLHRateUSD"`
		CustomerLineHaulCharge FloatValue  `json:"customerLineHaulCharge"`
		CustomerTotalCost      FloatValue  `json:"customerTotalCost"`
		CarrierLineHaulCharge  FloatValue  `json:"carrierLineHaulCharge"`
		CarrierTotalCost       FloatValue  `json:"carrierTotalCost"`
		Fsc                    FloatValue  `json:"fsc%"` // 0 - 100
		FscMile                FloatValue  `json:"fsc/mile"`
		MaxRate                FloatValue  `json:"maxRate"`
		NetProfitUSD           FloatValue  `json:"netUSD"`
		NetProfitPercent       FloatValue  `json:"profit"` // 0 -100
	}
	Specifications struct {
		MinTempFahrenheit     FloatValue `json:"minTempFahrenheit"`
		MaxTempFahrenheit     FloatValue `json:"maxTempFahrenheit"`
		CustomerDistanceMiles FloatValue `json:"customerDistanceMiles"`
	}
	Note struct {
		Note      StringValue `json:"note"`
		CreatedAt StringValue `json:"createdAt"`
		UpdatedBy StringValue `json:"updatedBy"`
	}
)

type (
	StringValue struct {
		Value string `json:"value"`

		// The form input's 'name' attribute required for POST requests
		FormName string `json:"formName"`

		// Some fields are always read-only (like Customer City which is autofilled)
		// and some fields are dynamically read-only depending on the status of the PRO
		IsReadOnly bool `json:"isReadOnly"`

		MaxLength int `json:"maxLength"`

		// Fun fact: Aljex sometimes has multiple form names for the same field and either 1 or all versions
		// need to be set in the POST request in order for the update to succeed
		// Ex: For dispatch date, the form element's name is `di_hdate`` but the name that actually works is `hdate`.
		OtherFormNames []string `json:"otherFormNames"`
	}

	FloatValue struct {
		Value          float32  `json:"value,string"`
		FormName       string   `json:"formName"`
		IsReadOnly     bool     `json:"isReadOnly"`
		MaxLength      int      `json:"maxLength"`
		OtherFormNames []string `json:"otherFormNames"`
	}

	RateType string
)

const (
	FlatRate RateType = "Flat Rate"
	AllIn    RateType = "All In"
	AutoRate RateType = "Auto Rate"
	CWT      RateType = "CWT"
	Ton      RateType = "Ton"
	Pieces   RateType = "Pieces"
	Mileage  RateType = "Mileage"
	Hourly   RateType = "Hourly"
	// For carrier only
	Percent RateType = "Percent"
	// For customer only
	Gainshare RateType = "Gainshare"
)

// ToLoadModel converts Aljex's LoadData struct to Drumkit's internal, universal Load model
func (a LoadData) ToLoadModel(
	ctx context.Context,
	freightTrackingID string,
	serviceID uint,
	tmsID uint,
) *models.Load {
	result := &models.Load{
		ExternalTMSID:     freightTrackingID,
		FreightTrackingID: freightTrackingID,
		ServiceID:         serviceID,
		TMSID:             tmsID,
		LoadCoreInfo: models.LoadCoreInfo{
			Status:   a.Status.Value,
			PONums:   a.PONums.Value,
			Operator: a.Operator.Value,

			AdditionalReferences: a.AdditionalReferences,

			RateData: models.RateData{
				CustomerRateType:     a.RateData.CustomerRateType.Value,
				CustomerRateNumUnits: a.RateData.CustomerNumHours.Value,
				CustomerLineHaulRate: a.RateData.CustomerLHRateUSD.Value,
				CustomerLineHaulCharge: models.ValueUnit{
					Val:  a.RateData.CustomerLineHaulCharge.Value,
					Unit: "USD",
				},
				FSCPercent:          a.RateData.Fsc.Value,
				FSCPerMile:          a.RateData.FscMile.Value,
				CarrierRateType:     a.RateData.CarrierRateType.Value,
				CarrierRateNumUnits: a.RateData.CarrierNumHours.Value,
				CarrierLineHaulRate: a.RateData.CarrierLHRateUSD.Value,
				CarrierLineHaulCharge: models.ValueUnit{
					Val:  a.RateData.CarrierLineHaulCharge.Value,
					Unit: "USD",
				},
				CarrierCost:         &a.RateData.CarrierTotalCost.Value,
				CarrierCostCurrency: "USD",
				CarrierMaxRate:      a.RateData.MaxRate.Value,
				NetProfitUSD:        a.RateData.NetProfitUSD.Value,
				ProfitPercent:       a.RateData.NetProfitPercent.Value,
			},
			Customer: models.Customer{
				CompanyCoreInfo: models.CompanyCoreInfo{
					Name:         a.Customer.Name.Value,
					AddressLine1: a.Customer.Address.Value,
					City:         a.Customer.City.Value,
					State:        a.Customer.State.Value,
					Zipcode:      a.Customer.Zip.Value,
					Country:      a.Customer.Country.Value,
					Contact:      a.Customer.Contact.Value,
					Phone:        a.Customer.Phone.Value,
					Email:        a.Customer.Email.Value,
				},
				RefNumber: a.Customer.Ref.Value,
			},
			BillTo: models.BillTo{
				CompanyCoreInfo: models.CompanyCoreInfo{
					Name:         a.BillTo.Name.Value,
					AddressLine1: a.BillTo.Address.Value,
					City:         a.BillTo.City.Value,
					State:        a.BillTo.State.Value,
					Zipcode:      a.BillTo.Zip.Value,
					Country:      a.BillTo.Country.Value,
					Contact:      a.BillTo.Contact.Value,
					Phone:        a.BillTo.Phone.Value,
				},
			},
			Pickup: models.Pickup{
				CompanyCoreInfo: models.CompanyCoreInfo{
					Name:         a.PickUp.Name.Value,
					AddressLine1: a.PickUp.Address.Value,
					AddressLine2: a.PickUp.Address2.Value,
					City:         a.PickUp.City.Value,
					State:        a.PickUp.State.Value,
					Zipcode:      a.PickUp.Zip.Value,
					Country:      a.PickUp.Country.Value,
					Contact:      a.PickUp.Contact.Value,
					Phone:        a.PickUp.Phone.Value,
					Email:        a.PickUp.Email.Value,
				},
				RefNumber:     a.PickUp.Ref.Value,
				ReadyTime:     stringsToTime(ctx, a.PickUp.ReadyDate.Value, a.PickUp.ReadyTime.Value),
				ApptStartTime: stringsToTime(ctx, a.PickUp.ApptDate.Value, a.PickUp.ApptStartTime.Value),
				ApptNote:      a.PickUp.ApptNote.Value,
				BusinessHours: a.PickUp.BusinessHours.Value,
			},
			Consignee: models.Consignee{
				CompanyCoreInfo: models.CompanyCoreInfo{
					Name:         a.Consignee.Name.Value,
					AddressLine1: a.Consignee.Address.Value,
					AddressLine2: a.Consignee.Address2.Value,
					City:         a.Consignee.City.Value,
					State:        a.Consignee.State.Value,
					Zipcode:      a.Consignee.Zip.Value,
					Country:      a.Consignee.Country.Value,
					Contact:      a.Consignee.Contact.Value,
					Phone:        a.Consignee.Phone.Value,
					Email:        a.Consignee.Email.Value,
				},
				BusinessHours: a.Consignee.BusinessHours.Value,
				RefNumber:     a.Consignee.Ref.Value,
				MustDeliver:   stringsToTime(ctx, a.Consignee.MustDeliver.Value, ""),
				ApptStartTime: stringsToTime(ctx, a.Consignee.Appt.Value, a.Consignee.ApptStartTime.Value),
				ApptNote:      a.Consignee.ApptNote.Value,
			},
			Carrier: models.Carrier{
				MCNumber:             a.Carrier.Mc.Value,
				DOTNumber:            a.Carrier.Dot.Value,
				Name:                 a.Carrier.Carrier.Value,
				Phone:                a.Carrier.Phone.Value,
				Dispatcher:           a.Carrier.Dispatcher.Value,
				SealNumber:           a.Carrier.Seal.Value,
				SCAC:                 a.Carrier.Scac.Value,
				FirstDriverName:      a.Carrier.FirstDriver.Value,
				FirstDriverPhone:     a.Carrier.FirstDriverCell.Value,
				SecondDriverName:     a.Carrier.SecondDriver.Value,
				SecondDriverPhone:    a.Carrier.SecondDriverCell.Value,
				Email:                a.Carrier.Email.Value,
				DispatchCity:         a.Carrier.DispCity.Value,
				DispatchState:        a.Carrier.DispState.Value,
				ExternalTMSTruckID:   a.Carrier.Truck.Value,
				ExternalTMSTrailerID: a.Carrier.Trailer.Value,
				ConfirmationSentTime: stringsToTime(
					ctx, a.Carrier.ConfSentDate.Value, a.Carrier.ConfSentTime.Value),
				ConfirmationReceivedTime: stringsToTime(
					ctx, a.Carrier.ConfRecdDate.Value, a.Carrier.ConfRecdTime.Value),
				DispatchedTime: stringsToTime(
					ctx, a.Carrier.DispatchedDate.Value, a.Carrier.DispatchedTime.Value),
				ExpectedPickupTime: stringsToTime(
					ctx, a.Carrier.WillPuDate.Value, a.Carrier.WillPuTime.Value),
				PickupStart: stringsToTime(
					ctx, a.Carrier.ArrivedPuDate.Value, a.Carrier.ArrivedPuTime.Value),
				PickupEnd: stringsToTime(
					ctx, a.Carrier.LoadedDate.Value, a.Carrier.LoadedTime.Value),
				ExpectedDeliveryTime: stringsToTime(
					ctx, a.Carrier.DelEtaDate.Value, a.Carrier.DelEtaTime.Value),
				DeliveryStart: stringsToTime(
					ctx, a.Carrier.ArrivedConsDate.Value, a.Carrier.ArrivedConsTime.Value),
				DeliveryEnd: stringsToTime(
					ctx, a.Carrier.DeliveredDate.Value, a.Carrier.DeliveredTime.Value),
				SignedBy: a.Carrier.SignedBy.Value,
			},
			Specifications: models.Specifications{
				TotalDistance: models.ValueUnit{
					Val:  a.Specifications.CustomerDistanceMiles.Value,
					Unit: models.MilesUnit,
				},
				TotalOutPalletCount: int(a.OutPalletCount.Value),
				MinTempFahrenheit:   a.Specifications.MinTempFahrenheit.Value,
				MaxTempFahrenheit:   a.Specifications.MaxTempFahrenheit.Value,
				TransportType:       a.EquipmentType.Value,
				TransportTypeEnum:   mapEquipmentCodeToCategory(a.EquipmentType.Value),
			},
		},
	}

	for _, v := range a.Notes {
		dateTimeSplit := strings.Split(v.CreatedAt.Value, " ")
		var date, clock string
		if len(dateTimeSplit) == 2 {
			date = dateTimeSplit[0]
			clock = regexp.MustCompile(`\d{2}:\d{2}`).FindString(strings.TrimSpace(dateTimeSplit[1]))
		}
		result.Notes = append(result.Notes, models.Note{
			CreatedAt: stringsToTime(ctx, date, clock),
			UpdatedBy: v.UpdatedBy.Value,
			Note:      v.Note.Value,
		})
	}

	setWarehouseTimezones(ctx, &result.Pickup, &result.Consignee)

	return result
}

// Get timezone of pickup and dropoff locations
func setWarehouseTimezones(ctx context.Context, pu *models.Pickup, dest *models.Consignee) {
	pickupTimezone, err := timezone.GetTimezone(
		ctx,
		pu.City,
		pu.State,
		pu.Country,
	)
	if err != nil {
		log.Warnf(ctx, "Failed to get pickup timezone for %s, %s: %v. Keeping original time.",
			pu.City, pu.State, err)
	} else {
		pu.Timezone = pickupTimezone
	}

	dropoffTimezone, err := timezone.GetTimezone(
		ctx,
		dest.City,
		dest.State,
		dest.Country,
	)
	if err != nil {
		log.Warnf(ctx, "Failed to get dropoff timezone for %s, %s: %v. Keeping original time.",
			dest.City, dest.State, err)
	} else {
		dest.Timezone = dropoffTimezone
	}

}

// ToAljexData converts a Load model to Aljex's LoadData struct, which includes the Aljex form names for each field
func ToAljexData(load models.Load) *LoadData {
	// Calculate summary totals from commodities
	var totalPieces, totalWeight float32
	for _, commodity := range load.Commodities {
		totalPieces += float32(commodity.Quantity)
		totalWeight += float32(commodity.WeightTotal)
	}

	// Ensure TransportTypeEnum is set based on TransportType if not already set
	if load.Specifications.TransportTypeEnum == nil && load.Specifications.TransportType != "" {
		category := mapTransportTypeToCategory(load.Specifications.TransportType)
		load.Specifications.TransportTypeEnum = &category
	}

	aljexData := LoadData{
		PONums: StringValue{Value: load.PONums, FormName: ""},
		Operator: StringValue{
			Value:    load.Operator,
			FormName: "fld62",
			// OtherFormNames: []string{"fld62", "fld62x"},
		},

		AdditionalReferences: convertAdditionalReferences(load.AdditionalReferences),

		OutPalletCount: FloatValue{Value: float32(load.Specifications.TotalOutPalletCount), FormName: "fld97"},
		Status:         StringValue{Value: load.Status},
		// Summary fields calculated from commodities
		TotalPieces: FloatValue{Value: totalPieces, FormName: "fld11"},
		TotalWeight: FloatValue{Value: totalWeight, FormName: "fld13"},
		// Equipment type mapping
		EquipmentType: StringValue{Value: getEquipmentTypeCode(load.Specifications.TransportType), FormName: "fld18"},
		// Individual commodity items for Items grid
		Commodities: mapCommoditiesToItems(load.Commodities),
		RateData: RateData{
			CustomerRateType: StringValue{
				Value:    mapRateTypeCode(load.RateData.CustomerRateType),
				FormName: "fld23",
			},
			CarrierRateType: StringValue{
				Value:    mapRateTypeCode(load.RateData.CarrierRateType),
				FormName: "paytype",
			},
			CarrierNumHours:        FloatValue{Value: load.RateData.CarrierRateNumUnits, FormName: "fld527"},
			CustomerNumHours:       FloatValue{Value: load.RateData.CustomerRateNumUnits, FormName: "fld526"},
			CarrierLHRateUSD:       FloatValue{Value: load.RateData.CarrierLineHaulRate, FormName: "payrate"},
			CustomerLHRateUSD:      FloatValue{Value: load.RateData.CustomerLineHaulRate, FormName: "fld24"},
			CustomerLineHaulCharge: FloatValue{Value: load.RateData.CustomerLineHaulCharge.Val, FormName: "fld25"},
			// Aljex autocalculated based on carrier line haul + sum of accessorial charges (fuel, detention, etc)
			CustomerTotalCost: FloatValue{Value: load.RateData.CustomerTotalCharge.Val, FormName: "t4"},

			CarrierLineHaulCharge: FloatValue{Value: load.RateData.CarrierLineHaulCharge.Val, FormName: "fld103"},
			// Aljex autocalculated based on carrier line haul + sum of accessorial charges (fuel, detention, etc)
			//nolint:staticcheck // For backwards compatibility
			CarrierTotalCost: FloatValue{Value: load.RateData.CarrierTotalCost.Val, FormName: "t3"},
			Fsc:              FloatValue{Value: load.RateData.FSCPercent, FormName: "fld664"},
			FscMile:          FloatValue{Value: load.RateData.FSCPerMile, FormName: "fld665"},
			MaxRate:          FloatValue{Value: load.RateData.CarrierMaxRate, FormName: "fld604"},
			NetProfitUSD:     FloatValue{Value: load.RateData.NetProfitUSD, FormName: "fldp0"},
			NetProfitPercent: FloatValue{Value: load.RateData.ProfitPercent, FormName: "fldp1"},
		},
		Customer: Customer{
			Name: StringValue{
				Value:          load.Customer.Name,
				FormName:       "s_fld2",
				OtherFormNames: []string{"s_fld2", "fld2", "fld2x"},
			},
			Address: StringValue{Value: load.Customer.AddressLine1, FormName: "fld3"},
			City: StringValue{
				Value:          load.Customer.City,
				FormName:       "s_fld7",
				OtherFormNames: []string{"s_fld7", "fld7"},
			},
			State: StringValue{
				Value:          load.Customer.State,
				FormName:       "s_fld5",
				OtherFormNames: []string{"s_fld5", "fld5"},
			},
			Zip:     StringValue{Value: load.Customer.Zipcode, FormName: "fld6"},
			Country: StringValue{Value: load.Customer.Country, FormName: "fld333"},
			Contact: StringValue{Value: load.Customer.Contact, FormName: "fld9"},
			Email:   StringValue{Value: load.Customer.Email, FormName: "fldp4"},
			Phone:   StringValue{Value: load.Customer.Phone, FormName: "fld8"},
			Ref: StringValue{
				Value:          load.Customer.RefNumber,
				FormName:       "s_fld59",
				OtherFormNames: []string{"s_fld59", "fld59"},
			},
		},
		BillTo: BillTo{
			Name: StringValue{
				Value:          load.BillTo.Name,
				FormName:       "s_fld169",
				OtherFormNames: []string{"s_fld169", "fld169", "fld169x"},
			},
			Address: StringValue{Value: load.BillTo.AddressLine1, FormName: "btaddr"},
			City: StringValue{
				Value:          load.BillTo.City,
				FormName:       "btcity",
				OtherFormNames: []string{"s_btcity", "btcity"},
			},
			State: StringValue{
				Value:          load.BillTo.State,
				FormName:       "s_btstate",
				OtherFormNames: []string{"s_btstate", "btstate"},
			},
			Zip:     StringValue{Value: load.BillTo.Zipcode, FormName: "btzip"},
			Country: StringValue{Value: load.BillTo.Country, FormName: "btcountry"},
			Contact: StringValue{Value: load.BillTo.Contact, FormName: "btcontact"},
			Phone: StringValue{
				Value:          load.BillTo.Phone,
				FormName:       "s_btphone",
				OtherFormNames: []string{"s_btphone", "btphone"},
			},
		},
		PickUp: PickUp{
			Name: StringValue{
				Value:          load.Pickup.Name,
				FormName:       "s_fld30",
				OtherFormNames: []string{"fld30x", "s_fld30", "fld30"}},
			Address:  StringValue{Value: load.Pickup.AddressLine1, FormName: "fld31"},
			Address2: StringValue{Value: load.Pickup.AddressLine2, FormName: "fld181"},
			City: StringValue{
				Value:          load.Pickup.City,
				FormName:       "s_fld32",
				OtherFormNames: []string{"s_fld32", "fld32", "fld32x"},
			},
			State: StringValue{
				Value:          load.Pickup.State,
				FormName:       "s_fld33",
				OtherFormNames: []string{"s_fld33", "fld33", "fld33x"},
			},
			Zip: StringValue{
				Value:          load.Pickup.Zipcode,
				FormName:       "fld34",
				OtherFormNames: []string{"fld34", "fld34x"},
			},
			Country: StringValue{Value: load.Pickup.Country, FormName: "fld334"},
			Contact: StringValue{Value: load.Pickup.Contact, FormName: "fld35"},
			Email:   StringValue{Value: load.Pickup.Email, FormName: "fld197"},
			Phone:   StringValue{Value: load.Pickup.Phone, FormName: "fld36"},
			ReadyDate: StringValue{
				Value:          timeToAljexDate(load.Pickup.ReadyTime),
				FormName:       "s_fld39",
				OtherFormNames: []string{"s_fld39", "fld39"},
			},
			ReadyTime:     StringValue{Value: timeToAljexTime(load.Pickup.ReadyTime), FormName: "fld14"},
			ApptDate:      StringValue{Value: timeToAljexDate(load.Pickup.ApptStartTime), FormName: "fld185"},
			ApptNote:      StringValue{Value: load.Pickup.ApptNote, FormName: "fld187"},
			ApptStartTime: StringValue{Value: timeToAljexTime(load.Pickup.ApptStartTime), FormName: "fld186"},
			Ref: StringValue{
				Value:          load.Pickup.RefNumber,
				FormName:       "s_fld155",
				OtherFormNames: []string{"s_fld155", "fld155"},
			},
			BusinessHours: StringValue{Value: load.Pickup.BusinessHours, FormName: "fld15"},
		},
		Consignee: Consignee{
			Name: StringValue{Value: load.Consignee.Name,
				FormName:       "s_fld29",
				OtherFormNames: []string{"fld29x", "fld29", "s_fld29"},
			},
			Address:  StringValue{Value: load.Consignee.AddressLine1, FormName: "fld60"},
			Address2: StringValue{Value: load.Consignee.AddressLine2, FormName: "fld182"},
			State: StringValue{
				Value:          load.Consignee.State,
				FormName:       "s_fld41",
				OtherFormNames: []string{"s_fld41", "fld41", "fld41x"},
			},
			Zip: StringValue{
				Value:          load.Consignee.Zipcode,
				FormName:       "fld70",
				OtherFormNames: []string{"fld70", "fld70x"},
			},
			Country: StringValue{Value: load.Consignee.Country, FormName: "fld335"},
			Appt: StringValue{
				Value:          timeToAljexDate(load.Consignee.ApptStartTime),
				FormName:       "s_fld150",
				OtherFormNames: []string{"s_fld150", "fld150"},
			},
			ApptNote:      StringValue{Value: load.Consignee.ApptNote, FormName: "fld152"},
			ApptStartTime: StringValue{Value: timeToAljexTime(load.Consignee.ApptStartTime), FormName: "fld151"},
			City: StringValue{
				Value:          load.Consignee.City,
				FormName:       "s_fld10",
				OtherFormNames: []string{"s_fld10", "fld10", "fld10x"},
			},
			Contact:     StringValue{Value: load.Consignee.Contact, FormName: "fld43"},
			Email:       StringValue{Value: load.Consignee.Email, FormName: "fld198"},
			MustDeliver: StringValue{Value: timeToAljexDate(load.Consignee.MustDeliver), FormName: "fld602"},
			Phone:       StringValue{Value: load.Consignee.Phone, FormName: "fld58"},
			Ref: StringValue{
				Value:          load.Consignee.RefNumber,
				FormName:       "s_fld156",
				OtherFormNames: []string{"s_fld156", "fld156"},
			},
			BusinessHours: StringValue{Value: load.Consignee.BusinessHours, FormName: "fld42"},
		},
		Carrier: Carrier{
			Carrier: StringValue{Value: load.Carrier.Name, FormName: "fldcarr"},
			Dot:     StringValue{Value: load.Carrier.DOTNumber, FormName: "dotno"},
			Mc:      StringValue{Value: load.Carrier.MCNumber, FormName: "mcno"},
			Scac:    StringValue{Value: load.Carrier.SCAC, FormName: "scac"},
			Seal: StringValue{
				Value:          load.Carrier.SealNumber,
				FormName:       "di_fld158",
				OtherFormNames: []string{"di_fld158", "fld158"},
			},
			Phone:       StringValue{Value: load.Carrier.Phone, FormName: "fld172"},
			Email:       StringValue{Value: load.Carrier.Email, FormName: "fld196"},
			FirstDriver: StringValue{Value: load.Carrier.FirstDriverName, FormName: "fld74"},
			FirstDriverCell: StringValue{
				Value:          load.Carrier.FirstDriverPhone,
				FormName:       "di_fld177",
				OtherFormNames: []string{"tr_fld177", "di_fld177", "fld177"}},
			SecondDriver: StringValue{
				Value:          load.Carrier.SecondDriverName,
				FormName:       "di_fld188",
				OtherFormNames: []string{"fld188", "di_fld188"}},
			SecondDriverCell: StringValue{
				Value:          load.Carrier.SecondDriverPhone,
				FormName:       "di_fld189",
				OtherFormNames: []string{"fld189", "di_fld189"}},
			DispCity:   StringValue{Value: load.Carrier.DispatchCity, FormName: "fld91"},
			DispState:  StringValue{Value: load.Carrier.DispatchState, FormName: "fld92"},
			Dispatcher: StringValue{Value: load.Carrier.Dispatcher, FormName: "fld176"},
			Trailer: StringValue{
				Value:          load.Carrier.ExternalTMSTrailerID,
				FormName:       "di_fld89",
				OtherFormNames: []string{"fld89", "fld89x", "di_fld89"}},
			Truck: StringValue{
				Value:          load.Carrier.ExternalTMSTruckID,
				FormName:       "di_fld191",
				OtherFormNames: []string{"fld191", "fld191x", "di_fld191"}},
			ConfSentDate: StringValue{
				Value:          timeToAljexDate(load.Carrier.ConfirmationSentTime),
				FormName:       "fld549",
				OtherFormNames: []string{"fld549"}},
			ConfSentTime: StringValue{
				Value:          timeToAljexTime(load.Carrier.ConfirmationSentTime),
				FormName:       "fld559",
				OtherFormNames: []string{"fld559"}},
			ConfRecdDate: StringValue{
				Value:          timeToAljexDate(load.Carrier.ConfirmationReceivedTime),
				FormName:       "fld561",
				OtherFormNames: []string{"fld561"}},
			ConfRecdTime: StringValue{
				Value:          timeToAljexTime(load.Carrier.ConfirmationReceivedTime),
				FormName:       "fld562",
				OtherFormNames: []string{"fld562"}},
			DispatchedDate: StringValue{
				Value:          timeToAljexDate(load.Carrier.DispatchedTime),
				FormName:       "di_hdate",
				OtherFormNames: []string{"hdate", "tr_hdate", "di_hdate"}},
			DispatchedTime: StringValue{
				Value:          timeToAljexTime(load.Carrier.DispatchedTime),
				FormName:       "di_fld53",
				OtherFormNames: []string{"fld53", "tr_fld53", "di_fld53"}},
			WillPuDate: StringValue{
				Value:          timeToAljexDate(load.Carrier.ExpectedPickupTime),
				FormName:       "di_wdate",
				OtherFormNames: []string{"di_wdate", "wdate"}},
			WillPuTime: StringValue{
				Value:          timeToAljexTime(load.Carrier.ExpectedPickupTime),
				FormName:       "di_fld54",
				OtherFormNames: []string{"di_fld54", "fld54"}},
			ArrivedPuDate: StringValue{
				Value:          timeToAljexDate(load.Carrier.PickupStart),
				FormName:       "di_avdate",
				OtherFormNames: []string{"avdate", "di_avdate"}},
			ArrivedPuTime: StringValue{
				Value:          timeToAljexTime(load.Carrier.PickupStart),
				FormName:       "di_avtime",
				OtherFormNames: []string{"avtime", "di_avtime"}},
			LoadedDate: StringValue{
				Value:          timeToAljexDate(load.Carrier.PickupEnd),
				FormName:       "di_ldate",
				OtherFormNames: []string{"ldate", "di_ldate"}},
			LoadedTime: StringValue{
				Value:          timeToAljexTime(load.Carrier.PickupEnd),
				FormName:       "di_ltime",
				OtherFormNames: []string{"ltime", "di_ltime"}},
			DelEtaDate: StringValue{
				Value:          timeToAljexDate(load.Carrier.ExpectedDeliveryTime),
				FormName:       "di_edate",
				OtherFormNames: []string{"edate", "di_edate", "tr_edate"}},
			DelEtaTime: StringValue{
				Value:          timeToAljexTime(load.Carrier.ExpectedDeliveryTime),
				FormName:       "di_etime",
				OtherFormNames: []string{"etime", "di_etime", "tr_etime"}},
			ArrivedConsDate: StringValue{
				Value:          timeToAljexDate(load.Carrier.DeliveryStart),
				FormName:       "di_addate",
				OtherFormNames: []string{"di_addate", "addate", "tr_addate"}},
			ArrivedConsTime: StringValue{
				Value:          timeToAljexTime(load.Carrier.DeliveryStart),
				FormName:       "di_addtime",
				OtherFormNames: []string{"di_addtime", "addtime", "tr_addtime"}},
			DeliveredDate: StringValue{
				Value:          timeToAljexDate(load.Carrier.DeliveryEnd),
				FormName:       "di_fld145",
				OtherFormNames: []string{"fld145", "di_fld145", "im_fld145", "tr_fld145", "af_fld145"}},
			DeliveredTime: StringValue{
				Value:          timeToAljexTime(load.Carrier.DeliveryEnd),
				FormName:       "di_dtime",
				OtherFormNames: []string{"dtime", "di_dtime", "im_dtime", "af_dtime", "tr_dtime"}},
			SignedBy: StringValue{
				Value:          load.Carrier.SignedBy,
				FormName:       "di_fld147",
				OtherFormNames: []string{"fld147", "di_fld147"}},
		},
	}

	return &aljexData
}

// maps specific transport types to Aljex equipment codes
var transportTypeToAljexCode = map[string]string{
	"V":           "V",
	"VA":          "VA",
	"VC":          "VC",
	"VLG":         "VLG",
	"VP":          "VP",
	"VR":          "VR",
	"R":           "R",
	"F":           "F",
	"FA":          "FA",
	"FOD":         "FOD",
	"FOW":         "FOW",
	"FSD":         "FSD",
	"FSDC":        "FSDC",
	"HS":          "HS",
	"HSDT":        "HSDT",
	"HSOD":        "HSOD",
	"HSWR":        "HSWR",
	"HSWW":        "HSWW",
	"STR":         "STR",
	"SPRI":        "SPRI",
	"STEP":        "STEP",
	"SDC":         "SDC",
	"SDOD":        "SDOD",
	"SDR":         "SDR",
	"STRD":        "STRD",
	"STRE":        "STRE",
	"STRF":        "STRF",
	"LB":          "LB",
	"RGN":         "RGN",
	"RGNW":        "RGNW",
	"RGOD":        "RGOD",
	"RGOW":        "RGOW",
	"DD":          "DD",
	"LAND":        "LAND",
	"CON":         "CON",
	"PO":          "PO",
	"POOD":        "POOD",
	"POOW":        "POOW",
	"PH":          "PH",
	"HOP":         "HOP",
	"DRVR":        "DRVR",
	"WRE":         "WRE",
	"ODS":         "ODS",
	"PARF":        "PARF",
	"TORD":        "TORD",
	"vanliftgate": "VLG",
	"vanlift":     "VLG",
	"liftgate":    "VLG",
}

// maps specific transport types to general TransportTypeEnum categories
var transportTypeToGeneralCategory = map[string]models.TransportTypeEnum{
	"V":                        models.Van,
	"VA":                       models.Van,
	"VC":                       models.Van,
	"VLG":                      models.Van,
	"VP":                       models.Van,
	"VR":                       models.Van,
	"vanliftgate":              models.Van,
	"vanlift":                  models.Van,
	"liftgate":                 models.Van,
	"R":                        models.Reefer,
	"F":                        models.Flatbed,
	"FA":                       models.Flatbed,
	"FOD":                      models.Flatbed,
	"FOW":                      models.Flatbed,
	"FSD":                      models.Flatbed,
	"FSDC":                     models.Flatbed,
	"FLATBED OD":               models.Flatbed,
	"STRETCH - FLATBED":        models.Flatbed,
	"AIR-RIDE FLATBED":         models.Flatbed,
	"STEPDECK CONESTOGA":       models.Flatbed,
	"STRETCH - RGN":            models.Flatbed,
	"FLATBED - STEPDECK":       models.Flatbed,
	"STEPDECK WRAMPS":          models.Flatbed,
	"CONESTOGA":                models.Flatbed,
	"STEPDECK":                 models.Flatbed,
	"FLATBED OW":               models.Flatbed,
	"RGN OW":                   models.Flatbed,
	"RGN OD":                   models.Flatbed,
	"FLATBED":                  models.Flatbed,
	"PARTIAL - OPEN DECK":      models.Flatbed,
	"STEPDECK OD":              models.Flatbed,
	"FLATBED -STEPDECK - CON.": models.Flatbed,
	"HOTSHOT":                  models.Hotshot,
	"HOTSHOT WRAMPS":           models.Hotshot,
	"HOTSHOT OD":               models.Hotshot,
	"STEP":                     models.Flatbed,
	"SDC":                      models.Flatbed,
	"SDOD":                     models.Flatbed,
	"SDR":                      models.Flatbed,
	"STRD":                     models.Flatbed,
	"STRE":                     models.Flatbed,
	"STRF":                     models.Flatbed,
	"LB":                       models.Flatbed,
	"RGN":                      models.Flatbed,
	"RGNW":                     models.Flatbed,
	"RGOD":                     models.Flatbed,
	"RGOW":                     models.Flatbed,
	"DD":                       models.Flatbed,
	"LAND":                     models.Flatbed,
	"CON":                      models.Flatbed,
	"HS":                       models.Hotshot,
	"HSDT":                     models.Hotshot,
	"HSOD":                     models.Hotshot,
	"HSWR":                     models.Hotshot,
	"HSWW":                     models.Hotshot,
	"STR":                      models.BoxTruck,
	"SPRI":                     models.BoxTruck,
	"PO":                       models.BoxTruck,
	"POOD":                     models.BoxTruck,
	"POOW":                     models.BoxTruck,
	"PH":                       models.BoxTruck,
	"HOP":                      models.BoxTruck,
	"DRVR":                     models.BoxTruck,
	"WRE":                      models.BoxTruck,
	"ODS":                      models.BoxTruck,
	"PARF":                     models.BoxTruck,
	"TORD":                     models.BoxTruck,
}

// maps transport type to Aljex equipment codes
func getEquipmentTypeCode(transportType string) string {
	if code, exists := transportTypeToAljexCode[transportType]; exists {
		return code
	}
	return "V" // default to Van
}

// maps specific transport type to general TransportTypeEnum category
func mapTransportTypeToCategory(transportType string) models.TransportTypeEnum {
	if category, exists := transportTypeToGeneralCategory[transportType]; exists {
		return category
	}
	return models.Van // default to Van
}

// mapRateTypeCode maps rate type codes to their full text equivalents for Aljex
func mapRateTypeCode(rateTypeCode string) string {
	switch rateTypeCode {
	case "F":
		return "Flat Rate"
	case "I":
		return "All In"
	case "A":
		return "Auto Rate"
	case "C":
		return "CWT"
	case "T":
		return "Ton"
	case "P":
		return "Pieces"
	case "M":
		return "Mileage"
	case "H":
		return "Hourly"
	case "G":
		return "Gainshare"
	case "%":
		return "Percent"
	default:
		return rateTypeCode
	}
}

// mapCommoditiesToItems converts models.Commodity slice to CommodityItem slice with Aljex form names
func mapCommoditiesToItems(commodities []models.Commodity) []CommodityItem {
	var items []CommodityItem

	for i, commodity := range commodities {
		if i >= 10 {
			break // Aljex Items grid supports max 10 rows
		}

		// Calculate field names for each row
		var (
			piecesField      string
			classField       string
			weightField      string
			lengthField      string
			widthField       string
			heightField      string
			productCodeField string
			descField        string
		)

		if i == 0 {
			// Row 1 fields
			piecesField = "exa01"
			classField = "exa03"
			weightField = "exa04"
			lengthField = "exc01"
			widthField = "exc02"
			heightField = "exc03"
			productCodeField = "exa05"
			descField = "exa07"
		} else {
			// Row 2+ fields (exa11, exa13, etc.)
			rowNum := i + 1
			piecesField = fmt.Sprintf("exa%d1", rowNum)
			classField = fmt.Sprintf("exa%d3", rowNum)
			weightField = fmt.Sprintf("exa%d4", rowNum)
			lengthField = fmt.Sprintf("exc%d1", rowNum)
			widthField = fmt.Sprintf("exc%d2", rowNum)
			heightField = fmt.Sprintf("exc%d3", rowNum)
			productCodeField = fmt.Sprintf("exa%d5", rowNum)
			descField = fmt.Sprintf("exa%d7", rowNum)
		}

		item := CommodityItem{
			Pieces:       StringValue{Value: fmt.Sprintf("%d", commodity.Quantity), FormName: piecesField},
			FreightClass: StringValue{Value: commodity.FreightClass, FormName: classField},
			Weight:       StringValue{Value: fmt.Sprintf("%.0f", commodity.WeightTotal), FormName: weightField},
			Length:       StringValue{Value: fmt.Sprintf("%.0f", commodity.Length), FormName: lengthField},
			Width:        StringValue{Value: fmt.Sprintf("%.0f", commodity.Width), FormName: widthField},
			Height:       StringValue{Value: fmt.Sprintf("%.0f", commodity.Height), FormName: heightField},
			ProductCode:  StringValue{Value: commodity.ReferenceNumber, FormName: productCodeField},
			Description:  StringValue{Value: commodity.Description, FormName: descField},
		}

		items = append(items, item)
	}

	return items
}

func (a LoadData) ToLoadAttributes() models.LoadAttributes {
	result := DefaultLoadAttributes

	// Customer
	{
		result.Customer.AddressLine1 = models.FieldAttributes{IsReadOnly: a.Customer.Address.IsReadOnly,
			MaxLength: a.Customer.Address.MaxLength}
		result.Customer.City = models.FieldAttributes{IsReadOnly: a.Customer.City.IsReadOnly,
			MaxLength: a.Customer.City.MaxLength}
		result.Customer.State = models.FieldAttributes{IsReadOnly: a.Customer.State.IsReadOnly,
			MaxLength: a.Customer.State.MaxLength}
		result.Customer.Zipcode = models.FieldAttributes{IsReadOnly: a.Customer.Zip.IsReadOnly,
			MaxLength: a.Customer.Zip.MaxLength}
		result.Customer.Country = models.FieldAttributes{IsReadOnly: a.Customer.Country.IsReadOnly,
			MaxLength: a.Customer.Country.MaxLength}
		result.Customer.Contact = models.FieldAttributes{IsReadOnly: a.Customer.Contact.IsReadOnly,
			MaxLength: a.Customer.Contact.MaxLength}
		result.Customer.Phone = models.FieldAttributes{IsReadOnly: a.Customer.Phone.IsReadOnly,
			MaxLength: a.Customer.Phone.MaxLength}
		result.Customer.Email = models.FieldAttributes{IsReadOnly: a.Customer.Email.IsReadOnly,
			MaxLength: a.Customer.Email.MaxLength}
		result.Customer.RefNumber = models.FieldAttributes{IsReadOnly: a.Customer.Ref.IsReadOnly,
			MaxLength: a.Customer.Ref.MaxLength}
	}

	// Bill To; see above TODO about adding load building functionality
	result.BillTo.AddressLine1 = models.FieldAttributes{
		IsReadOnly: a.BillTo.Address.IsReadOnly,
		MaxLength:  a.BillTo.Address.MaxLength,
	}
	result.BillTo.City = models.FieldAttributes{
		IsReadOnly: a.BillTo.City.IsReadOnly,
		MaxLength:  a.BillTo.City.MaxLength,
	}
	result.BillTo.State = models.FieldAttributes{
		IsReadOnly: a.BillTo.State.IsReadOnly,
		MaxLength:  a.BillTo.State.MaxLength,
	}
	result.BillTo.Zipcode = models.FieldAttributes{
		IsReadOnly: a.BillTo.Zip.IsReadOnly,
		MaxLength:  a.BillTo.Zip.MaxLength,
	}
	result.BillTo.Country = models.FieldAttributes{
		IsReadOnly: a.BillTo.Country.IsReadOnly,
		MaxLength:  a.BillTo.Country.MaxLength,
	}

	// Pickup
	{
		result.Pickup.Name = models.FieldAttributes{
			IsReadOnly: a.PickUp.Name.IsReadOnly,
			MaxLength:  a.PickUp.Name.MaxLength,
		}
		result.Pickup.AddressLine1 = models.FieldAttributes{
			IsReadOnly: a.PickUp.Address.IsReadOnly,
			MaxLength:  a.PickUp.Address.MaxLength,
		}
		result.Pickup.AddressLine2 = models.FieldAttributes{
			IsReadOnly: a.PickUp.Address2.IsReadOnly,
			MaxLength:  a.PickUp.Address2.MaxLength,
		}
		result.Pickup.City = models.FieldAttributes{
			IsReadOnly: a.PickUp.City.IsReadOnly,
			MaxLength:  a.PickUp.City.MaxLength,
		}
		result.Pickup.State = models.FieldAttributes{
			IsReadOnly: a.PickUp.State.IsReadOnly,
			MaxLength:  a.PickUp.State.MaxLength,
		}
		result.Pickup.Zipcode = models.FieldAttributes{
			IsReadOnly: a.PickUp.Zip.IsReadOnly,
			MaxLength:  a.PickUp.Zip.MaxLength,
		}
		result.Pickup.Country = models.FieldAttributes{
			IsReadOnly: a.PickUp.Country.IsReadOnly,
			MaxLength:  a.PickUp.Country.MaxLength,
		}
		result.Pickup.Contact = models.FieldAttributes{
			IsReadOnly: a.PickUp.Contact.IsReadOnly,
			MaxLength:  a.PickUp.Contact.MaxLength,
		}
		result.Pickup.Phone = models.FieldAttributes{
			IsReadOnly: a.PickUp.Phone.IsReadOnly,
			MaxLength:  a.PickUp.Phone.MaxLength,
		}
		result.Pickup.Email = models.FieldAttributes{
			IsReadOnly: a.PickUp.Email.IsReadOnly,
			MaxLength:  a.PickUp.Email.MaxLength,
		}
		result.Pickup.RefNumber = models.FieldAttributes{
			IsReadOnly: a.PickUp.Ref.IsReadOnly,
			MaxLength:  a.PickUp.Ref.MaxLength,
		}
		result.Pickup.ReadyTime = models.FieldAttributes{
			IsReadOnly: a.PickUp.ReadyDate.IsReadOnly && a.PickUp.ReadyTime.IsReadOnly,
		}
		result.Pickup.ApptRequired = models.FieldAttributes{IsNotSupported: true}
		result.Pickup.ApptStartTime = models.FieldAttributes{
			IsReadOnly: a.PickUp.ApptDate.IsReadOnly && a.PickUp.ApptStartTime.IsReadOnly,
		}
		result.Pickup.ApptEndTime = models.FieldAttributes{IsNotSupported: true}
		result.Pickup.ApptNote = models.FieldAttributes{
			IsReadOnly: a.PickUp.ApptNote.IsReadOnly,
			MaxLength:  a.PickUp.ApptNote.MaxLength,
		}
		result.Pickup.Timezone = models.FieldAttributes{IsNotSupported: true}
	}

	// Consignee
	{
		result.Consignee.Name = models.FieldAttributes{
			IsReadOnly: a.Consignee.Name.IsReadOnly,
			MaxLength:  a.Consignee.Name.MaxLength,
		}
		result.Consignee.AddressLine1 = models.FieldAttributes{
			IsReadOnly: a.Consignee.Address.IsReadOnly,
			MaxLength:  a.Consignee.Address.MaxLength,
		}
		result.Consignee.AddressLine2 = models.FieldAttributes{
			IsReadOnly: a.Consignee.Address2.IsReadOnly,
			MaxLength:  a.Consignee.Address2.MaxLength,
		}
		result.Consignee.City = models.FieldAttributes{
			IsReadOnly: a.Consignee.City.IsReadOnly,
			MaxLength:  a.Consignee.City.MaxLength,
		}
		result.Consignee.State = models.FieldAttributes{
			IsReadOnly: a.Consignee.State.IsReadOnly,
			MaxLength:  a.Consignee.State.MaxLength,
		}
		result.Consignee.Zipcode = models.FieldAttributes{
			IsReadOnly: a.Consignee.Zip.IsReadOnly,
			MaxLength:  a.Consignee.Zip.MaxLength,
		}
		result.Consignee.Country = models.FieldAttributes{
			IsReadOnly: a.Consignee.Country.IsReadOnly,
			MaxLength:  a.Consignee.Country.MaxLength,
		}
		result.Consignee.Contact = models.FieldAttributes{
			IsReadOnly: a.Consignee.Contact.IsReadOnly,
			MaxLength:  a.Consignee.Contact.MaxLength,
		}
		result.Consignee.Phone = models.FieldAttributes{
			IsReadOnly: a.Consignee.Phone.IsReadOnly,
			MaxLength:  a.Consignee.Phone.MaxLength,
		}
		result.Consignee.Email = models.FieldAttributes{
			IsReadOnly: a.Consignee.Email.IsReadOnly,
			MaxLength:  a.Consignee.Email.MaxLength,
		}
		result.Consignee.BusinessHours = models.FieldAttributes{
			IsReadOnly: a.Consignee.BusinessHours.IsReadOnly,
			MaxLength:  a.Consignee.BusinessHours.MaxLength,
		}
		result.Consignee.RefNumber = models.FieldAttributes{
			IsReadOnly: a.Consignee.Ref.IsReadOnly,
			MaxLength:  a.Consignee.Ref.MaxLength,
		}
		result.Consignee.MustDeliver = models.FieldAttributes{
			IsReadOnly: a.Consignee.MustDeliver.IsReadOnly,
		}
		result.Consignee.ApptRequired = models.FieldAttributes{IsNotSupported: true}
		result.Consignee.ApptStartTime = models.FieldAttributes{
			IsReadOnly: a.Consignee.Appt.IsReadOnly && a.Consignee.ApptStartTime.IsReadOnly,
			MaxLength:  a.Consignee.ApptStartTime.MaxLength,
		}
		result.Consignee.ApptEndTime = models.FieldAttributes{IsNotSupported: true}
		result.Consignee.ApptNote = models.FieldAttributes{
			IsReadOnly: a.Consignee.ApptNote.IsReadOnly,
			MaxLength:  a.Consignee.ApptNote.MaxLength,
		}
		result.Consignee.Timezone = models.FieldAttributes{IsNotSupported: true}
	}

	// Carrier
	{
		result.Carrier.Phone = models.FieldAttributes{
			IsReadOnly: a.Carrier.Phone.IsReadOnly,
			MaxLength:  a.Carrier.Phone.MaxLength,
		}
		result.Carrier.Dispatcher = models.FieldAttributes{
			IsReadOnly: a.Carrier.Dispatcher.IsReadOnly,
			MaxLength:  a.Carrier.Dispatcher.MaxLength,
		}
		result.Carrier.SealNumber = models.FieldAttributes{
			IsReadOnly: a.Carrier.Seal.IsReadOnly,
			MaxLength:  a.Carrier.Seal.MaxLength,
		}
		result.Carrier.FirstDriverName = models.FieldAttributes{
			IsReadOnly: a.Carrier.FirstDriver.IsReadOnly,
			MaxLength:  a.Carrier.FirstDriver.MaxLength,
		}
		result.Carrier.FirstDriverPhone = models.FieldAttributes{
			IsReadOnly: a.Carrier.FirstDriverCell.IsReadOnly,
			MaxLength:  a.Carrier.FirstDriverCell.MaxLength,
		}
		result.Carrier.SecondDriverName = models.FieldAttributes{
			IsReadOnly: a.Carrier.SecondDriver.IsReadOnly,
			MaxLength:  a.Carrier.SecondDriver.MaxLength,
		}
		result.Carrier.SecondDriverPhone = models.FieldAttributes{
			IsReadOnly: a.Carrier.SecondDriverCell.IsReadOnly,
			MaxLength:  a.Carrier.SecondDriverCell.MaxLength,
		}
		result.Carrier.Email = models.FieldAttributes{
			IsReadOnly: a.Carrier.Email.IsReadOnly,
			MaxLength:  a.Carrier.Email.MaxLength,
		}
		result.Carrier.DispatchCity = models.FieldAttributes{
			IsReadOnly: a.Carrier.DispCity.IsReadOnly,
			MaxLength:  a.Carrier.DispCity.MaxLength,
		}
		result.Carrier.DispatchState = models.FieldAttributes{
			IsReadOnly: a.Carrier.DispState.IsReadOnly,
			MaxLength:  a.Carrier.DispState.MaxLength,
		}
		result.Carrier.ExternalTMSTruckID = models.FieldAttributes{
			IsReadOnly: a.Carrier.Truck.IsReadOnly,
			MaxLength:  a.Carrier.Truck.MaxLength,
		}
		result.Carrier.ExternalTMSTrailerID = models.FieldAttributes{
			IsReadOnly: a.Carrier.Trailer.IsReadOnly,
			MaxLength:  a.Carrier.Trailer.MaxLength,
		}
		result.Carrier.ConfirmationSentTime = models.FieldAttributes{
			IsReadOnly: ((a.Carrier.ConfSentDate.IsReadOnly && a.Carrier.ConfSentTime.IsReadOnly) ||
				a.Carrier.Carrier.Value == ""),
		}
		result.Carrier.ConfirmationReceivedTime = models.FieldAttributes{
			IsReadOnly: ((a.Carrier.ConfRecdDate.IsReadOnly && a.Carrier.ConfRecdTime.IsReadOnly) ||
				a.Carrier.Carrier.Value == ""),
		}
		result.Carrier.DispatchedTime = models.FieldAttributes{
			IsReadOnly: (a.Carrier.DispatchedDate.IsReadOnly && a.Carrier.DispatchedTime.IsReadOnly),
		}
		result.Carrier.ExpectedPickupTime = models.FieldAttributes{
			IsReadOnly: (a.Carrier.WillPuDate.IsReadOnly && a.Carrier.WillPuTime.IsReadOnly),
		}
		result.Carrier.PickupStart = models.FieldAttributes{
			IsReadOnly: (a.Carrier.ArrivedPuDate.IsReadOnly && a.Carrier.ArrivedPuTime.IsReadOnly),
		}
		result.Carrier.PickupEnd = models.FieldAttributes{
			IsReadOnly: (a.Carrier.LoadedDate.IsReadOnly && a.Carrier.LoadedTime.IsReadOnly),
		}
		result.Carrier.ExpectedDeliveryTime = models.FieldAttributes{
			IsReadOnly: (a.Carrier.DelEtaDate.IsReadOnly && a.Carrier.DelEtaTime.IsReadOnly),
		}
		result.Carrier.DeliveryStart = models.FieldAttributes{
			IsReadOnly: (a.Carrier.ArrivedConsDate.IsReadOnly && a.Carrier.ArrivedConsTime.IsReadOnly),
		}
		result.Carrier.DeliveryEnd = models.FieldAttributes{
			IsReadOnly: (a.Carrier.DeliveredDate.IsReadOnly && a.Carrier.DeliveredTime.IsReadOnly),
		}
		result.Carrier.SignedBy = models.FieldAttributes{
			IsReadOnly: a.Carrier.SignedBy.IsReadOnly,
		}

	}

	// Specifications
	{
		result.Specifications.TotalOutPalletCount = models.FieldAttributes{
			IsReadOnly: a.OutPalletCount.IsReadOnly,
			MaxLength:  a.OutPalletCount.MaxLength}
		result.Specifications.MinTempFahrenheit = models.FieldAttributes{
			IsReadOnly: a.Specifications.MaxTempFahrenheit.IsReadOnly}
		result.Specifications.MaxTempFahrenheit = models.FieldAttributes{
			IsReadOnly: a.Specifications.MaxTempFahrenheit.IsReadOnly}
	}

	return result
}

func mapEquipmentCodeToCategory(equipmentCode string) *models.TransportTypeEnum {
	if category, exists := transportTypeToGeneralCategory[equipmentCode]; exists {
		return &category
	}
	category := models.Van // default to Van
	return &category
}

func stringsToTime(ctx context.Context, date string, clock string) models.NullTime {
	if date == "" {
		return models.NullTime{}
	}

	// TODO: infer timezone from Aljex/browser settings. Because timezone is zero, it's always UTC
	dateObj, err := time.Parse("01/02/06", date)
	if err != nil {
		err = fmt.Errorf("error parsing date: %w", err)
		sentry.GetHubFromContext(ctx).CaptureException(err)

		return models.NullTime{}
	}

	// Time is optional; it's sometimes empty or strings like 'DROP' and 'FCFS'
	clockObj, err := time.Parse("15:04", clock)
	if err != nil {
		log.WarnNoSentry(ctx, "skipping parsing clock", zap.String("value", clock), zap.Error(err))
	}

	return models.NullTime{
		Time: time.Date(dateObj.Year(), dateObj.Month(), dateObj.Day(),
			clockObj.Hour(), clockObj.Minute(), clockObj.Second(), clockObj.Nanosecond(), time.UTC),
		Valid: true,
	}

}

func timeToAljexDate(dt models.NullTime) (date string) {
	if !dt.Valid {
		return ""
	}

	return dt.Time.UTC().Format("01/02/06")
}

func timeToAljexTime(dt models.NullTime) (clock string) {
	if !dt.Valid {
		return ""
	}

	return dt.Time.UTC().Format("15:04")
}

// converts models.AdditionalReferences to the Aljex format
func convertAdditionalReferences(refs models.AdditionalReferences) models.AdditionalReferences {
	result := make(models.AdditionalReferences, 0, len(refs))

	for _, ref := range refs {
		result = append(result, models.AdditionalReference{
			Qualifier:          ref.Qualifier,
			Number:             ref.Number,
			Weight:             ref.Weight,
			Pieces:             ref.Pieces,
			ShouldSendToDriver: ref.ShouldSendToDriver,
		})
	}

	return result
}
