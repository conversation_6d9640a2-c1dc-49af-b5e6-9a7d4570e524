package aljex

import "github.com/drumkitai/drumkit/common/models"

// A PRO's read-only fields dynamically change depending on the PRO's status and inputs,
// which is why aljex.LoadData.ToLoadAttributes() exists. However, if there's an issue live-looking up
// the PRO, the endpoint falls back to the load in the DB. Similarly, we fallback to static read-only fields.
//
// NOTE: Updates to this file should be reflected in the aljex.LoadData.ToLoadAttributes() function.
var DefaultLoadAttributes = models.LoadAttributes{
	FreightTrackingID: models.FieldAttributes{IsReadOnly: true},
	LoadCoreInfoAttributes: models.LoadCoreInfoAttributes{
		Mode:             models.FieldAttributes{IsReadOnly: true},
		MoreThanTwoStops: models.FieldAttributes{IsNotSupported: true},
		Status:           models.FieldAttributes{IsReadOnly: true},
		// We currently do not support updating PO nums because it's multiple fields combined into 1
		PONums: models.FieldAttributes{IsReadOnly: true},

		AdditionalReferences: models.FieldAttributes{MaxLength: 18},

		Customer: models.CustomerAttributes{
			CompanyCoreInfoAttributes: models.CompanyCoreInfoAttributes{
				Name:         models.FieldAttributes{IsReadOnly: true},
				AddressLine1: models.FieldAttributes{IsReadOnly: true, MaxLength: 30},
				AddressLine2: models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				City:         models.FieldAttributes{IsReadOnly: true, MaxLength: 20},
				State:        models.FieldAttributes{IsReadOnly: true, MaxLength: 2},
				Zipcode:      models.FieldAttributes{IsReadOnly: true, MaxLength: 6},
				Country:      models.FieldAttributes{IsReadOnly: true, MaxLength: 20},
				Phone:        models.FieldAttributes{IsReadOnly: true, MaxLength: 14},
				Email:        models.FieldAttributes{IsReadOnly: true, MaxLength: 60},
				Contact:      models.FieldAttributes{MaxLength: 18},
			},
			RefNumber: models.FieldAttributes{MaxLength: 18},
		},
		BillTo: models.BillToAttributes{
			CompanyCoreInfoAttributes: models.CompanyCoreInfoAttributes{
				Name:         models.FieldAttributes{IsReadOnly: true},
				AddressLine1: models.FieldAttributes{IsReadOnly: true, MaxLength: 30},
				AddressLine2: models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				City:         models.FieldAttributes{IsReadOnly: true, MaxLength: 20},
				State:        models.FieldAttributes{IsReadOnly: true, MaxLength: 2},
				Zipcode:      models.FieldAttributes{IsReadOnly: true, MaxLength: 6},
				Country:      models.FieldAttributes{IsReadOnly: true, MaxLength: 20},
				Phone:        models.FieldAttributes{IsReadOnly: true},
				Email:        models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				// Even though Contact field is editable, Aljex rejects the input even on the browser
				Contact: models.FieldAttributes{IsReadOnly: true},
			},
		},
		Pickup: models.PickupAttributes{
			CompanyCoreInfoAttributes: models.CompanyCoreInfoAttributes{
				ExternalTMSID: models.FieldAttributes{IsNotSupported: true},
				Name:          models.FieldAttributes{MaxLength: 30},
				AddressLine1:  models.FieldAttributes{MaxLength: 30},
				AddressLine2:  models.FieldAttributes{MaxLength: 30},
				City:          models.FieldAttributes{MaxLength: 15},
				State:         models.FieldAttributes{MaxLength: 2},
				Zipcode:       models.FieldAttributes{MaxLength: 6},
				Country:       models.FieldAttributes{MaxLength: 20},
				Contact:       models.FieldAttributes{MaxLength: 18},
				Phone:         models.FieldAttributes{MaxLength: 14},
				Email:         models.FieldAttributes{MaxLength: 60},
			},
			ExternalTMSStopID: models.FieldAttributes{IsNotSupported: true},
			RefNumber:         models.FieldAttributes{MaxLength: 18},
			ApptNote:          models.FieldAttributes{MaxLength: 19},
			ApptRequired:      models.FieldAttributes{IsNotSupported: true},
			ApptType:          models.FieldAttributes{IsNotSupported: true},
			ApptEndTime:       models.FieldAttributes{IsNotSupported: true},
			Timezone:          models.FieldAttributes{IsNotSupported: true},
		},
		Consignee: models.ConsigneeAttributes{
			CompanyCoreInfoAttributes: models.CompanyCoreInfoAttributes{
				ExternalTMSID: models.FieldAttributes{IsNotSupported: true},
				Name:          models.FieldAttributes{MaxLength: 30},
				AddressLine1:  models.FieldAttributes{MaxLength: 30},
				AddressLine2:  models.FieldAttributes{MaxLength: 30},
				City:          models.FieldAttributes{MaxLength: 20},
				State:         models.FieldAttributes{MaxLength: 2},
				Zipcode:       models.FieldAttributes{MaxLength: 6},
				Country:       models.FieldAttributes{MaxLength: 20},
				Contact:       models.FieldAttributes{MaxLength: 18},
				Phone:         models.FieldAttributes{MaxLength: 14},
				Email:         models.FieldAttributes{MaxLength: 60},
			},
			ExternalTMSStopID: models.FieldAttributes{IsNotSupported: true},
			ApptRequired:      models.FieldAttributes{IsNotSupported: true},
			ApptType:          models.FieldAttributes{IsNotSupported: true},
			BusinessHours:     models.FieldAttributes{MaxLength: 9},
			RefNumber:         models.FieldAttributes{MaxLength: 18},
			ApptStartTime:     models.FieldAttributes{MaxLength: 8},
			ApptEndTime:       models.FieldAttributes{IsNotSupported: true},
			ApptNote:          models.FieldAttributes{MaxLength: 19},
			Timezone:          models.FieldAttributes{IsNotSupported: true},
		},
		Carrier: models.CarrierAttributes{
			// Carrier assignment is not a supported feature yet
			Name:                 models.FieldAttributes{IsReadOnly: true},
			DOTNumber:            models.FieldAttributes{IsReadOnly: true},
			SCAC:                 models.FieldAttributes{IsReadOnly: true},
			MCNumber:             models.FieldAttributes{IsReadOnly: true},
			RateConfirmationSent: models.FieldAttributes{IsNotSupported: true},

			Phone:                models.FieldAttributes{MaxLength: 14},
			Dispatcher:           models.FieldAttributes{MaxLength: 20},
			Notes:                models.FieldAttributes{IsNotSupported: true},
			SealNumber:           models.FieldAttributes{MaxLength: 18},
			FirstDriverName:      models.FieldAttributes{MaxLength: 18},
			FirstDriverPhone:     models.FieldAttributes{MaxLength: 14},
			SecondDriverName:     models.FieldAttributes{MaxLength: 18},
			SecondDriverPhone:    models.FieldAttributes{MaxLength: 14},
			Email:                models.FieldAttributes{MaxLength: 60},
			DispatchCity:         models.FieldAttributes{MaxLength: 15},
			DispatchState:        models.FieldAttributes{MaxLength: 2},
			ExternalTMSTruckID:   models.FieldAttributes{MaxLength: 18},
			ExternalTMSTrailerID: models.FieldAttributes{MaxLength: 18},
		},
		// NOTE: Some values are parsed, so change this when ready to show on FE
		RateData: models.InitUnsupportedRateData,
		Specifications: models.SpecificationsAttributes{
			TotalOutPalletCount: models.FieldAttributes{IsReadOnly: true},
			MaxTempFahrenheit:   models.FieldAttributes{IsReadOnly: true},
			MinTempFahrenheit:   models.FieldAttributes{IsReadOnly: true},

			TotalPiecesType:    models.FieldAttributes{IsNotSupported: true},
			TotalVolume:        models.FieldAttributes{IsNotSupported: true},
			OrderType:          models.FieldAttributes{IsNotSupported: true},
			TransportType:      models.FieldAttributes{IsNotSupported: true},
			TotalInPalletCount: models.FieldAttributes{IsNotSupported: true},
			TotalPieces:        models.FieldAttributes{IsNotSupported: true},
			Commodities:        models.FieldAttributes{IsNotSupported: true},
			NumCommodities:     models.FieldAttributes{IsNotSupported: true},
			TotalWeight:        models.FieldAttributes{IsNotSupported: true},
			BillableWeight:     models.FieldAttributes{IsNotSupported: true},
			TotalDistance:      models.FieldAttributes{IsNotSupported: true},
			IsRefrigerated:     models.FieldAttributes{IsNotSupported: true},
		},
	},
}
