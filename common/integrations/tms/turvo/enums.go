package turvo

const CustomIDType = "customId[eq]"
const PONumIDType = "poNumber[eq]"

var ServiceTypeReverseMap = map[string]string{
	"Any":      "24304",
	"Expedite": "24349",
	"Standard": "24338",
}

var CurrencyReverseMap = map[string]string{
	"USD": "1550",
	"CAD": "1551",
}

var WeightUnitReverseMap = map[string]string{
	"t":   "1522",
	"oz":  "1523",
	"ton": "1524",
	"g":   "1525",
	"lb":  "1520",
	"kg":  "1521",
}

var EquipmentSizeReverseMap = map[string]string{
	"8 ft":     "1308",
	"10 ft":    "1309",
	"12 ft":    "1310",
	"14 ft":    "1311",
	"16 ft":    "1312",
	"42 ft":    "1313",
	"45 ft":    "1314",
	"20 ft":    "1300",
	"24 ft":    "1301",
	"26 ft":    "1317",
	"28 ft":    "1302",
	"40 ft":    "1303",
	"48 ft":    "1304",
	"50 ft":    "1305",
	"53 ft":    "1306",
	"Other":    "1307",
	"75 bbl":   "1336",
	"90 bbl":   "1337",
	"115 bbl":  "1339",
	"110 bbl":  "1330",
	"130 bbl":  "1331",
	"150 bbl":  "1332",
	"170 bbl":  "1333",
	"190 bbl":  "1334",
	"210 bbl":  "1335",
	"5000 psi": "1344",
	"6000 psi": "1345",
	"7000 psi": "1346",
	"8000 psi": "1347",
	"1000 psi": "1340",
	"2000 psi": "1341",
	"3000 psi": "1342",
	"4000 psi": "1343",
	"1 car":    "1318",
	"2 car":    "1319",
	"3 car":    "1320",
	"4 car":    "1321",
	"70 bbl":   "1322",
	"82 bbl":   "1323",
	"92 bbl":   "1324",
	"100 bbl":  "1325",
	"125 bbl":  "1326",
	"160 bbl":  "1327",
	"180 bbl":  "1328",
	"200 bbl":  "1329",
}

var ItemUnitReverseMap = map[string]string{
	"Bags":                 "6003",
	"Bales":                "6004",
	"Barrels":              "6035",
	"Base units":           "6039",
	"Bin":                  "6047",
	"Blocks":               "6033",
	"Bottles":              "6040",
	"Boxes":                "6001",
	"Buckets":              "6056",
	"Bulk":                 "6034",
	"Bundles":              "6005",
	"Bushels":              "6026",
	"Cans":                 "6006",
	"Carboys":              "6007",
	"Carpets":              "6008",
	"Cartons":              "6009",
	"Cases":                "6022",
	"Coils":                "6010",
	"Combinations":         "6057",
	"Containers":           "6027",
	"Crates":               "6011",
	"Cylinders":            "6012",
	"Drums":                "6013",
	"Each":                 "6037",
	"Feet":                 "6051",
	"Gallons":              "6028",
	"Grams":                "6053",
	"Hundredweight on Net": "6058",
	"Inner Pack":           "6052",
	"Items":                "6055",
	"Kegs":                 "6024",
	"Kgs":                  "6031",
	"Kit":                  "6048",
	"Layer":                "6054",
	"Lbs":                  "6030",
	"Liters":               "6032",
	"Loose":                "6002",
	"Metric tons":          "6025",
	"Other":                "6021",
	"Pack":                 "6049",
	"Packages":             "6041",
	"Pails":                "6014",
	"Pair":                 "6050",
	"Pallets":              "6038",
	"Piece":                "6043",
	"Pouches":              "6059",
	"Racks":                "6042",
	"Ream":                 "6044",
	"Reels":                "6015",
	"Rolls":                "6016",
	"Skids":                "6017",
	"Spack":                "6045",
	"Spool":                "6046",
	"Tons":                 "6029",
	"Totes":                "6018",
	"Trays":                "6060",
	"Truckload":            "6023",
	"Tubes/pipes":          "6019",
	"Tubs":                 "6061",
	"Units":                "6036",
	"Vehicles":             "6020",
}

var EquipmentReverseMap = map[string]string{
	"Ag hopper":                          "24520",
	"Auger feed truck":                   "24519",
	"Auto transport":                     "1209",
	"Automotive racks":                   "24401",
	"Barrack barge":                      "24436",
	"Bobtail":                            "1238",
	"Box Truck":                          "24527",
	"Boxcars":                            "24402",
	"Bulk carriers":                      "24418",
	"Bulk commodity":                     "24446",
	"Car float barge":                    "24434",
	"Cargo ship":                         "24420",
	"Cargo van":                          "24455",
	"Centerbeams":                        "24403",
	"Chassis":                            "1237",
	"Coil cars":                          "24405",
	"Conestoga":                          "1210",
	"Container - Bulk":                   "24462",
	"Container - Power pack":             "24463",
	"Container - Upgraded":               "24464",
	"Container - flat rack":              "1229",
	"Container - high cube":              "1227",
	"Container - open top":               "1228",
	"Container - pallet wide":            "1230",
	"Container - refrigerated":           "1231",
	"Container - standard":               "1211",
	"Container - tanker":                 "1232",
	"Container - temperature controlled": "1245",
	"Container ships":                    "24419",
	"Containers":                         "24480",
	"Covered hoppers":                    "24404",
	"Crane":                              "24459",
	"Deck barge":                         "24432",
	"Double drop (low boy)":              "1212",
	"Double-axle":                        "24450",
	"Driveaway":                          "24461",
	"Dry - HC":                           "24504",
	"Dry - Standard":                     "24503",
	"Dump - end":                         "1218",
	"Dump - side":                        "1219",
	"Electric heat":                      "24452",
	"Enclosed":                           "1220",
	"Flatbed":                            "1204",
	"Flatbed - 4' tarp":                  "1205",
	"Flatbed - 6' tarp":                  "1206",
	"Flatbed - 8' tarp":                  "1207",
	"Flatbed - Quad-axle":                "24515",
	"Flatbed - Quad-axle rolltite":       "24516",
	"Flatbed - Super B":                  "24517",
	"Flatbed - auto":                     "1217",
	"Flatbed - covered wagon":            "24444",
	"Flatbed - curtain side":             "24445",
	"Flatbed - legal":                    "24442",
	"Flatbed - standard":                 "24441",
	"Flatbed - stretch/extendable":       "24443",
	"Flatcars":                           "24406",
	"Food trailer - frozen":              "1243",
	"Gondolas":                           "24407",
	"Hopper":                             "1221",
	"Hopper barge":                       "24433",
	"Hot oil truck":                      "1239",
	"Hotshot":                            "24460",
	"Hotshot Flatbed":                    "24511",
	"Hydro excavation truck":             "1240",
	"Insulated Van or Reefer":            "24468",
	"Isotank":                            "24514",
	"Liquid cargo barge":                 "24435",
	"Logging":                            "24447",
	"Mini float":                         "24456",
	"Moffett":                            "24457",
	"Open top hoppers":                   "24409",
	"Other":                              "1216",
	"Pickup truck":                       "24454",
	"Pneumatic":                          "1226",
	"Power barge":                        "24438",
	"Power unit - tractor":               "1233",
	"Pump truck":                         "1241",
	"Reefer":                             "24487",
	"Reefer - HC":                        "24506",
	"Reefer or Vented Van":               "24469",
	"Reefer- Standard":                   "24505",
	"Reefer – Quadaxle":                  "24525",
	"Reefer – Triaxle":                   "24524",
	"Refrigerated":                       "1208",
	"Refrigerated boxcar":                "24408",
	"Refrigerated ships":                 "24421",
	"Removable goose neck":               "1213",
	"Roll Tite":                          "24523",
	"Roll on/Roll off Ships":             "24422",
	"Royal Barge":                        "24439",
	"Single-axle":                        "24449",
	"Specialized rail equipment":         "24410",
	"Split Hopper barge":                 "24437",
	"Step deck":                          "1214",
	"Stepdeck Conestoga":                 "24526",
	"Straight truck":                     "24453",
	"Tank barge":                         "24431",
	"Tanker":                             "1215",
	"Tanker - chemical":                  "1222",
	"Tanker - food":                      "1223",
	"Tanker - fuel":                      "1224",
	"Tow":                                "1225",
	"Tow - heavy duty":                   "1235",
	"Tow - landoll":                      "1236",
	"Tow - light duty":                   "24448",
	"Tow - medium duty":                  "1234",
	"Tri-axle":                           "24451",
	"Truck, van":                         "1244",
	"Van":                                "1200",
	"Van - curtain side":                 "1203",
	"Van - dry":                          "24440",
	"Van - open top":                     "24528",
	"Van - sprinter":                     "24513",
	"Van - tri-axle dry":                 "24518",
	"Van - vented":                       "1201",
	"Walking floor":                      "24521",
	"Winch truck":                        "24458",
}

var AppointmentTypeReverseMap = map[string]string{
	"FCFS":           "9400",
	"By appointment": "9401",
}
