package turvo

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"slices"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"go.opentelemetry.io/otel/attribute"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

const createShipmentPath = "/v1/shipments"
const appTurvoURL = "https://app.turvo.com/api"

func (t *Turvo) CreateLoad(
	ctx context.Context,
	reqLoad models.Load,
	_ *models.TMSUser,
) (result models.Load, err error) {

	ctx, metaSpan := otel.StartSpan(ctx, "CreateLoadTurvo", otel.IntegrationAttrs(t.tms))
	defer func() { metaSpan.End(err) }()

	shipmentReqBody, err := t.loadToTurvoShipment(ctx, reqLoad)
	if err != nil {
		return models.Load{}, fmt.Errorf("error creating Turvo Shipment body: %w", err)
	}

	queryParams := url.Values{}
	queryParams.Add("fullResponse", "true")

	var response LoadResponse
	err = t.postWithAuth(ctx, createShipmentPath, queryParams, shipmentReqBody, &response, s3backup.TypeLoads)
	if err != nil {
		return models.Load{}, fmt.Errorf("error submitting load to Turvo: %w", err)
	}

	// Handle Turvo specific error cases that are not caught as errors
	// ex: "Customer: [customer name] is not in active status."
	if response.Details.ErrorMessage != "" {
		return models.Load{}, errtypes.NewUserFacingError(fmt.Errorf("Turvo error: %s", response.Details.ErrorMessage))
	}

	result, err = t.turvoShipmentToLoad(ctx, response.Details)
	if err != nil {
		return result, fmt.Errorf("failed to map turvo shipment to load: %w", err)
	}
	return result, nil
}

// `reqLoad.ExternalTMSID` must be provided (not the same as `freightTrackingID`). The former is the API's UUID,
// the latter is the client-facing ID by which one can lookup the load's UUID.
//
// "Turvo's systems allow for partial updates of each entity: for an update the entire entity data does not need to be
// sent over, in fact, such an operation is discouraged as it can potentially overwrite a concurrent update to the same
// entity. As an example, if one wants to simply modify the customerOrder of a shipment, they should only send the
// `customerOrder` object as a part of an update [PUT] request. app.turvo.com/lobby/documentation#tag/API-Overview
func (t *Turvo) UpdateLoad(
	ctx context.Context,
	_ *models.Load,
	reqLoad *models.Load,
) (result models.Load, defaultAttrs models.LoadAttributes, err error) {
	spanAttrs := append(otel.IntegrationAttrs(t.tms), otel.LoadAttrs(*reqLoad)...)

	ctx, metaSpan := otel.StartSpan(ctx, "UpdateLoadTurvo", spanAttrs)
	defer func() { metaSpan.End(err) }()

	defaultAttrs = t.GetDefaultLoadAttributes()

	if reqLoad.ExternalTMSID == "" {
		return result, defaultAttrs, errors.New("empty ExternalTMSID")
	}

	log.With(ctx, zap.String("externalTMSID", reqLoad.ExternalTMSID))

	queryParams := url.Values{}
	// Flag on wether or not Turvo should return the full response to the request.
	queryParams.Add("fullResponse", "true")

	// Retrieve info from Turvo as we need the current entities IDs
	var currentLoad LoadResponse
	getLoadURL := "/v1/shipments/" + reqLoad.ExternalTMSID

	err = t.getWithAuth(ctx, getLoadURL, queryParams, &currentLoad, s3backup.TypeLoads)
	if err != nil {
		return models.Load{}, t.GetDefaultLoadAttributes(), fmt.Errorf("get load failed: %w", err)
	}

	if strings.EqualFold(currentLoad.Status, "error") { // TODO use models.HTTPcurrentLoadError
		return result, defaultAttrs, fmt.Errorf("getting current load failed: %s - %s",
			currentLoad.Details.ErrorCode, currentLoad.Details.ErrorMessage)
	}

	var startDate, endDate *LoadUpdateDateTimeField
	var newGlobalRoute []LoadUpdateGlobalRoute

	// We can only support updates for now, not insert or removal
	if len(currentLoad.Details.GlobalRoute) > 0 {
		var firstPickupStop *GlobalRoute
		for _, stop := range currentLoad.Details.GlobalRoute {
			if strings.ToLower(stop.StopType.Value) == "pickup" {
				// Create a copy of the variable to avoid referencing the same memory address
				tempStop := stop
				firstPickupStop = &tempStop
				break // Exit the loop after finding the first match
			}
		}

		apptType := mapToValue(reqLoad.Pickup.ApptType, AppointmentTypeReverseMap)

		if firstPickupStop != nil {
			// Summary Start and EndDates are tied to pickup/delivery appts, not the actual times
			// But updating pickup/delivery appointment times doesn't automatically update shipment's summary dates;
			// must explicitly update them as well https://shorturl.at/hoO45
			if reqLoad.Pickup.ApptStartTime.Valid {
				startDate = &LoadUpdateDateTimeField{
					Date:     models.FallbackTime(reqLoad.Pickup.ReadyTime, reqLoad.Pickup.ApptStartTime),
					TimeZone: currentLoad.Details.StartDate.TimeZone,
				}
			}

			pickupLoc, err := time.LoadLocation(firstPickupStop.Timezone)
			if err != nil {
				return result, defaultAttrs, fmt.Errorf("error loading pickup timezone location: %w", err)
			}

			updatedStop := LoadUpdateGlobalRoute{
				ID:        firstPickupStop.ID,
				Operation: 1,
				Notes:     reqLoad.Pickup.ApptNote,
				PONumbers: strings.Split(reqLoad.Pickup.RefNumber, ","), // length must = 1
				SchedulingType: KeyValuePair{
					Key:   apptType.Key,
					Value: apptType.Value,
				},
				Appointment: LoadUpdateAppointment{
					Date:     reqLoad.Pickup.ApptStartTime.Time.In(pickupLoc),
					HasTime:  reqLoad.Pickup.ApptStartTime.Valid,
					TimeZone: firstPickupStop.Timezone,
				},
				PlannedAppointmentDate: LoadUpdatePlannedAppointmentDate{
					Appointment: LoadUpdateAppointmentRef{
						From: LoadUpdateAppointment{
							Date:     reqLoad.Pickup.ReadyTime.Time,
							HasTime:  reqLoad.Pickup.ReadyTime.Valid,
							TimeZone: firstPickupStop.Timezone,
						},
					},
				},
			}

			if reqLoad.Carrier.PickupStart.Valid {
				updatedStop.Attributes.Arrival = DateTimeField{
					Date: reqLoad.Carrier.PickupStart.Time, TimeZone: firstPickupStop.Attributes.Arrival.TimeZone}
			}

			if reqLoad.Carrier.PickupEnd.Valid {
				updatedStop.Attributes.Departed = DateTimeField{
					Date: reqLoad.Carrier.PickupEnd.Time, TimeZone: firstPickupStop.Attributes.Departed.TimeZone}
				if time.Now().After(reqLoad.Carrier.PickupEnd.Time) {
					updatedStop.State = "COMPLETED"
				}
			}

			newGlobalRoute = append(newGlobalRoute, updatedStop)
		}
	}

	if len(currentLoad.Details.GlobalRoute) > 0 {
		var firstDeliveryStop *GlobalRoute
		for _, stop := range currentLoad.Details.GlobalRoute {
			if strings.ToLower(stop.StopType.Value) == "delivery" {
				tempStop := stop
				firstDeliveryStop = &tempStop
				break // Exit the loop after finding the first match
			}
		}

		apptType := mapToValue(reqLoad.Consignee.ApptType, AppointmentTypeReverseMap)

		if firstDeliveryStop != nil {
			deliveryLoc, err := time.LoadLocation(firstDeliveryStop.Timezone)
			if err != nil {
				return result, defaultAttrs, fmt.Errorf("error loading delivery timezone location: %w", err)
			}

			if reqLoad.Consignee.ApptStartTime.Valid {
				endDate = &LoadUpdateDateTimeField{
					Date:     models.FallbackTime(reqLoad.Consignee.MustDeliver, reqLoad.Consignee.ApptStartTime),
					TimeZone: currentLoad.Details.EndDate.TimeZone,
				}
			}

			updatedDeliveryStop := LoadUpdateGlobalRoute{
				ID:        firstDeliveryStop.ID,
				Operation: 1,
				Notes:     reqLoad.Consignee.ApptNote,
				PONumbers: strings.Split(reqLoad.Consignee.RefNumber, ","),
				SchedulingType: KeyValuePair{
					Key:   apptType.Key,
					Value: apptType.Value,
				},
				Appointment: LoadUpdateAppointment{
					Date:     reqLoad.Consignee.ApptStartTime.Time.In(deliveryLoc),
					HasTime:  reqLoad.Consignee.ApptStartTime.Valid,
					TimeZone: firstDeliveryStop.Timezone,
				},
			}

			if reqLoad.Carrier.DeliveryStart.Valid {
				updatedDeliveryStop.Attributes.Arrival = DateTimeField{
					Date: reqLoad.Carrier.DeliveryStart.Time, TimeZone: firstDeliveryStop.Attributes.Arrival.TimeZone}
			}

			if reqLoad.Carrier.DeliveryEnd.Valid {
				updatedDeliveryStop.Attributes.Departed = DateTimeField{
					Date: reqLoad.Carrier.DeliveryEnd.Time, TimeZone: firstDeliveryStop.Attributes.Departed.TimeZone}

				if time.Now().After(reqLoad.Carrier.DeliveryEnd.Time) {
					updatedDeliveryStop.State = "COMPLETED"
				}

			}

			newGlobalRoute = append(newGlobalRoute, updatedDeliveryStop)

		}
	}
	reqBody := LoadUpdateRequest{
		StartDate:   startDate,
		EndDate:     endDate,
		GlobalRoute: newGlobalRoute,
	}

	var response LoadResponse
	updateLoadURL := "/v1/shipments/" + reqLoad.ExternalTMSID

	err = t.putWithAuth(ctx, updateLoadURL, queryParams, reqBody, &response, s3backup.TypeLoads)
	if err != nil {
		return result, defaultAttrs, fmt.Errorf("updating load failed: %w", err)
	}
	if strings.EqualFold(response.Status, "error") { // TODO: use errtypes.HTTPResponseError
		return result, defaultAttrs, fmt.Errorf("updating load failed: %s - %s",
			response.Details.ErrorCode, currentLoad.Details.ErrorMessage)
	}

	result, err = t.turvoShipmentToLoad(ctx, response.Details)
	if err != nil {
		return result, defaultAttrs, fmt.Errorf("error mapping updating Turvo shipment: %w", err)
	}

	return result, defaultAttrs, nil
}

func (t Turvo) GetLoadIDs(ctx context.Context, query models.SearchLoadsQuery) (
	[]string,
	error,
) {
	loadIDMap := make(map[string]struct{}) // Use map for deduplication

	// Fetch loads based on update date
	err := t.fetchLoadIDsByFilter(ctx, "updated", query, loadIDMap)
	if err != nil {
		return mapKeysToSlice(loadIDMap), fmt.Errorf("error fetching loads by update date: %w", err)
	}

	allLoadIDs := mapKeysToSlice(loadIDMap)
	log.Info(ctx, "Finished fetching all pages of load IDs", zap.Int("totalCount", len(allLoadIDs)))
	return allLoadIDs, nil
}

// Helper function to fetch load IDs based on a specific filter (created or updated at dates)
func (t Turvo) fetchLoadIDsByFilter(
	ctx context.Context,
	filterType string,
	query models.SearchLoadsQuery,
	loadIDMap map[string]struct{},
) error {

	const pageSize = 100
	start := 0 // Start index for pagination

	for {
		queryParams := url.Values{}
		if query.FromDate.Valid {
			queryParams.Set(fmt.Sprintf("%s[gte]", filterType), query.FromDate.Time.UTC().Format(time.RFC3339))
		}
		if query.ToDate.Valid {
			queryParams.Set(fmt.Sprintf("%s[lte]", filterType), query.ToDate.Time.UTC().Format(time.RFC3339))
		}
		queryParams.Set("pageSize", strconv.Itoa(pageSize))
		queryParams.Set("start", strconv.Itoa(start))

		var response FilterLoadsResponse

		// fullResponse=true doesn't work on this endpoint
		err := t.getWithAuth(ctx, "/v1/shipments/list", queryParams, &response, s3backup.TypeLoads)
		if err != nil {
			return fmt.Errorf("list loads failed on page (start %d): %w", start, err)
		}

		if strings.EqualFold(response.Status, "error") {
			return fmt.Errorf("list loads failed on page (start %d): %s - %s",
				start, response.Details.ErrorCode, response.Details.ErrorMessage)
		}

		if len(response.Details.Loads) == 0 {
			break
		}

		for _, load := range response.Details.Loads {
			if load.CustomID != "" {
				loadIDMap[fmt.Sprint(load.CustomID)] = struct{}{}
			}
		}
		start += pageSize
	}

	return nil
}

// Helper function to convert a map keys to a slice
func mapKeysToSlice(m map[string]struct{}) []string {
	result := make([]string, 0, len(m))
	for k := range m {
		result = append(result, k)
	}
	return result
}

func (t Turvo) GetLoad(
	ctx context.Context,
	freightTrackingID string,
) (result models.Load, defaultAttrs models.LoadAttributes, err error) {

	spanAttrs := append(
		otel.IntegrationAttrs(t.tms),
		attribute.String("freight_tracking_id", freightTrackingID),
	)
	ctx, metaSpan := otel.StartSpan(ctx, "GetLoadTurvo", spanAttrs)
	defer func() { metaSpan.End(err) }()

	defaultAttrs = t.GetDefaultLoadAttributes()

	shipmentID, err := t.FetchShipmentID(ctx, freightTrackingID, CustomIDType)
	if err != nil {
		return result, defaultAttrs, fmt.Errorf("fetchShipmentId failed: %w", err)
	}

	log.With(ctx, zap.Int("shipmentID", shipmentID))

	var response LoadResponse
	getLoadURL := "/v1/shipments/" + strconv.Itoa(shipmentID)

	queryParams := url.Values{}
	// Flag on whether or not Turvo should return the full response to the request.
	queryParams.Add("fullResponse", "true")

	err = t.getWithAuth(ctx, getLoadURL, queryParams, &response, s3backup.TypeLoads)
	if err != nil {
		return result, defaultAttrs, fmt.Errorf("get load failed: %w", err)
	}

	if strings.EqualFold(response.Status, "error") { // TODO: use errtypes.HTTPResponseError
		return result, defaultAttrs, fmt.Errorf(
			"get load failed: %s - %s",
			response.Details.ErrorCode,
			response.Details.ErrorMessage,
		)
	}

	result, err = t.turvoShipmentToLoad(ctx, response.Details)
	if err != nil {
		return result, defaultAttrs, fmt.Errorf("error mapping Turvo shipment: %w", err)
	}

	// Separate app.turvo.com/api endpoint flow to retrieve carrier order details (load RateData)
	// NOTE: We call the app turvo endpoint as it's the only way to get ratedata_carrier_cost and other fields
	var appResponse AppTurvoShipmentResponse
	fullURL := appTurvoURL + "/shipments/" + strconv.Itoa(shipmentID)

	appQueryParams := url.Values{}
	// Params needed for app.turvo.com/api shipment by ID endpoint to get summary details
	appQueryParams.Set("types", `["general","permissions","groups","commissions","bids","topCarriers"]`)
	appQueryParams.Set("event", "join")

	err = t.getWithAuth(ctx, fullURL, appQueryParams, &appResponse, s3backup.TypeLoads)
	if err != nil {
		return result, defaultAttrs, fmt.Errorf("get load failed: %w", err)
	}

	// Update load with app response (rate_data info)
	result = t.turvoAppShipmentToLoad(ctx, &result, appResponse)

	return result, defaultAttrs, nil
}

func (t Turvo) GetLoadsByIDType(
	ctx context.Context,
	id string, idType string,
) ([]models.Load,
	models.LoadAttributes, error) {
	var err error
	ctx, metaSpan := otel.StartSpan(ctx, "GetLoadsByIDTypeTurvo", otel.IntegrationAttrs(t.tms))
	defer func() { metaSpan.End(err) }()
	// get both the shipment ID and freight tracking ID from the shipment/list endpoint
	shipmentID, err := t.FetchShipmentID(ctx, id, idType)
	if err != nil {
		return nil, t.GetDefaultLoadAttributes(), fmt.Errorf("fetchShipmentID failed: %w", err)
	}

	log.With(ctx, zap.Int("externalTMSID", shipmentID))

	queryParams := url.Values{}
	// Flag on wether or not Turvo should return the full response to the request.
	queryParams.Add("fullResponse", "true")

	var response LoadResponse
	getLoadURL := "/v1/shipments/" + strconv.Itoa(shipmentID)

	err = t.getWithAuth(ctx, getLoadURL, queryParams, &response, s3backup.TypeLoads)
	if err != nil {
		return nil, t.GetDefaultLoadAttributes(), fmt.Errorf("get load failed: %w", err)
	}

	result, err := t.turvoShipmentToLoad(ctx, response.Details)
	if err != nil {
		return nil, t.GetDefaultLoadAttributes(), fmt.Errorf("error mapping Turvo shipment: %w", err)
	}

	return []models.Load{result}, t.GetDefaultLoadAttributes(), nil
}

func (t *Turvo) loadToTurvoShipment(ctx context.Context, load models.Load) (CreateLoadRequest, error) {

	startDateTimezone, endDateTimezone, err := lookupTimezone(ctx, load.Pickup, load.Consignee)
	if err != nil {
		return CreateLoadRequest{}, fmt.Errorf("error looking up timezones: %w", err)
	}

	pickupLoc, err := time.LoadLocation(startDateTimezone)
	if err != nil {
		return CreateLoadRequest{}, fmt.Errorf("error loading pickup timezone location: %w", err)
	}
	deliveryLoc, err := time.LoadLocation(endDateTimezone)
	if err != nil {
		return CreateLoadRequest{}, fmt.Errorf("error loading delivery timezone location: %w", err)
	}

	// matching values with Turvo-specific key value pairs
	equipmentType := mapToValue(load.Specifications.TransportType, EquipmentReverseMap)
	itemUnit := mapToValue(load.Specifications.TotalPieces.Unit, ItemUnitReverseMap)
	serviceType := mapToValue(load.Specifications.ServiceType, ServiceTypeReverseMap)
	currency := mapToValue(load.RateData.CustomerTotalCharge.Unit, CurrencyReverseMap)
	netWeightUnit := mapToValue(load.Specifications.NetWeight.Unit, WeightUnitReverseMap)
	grossWeightUnit := mapToValue(load.Specifications.TotalWeight.Unit, WeightUnitReverseMap)
	transportSize := mapToValue(load.Specifications.TransportSize, EquipmentSizeReverseMap)
	pickupApptType := mapToValue(load.Pickup.ApptType, AppointmentTypeReverseMap)
	consigneeApptType := mapToValue(load.Consignee.ApptType, AppointmentTypeReverseMap)
	transportWeightUnit := mapToValue(load.Specifications.TransportWeight.Unit, WeightUnitReverseMap)

	// location IDs
	customerID, err := strconv.Atoi(load.Customer.ExternalTMSID)
	if err != nil {
		return CreateLoadRequest{}, fmt.Errorf("error converting customer ID: %w", err)
	}
	pickupLocationID, err := strconv.Atoi(load.Pickup.ExternalTMSID)
	if err != nil {
		return CreateLoadRequest{}, fmt.Errorf("error converting pickup location ID: %w", err)
	}
	deliveryLocationID, err := strconv.Atoi(load.Consignee.ExternalTMSID)
	if err != nil {
		return CreateLoadRequest{}, fmt.Errorf("error converting delivery location ID: %w", err)
	}

	// Rate data fields
	var lineHaulChargeAdded, fscAdded bool
	var lineItems []ShipmentLineItem

	if load.RateData.CustomerLineHaulCharge.Val > 0 {
		lineHaulChargeAdded = true
		lineItems = append(lineItems, ShipmentLineItem{
			Code: KeyValuePair{
				Key:   LineHaulCodeKey,
				Value: LineHaulCodeValue,
			},
			Qty:      1,
			Price:    load.RateData.CustomerLineHaulCharge.Val,
			Amount:   load.RateData.CustomerLineHaulCharge.Val,
			Billable: true,
		})
	}

	if load.RateData.FSCFlatRate > 0 {
		fscAdded = true

		lineItems = append(lineItems, ShipmentLineItem{
			Code: KeyValuePair{
				Key:   FuelFlatCodeKey,
				Value: FuelFlatCodeValue,
			},
			Qty:      1,
			Price:    load.RateData.FSCFlatRate,
			Amount:   load.RateData.FSCFlatRate,
			Billable: true,
		})
	}

	// Add accessorial charges
	for _, accessorial := range load.RateData.CustomerLineItems {
		// Avoid double-charging now that accessorial charges introduced
		if accessorial.Label == LineHaulCodeValue && lineHaulChargeAdded {
			continue
		}
		if accessorial.Label == FuelFlatCodeValue && fscAdded {
			continue
		}

		key, ok := CostTypeReverseMap[strings.ToLower(accessorial.Label)]
		if !ok {
			return CreateLoadRequest{}, fmt.Errorf("invalid accessorial label: %s", accessorial.Label)
		}
		lineItems = append(lineItems, ShipmentLineItem{
			Code: KeyValuePair{
				Key:   key,
				Value: accessorial.Label,
			},
			Qty:      accessorial.Quantity,
			Price:    accessorial.RatePerUnitUSD,
			Amount:   accessorial.TotalChargeUSD,
			Billable: true,
			Notes:    accessorial.Note,
		})
	}

	// random UUIDs used as reference IDs in Turvo, they are not persisted in Turvo
	customerOrderSourceID := uuid.New().ID()
	pickupGlobalLocationSourceID := uuid.NewString()
	deliveryGlobalLocationSourceID := uuid.NewString()

	var pickupAppointment ShipmentAppointment
	var deliveryAppointment ShipmentAppointment

	// If ready time is not set but pickup date is, that means we just have a date
	if !load.Pickup.ReadyTime.Valid && !load.PickupDate.IsZero() {
		pickupAppointment = ShipmentAppointment{
			Date: time.Date(
				load.PickupDate.Year,
				load.PickupDate.Month,
				load.PickupDate.Day,
				0, 0, 0, 0,
				pickupLoc,
			),
			HasTime:  false,
			TimeZone: startDateTimezone,
		}
	} else {
		pickupAppointment = ShipmentAppointment{
			Date:     models.FallbackTime(load.Pickup.ReadyTime, load.Pickup.ApptStartTime).In(pickupLoc),
			HasTime:  load.Pickup.ReadyTime.Valid || load.Pickup.ApptStartTime.Valid,
			TimeZone: startDateTimezone,
		}
	}

	// If must deliver is not set but dropoff date is, that means we just have a date
	if !load.Consignee.MustDeliver.Valid && !load.DropoffDate.IsZero() {
		deliveryAppointment = ShipmentAppointment{
			Date: time.Date(
				load.DropoffDate.Year,
				load.DropoffDate.Month,
				load.DropoffDate.Day,
				0, 0, 0, 0,
				deliveryLoc,
			),
			HasTime:  false,
			TimeZone: endDateTimezone,
		}
	} else {
		deliveryAppointment = ShipmentAppointment{
			Date:     models.FallbackTime(load.Consignee.MustDeliver, load.Consignee.ApptStartTime).In(deliveryLoc),
			HasTime:  load.Consignee.MustDeliver.Valid || load.Consignee.ApptStartTime.Valid,
			TimeZone: endDateTimezone,
		}
	}

	customerOrderRef := CustomerOrderRef{
		CustomerID:            customerID,
		CustomerOrderSourceID: customerOrderSourceID,
	}

	shippingEquipment := ShipmentEquipment{
		Type:        equipmentType,
		Size:        transportSize,
		Weight:      load.Specifications.TransportWeight.Val,
		WeightUnits: transportWeightUnit,
	}

	if isRefrigeratedEquipment(equipmentType) {
		shippingEquipment.Temp = load.Specifications.MinTempFahrenheit
		shippingEquipment.TempUnits = KeyValuePair{
			Key:   FahrenheitKey,
			Value: FahrenheitValue,
		}
	}

	mappedShipment := CreateLoadRequest{
		LTLShipment: false,
		StartDate: DateTimeField{
			Date:     models.FallbackTime(load.Pickup.ReadyTime, load.Pickup.ApptStartTime),
			TimeZone: startDateTimezone,
		},
		EndDate: DateTimeField{
			Date:     models.FallbackTime(load.Consignee.MustDeliver, load.Consignee.ApptStartTime),
			TimeZone: endDateTimezone,
		},
		// SDS wants loads built as draft not tendered
		// TODO: Load building config for scalable way to handle differences in how customers want to build loads
		Status: helpers.Ternary(strings.Contains(t.tms.Username, "sdslogistics"),
			CreateLoadStatus{
				Code: KeyValuePair{
					Key:   DraftStatusKey,
					Value: DraftStatusValue,
				},
			},
			CreateLoadStatus{
				Code: KeyValuePair{
					Key:   TenderedStatusKey,
					Value: TenderedStatusValue,
				},
			},
		),
		Lane: Lane{
			Start: fmt.Sprintf(
				"%s, %s", load.Pickup.City, load.Pickup.State),
			End: fmt.Sprintf(
				"%s, %s", load.Consignee.City, load.Consignee.State),
		},
		Equipment: []ShipmentEquipment{shippingEquipment},
		ShipmentGlobalRoute: []ShipmentGlobalRoute{
			{
				Name: load.Pickup.Name,
				StopType: KeyValuePair{
					Key:   PickupStopTypeKey,
					Value: PickupStopTypeValue,
				},
				Location: Location{
					ID: pickupLocationID,
				},
				Sequence:                   0,
				SegmentSequence:            0,
				GlobalShipLocationSourceID: pickupGlobalLocationSourceID,
				State:                      OpenState,
				SchedulingType: KeyValuePair{
					Key:   pickupApptType.Key,
					Value: pickupApptType.Value,
				},
				Timezone:      startDateTimezone,
				Appointment:   pickupAppointment,
				CustomerOrder: []CustomerOrderRef{customerOrderRef},
				Transportation: Transportation{
					Mode: KeyValuePair{
						Key:   TLModeKey,
						Value: TLModeValue,
					},
					ServiceType: serviceType,
				},
				PoNumbers: []string{load.Pickup.RefNumber},
			},
			{
				Name: load.Consignee.Name,
				StopType: KeyValuePair{
					Key:   DeliveryStopTypeKey,
					Value: DeliveryStopTypeValue,
				},
				Location: Location{
					ID: deliveryLocationID,
				},
				Sequence:                   1,
				SegmentSequence:            0,
				GlobalShipLocationSourceID: deliveryGlobalLocationSourceID,
				State:                      OpenState,
				SchedulingType: KeyValuePair{
					Key:   consigneeApptType.Key,
					Value: consigneeApptType.Value,
				},
				Timezone:      endDateTimezone,
				Appointment:   deliveryAppointment,
				CustomerOrder: []CustomerOrderRef{customerOrderRef},
				Transportation: Transportation{
					Mode: KeyValuePair{
						Key:   TLModeKey,
						Value: TLModeValue,
					},
					ServiceType: serviceType,
				},
				PoNumbers: []string{load.Consignee.RefNumber},
			},
		},
		CustomerOrder: []ShipmentCustomerOrder{
			{
				CustomerOrderSourceID: customerOrderSourceID,
				Customer: Customer{
					ID:   customerID,
					Name: load.Customer.Name,
				},
				Costs: ShipmentCosts{
					TotalAmount:         load.RateData.CustomerTotalCharge.Val,
					TransactionCurrency: currency,
					LineItem:            lineItems,
				},
				ExternalIDs: []ExternalIDs{
					{
						Type: KeyValuePair{
							Key:   ReferenceExternalIDTypeKey,
							Value: ReferenceExternalIDTypeValue,
						},
						Value:              load.Customer.RefNumber,
						CopyToCarrierOrder: false,
					},
					{
						Type: KeyValuePair{
							Key:   POExternalIDTypeKey,
							Value: POExternalIDTypeValue,
						},
						Value:              load.PONums,
						CopyToCarrierOrder: false,
					},
				},
				Items: []ShipmentItems{
					{
						Name: load.Specifications.Commodities,
						Qty:  load.Specifications.TotalPieces.Val,
						NetWeight: Weight{
							Weight:     load.Specifications.NetWeight.Val,
							WeightUnit: netWeightUnit,
						},
						GrossWeight: Weight{
							Weight:     load.Specifications.TotalWeight.Val,
							WeightUnit: grossWeightUnit,
						},
						Unit: itemUnit,
						PickupLocation: []LocationRef{
							{
								Name:                       load.Pickup.Name,
								GlobalShipLocationSourceID: pickupGlobalLocationSourceID,
							},
						},
						DeliveryLocation: []LocationRef{
							{
								Name:                       load.Consignee.Name,
								GlobalShipLocationSourceID: deliveryGlobalLocationSourceID,
							},
						},
					},
				},
			},
		},
		ModeInfo: []ShipmentModeInfo{
			{
				Operation:             0,
				SourceSegmentSequence: "0",
				Mode: KeyValuePair{
					Key:   TLModeKey,
					Value: TLModeValue,
				},
				ServiceType: serviceType,
			},
		},
	}

	// TODO: figure out how to get the user's TMS groups
	// if len(userTMSGroups) > 0 {
	// 	mappedShipment.Groups = []GroupsWithOperation{}
	// 	for _, group := range userTMSGroups {
	// 		mappedShipment.Groups = append(mappedShipment.Groups, GroupsWithOperation{
	// 			ID:        group.ID,
	// 			Name:      group.Name,
	// 			Operation: 0,
	// 		})
	// 	}
	// }

	return mappedShipment, nil
}

// TODO: Refactor to remove nesting of ifs...
// We can do this by leveraging the fact that Go will return the zero value
// for a struct if it is not initialized.
// This was done this way at first to make it easier to read and understand
// the nested structure of the Turvo response and mapping.
func (t *Turvo) turvoShipmentToLoad(
	ctx context.Context,
	turvoShipmentDetails LoadDetails,
) (models.Load, error) {

	result := models.Load{
		ExternalTMSID:     strconv.Itoa(turvoShipmentDetails.ID),
		FreightTrackingID: turvoShipmentDetails.CustomID,
		ServiceID:         t.tms.ServiceID,
		TMSID:             t.tms.ID,
		LoadCoreInfo: models.LoadCoreInfo{
			Status: turvoShipmentDetails.Status.Code.Value,
			Specifications: models.Specifications{
				TransportType: func() string {
					if len(turvoShipmentDetails.Equipment) > 0 {
						return turvoShipmentDetails.Equipment[0].Type.Value
					}
					return ""
				}(),
				ServiceType: turvoShipmentDetails.Transportation.ServiceType.Value,
			},
		},
	}

	switch turvoShipmentDetails.Transportation.Mode.Key {
	case TLModeKey:
		result.Mode = models.TLMode
	case LTLModeKey:
		result.Mode = models.LTLMode
	case AirModeKey:
		result.Mode = models.AirMode
	case DrayageModeKey:
		result.Mode = models.DrayageMode
	}

	result.MoreThanTwoStops = len(turvoShipmentDetails.GlobalRoute) > 2

	if len(turvoShipmentDetails.CustomerOrder) > 0 {
		customerOrder := turvoShipmentDetails.CustomerOrder[0]

		result.Customer.ExternalTMSID = strconv.Itoa(customerOrder.Customer.ID)
		result.Customer.Name = customerOrder.Customer.Name

		customer, err := t.GetCustomerByID(ctx, customerOrder.Customer.ID)
		if err != nil {
			log.Info(ctx, "get customer failed, skipping information", zap.Error(err))
		} else {
			// Customer Fields
			if len(customer.Address) > 0 {
				address := customer.Address[0]
				result.Customer.AddressLine1 = address.Line1
				result.Customer.AddressLine2 = address.Line2
				result.Customer.City = address.City
				result.Customer.State = address.State
				result.Customer.Zipcode = address.Zip
				result.Customer.Country = address.Country
			}

			result.Customer.Contact = customer.Name

			if len(customer.Phone) > 0 {
				result.Customer.Phone = customer.Phone[0].Number
			}

			if len(customer.Email) > 0 {
				result.Customer.Email = customer.Email[0].Email
			}

			// Bill To fields
			result.BillTo.ExternalTMSID = strconv.Itoa(customer.ID)

			if len(customer.Billings) > 0 {
				billing := customer.Billings[0]
				result.BillTo.Name = *billing.ToName
				result.BillTo.AddressLine1 = billing.Address.Line1
				result.BillTo.AddressLine2 = billing.Address.Line2
				result.BillTo.City = billing.Address.City
				result.BillTo.State = billing.Address.State
				result.BillTo.Zipcode = billing.Address.Zip
				result.BillTo.Country = billing.Address.Country
				result.BillTo.Phone = billing.Phone.Number
			}
		}

		// TODO: add support mapping for multiple items. Map to result.Commodities (array of commodities),
		// not Specifications.Commodities (only a string)
		if len(customerOrder.Items) > 0 {
			result.Specifications.Commodities = customerOrder.Items[0].Name
			result.Specifications.TotalPieces.Val = customerOrder.Items[0].Qty
			result.Specifications.TotalPieces.Unit = customerOrder.Items[0].Unit.Value
			result.Specifications.TotalWeight.Val = customerOrder.Items[0].GrossWeight.Weight
			result.Specifications.TotalWeight.Unit = customerOrder.Items[0].GrossWeight.WeightUnit.Value
			result.Specifications.NetWeight.Val = customerOrder.Items[0].NetWeight.Weight
			result.Specifications.NetWeight.Unit = customerOrder.Items[0].NetWeight.WeightUnit.Value
		}

	}

	if len(turvoShipmentDetails.CarrierOrder) > 0 {
		carrierOrder := turvoShipmentDetails.CarrierOrder[0]

		if len(carrierOrder.Drivers) > 0 {
			firstDriver := carrierOrder.Drivers[0]

			result.Carrier.FirstDriverName = firstDriver.Name
			result.Carrier.FirstDriverPhone = firstDriver.Phone.Number
		}

		if len(carrierOrder.Drivers) > 1 {
			secondDriver := carrierOrder.Drivers[1]

			result.Carrier.SecondDriverName = secondDriver.Name
			result.Carrier.SecondDriverPhone = secondDriver.Phone.Number
		}

		carrier, err := t.GetCarrierByID(ctx, carrierOrder.Carrier.ID)
		if err != nil {
			log.Info(ctx, "get carrier failed, skipping information", zap.Error(err))
		} else {
			result.Carrier.ExternalTMSID = strconv.Itoa(carrier.ID)
			result.Carrier.MCNumber = carrier.McNumber

			// Handle DotNumber typed as any
			switch v := carrier.DotNumber.(type) {
			case string:
				result.Carrier.DOTNumber = v
			case float64:
				result.Carrier.DOTNumber = strconv.FormatFloat(v, 'f', -1, 64)
			case int:
				result.Carrier.DOTNumber = strconv.Itoa(v)
			case nil:
				result.Carrier.DOTNumber = ""
			default:
				log.Warn(
					ctx,
					"Unexpected type for carrier.DotNumber",
					zap.Any("dotNumberValue", v),
					zap.Int("carrierID", carrier.ID),
				)
				result.Carrier.DOTNumber = fmt.Sprintf("%v", v)
			}
			result.Carrier.Name = carrier.Name

			if len(carrier.Phone) > 0 {
				result.Carrier.Phone = carrier.Phone[0].Number
			}

			result.Carrier.Dispatcher = carrier.Contact.Name

			if len(carrier.Scac) > 0 {
				result.Carrier.SCAC = carrier.Scac[0].SCAC
			}

			if len(carrier.Email) > 0 {
				result.Carrier.Email = carrier.Email[0].Email
			}

			if len(carrier.Address) > 0 {
				address := carrier.Address[0]

				result.Carrier.DispatchCity = address.City
				result.Carrier.DispatchState = address.State
			}
		}
	}

	// We only support one equipment type per shipment
	if len(turvoShipmentDetails.Equipment) > 0 {
		result.Specifications.TransportSize = turvoShipmentDetails.Equipment[0].Size.Value
		result.Specifications.MinTempFahrenheit = turvoShipmentDetails.Equipment[0].Temp
		result.Specifications.MaxTempFahrenheit = turvoShipmentDetails.Equipment[0].Temp
		result.Specifications.TransportWeight.Val = turvoShipmentDetails.Equipment[0].Weight
		result.Specifications.TransportWeight.Unit = turvoShipmentDetails.Equipment[0].WeightUnits.Value
	}

	var firstGlobalPickupStop *GlobalRoute
	var poNumbers []string
	for _, stop := range turvoShipmentDetails.GlobalRoute {
		if strings.ToLower(stop.StopType.Value) == "pickup" {
			tempStop := stop
			firstGlobalPickupStop = &tempStop
			break // Exit the loop after finding the first match
		}
	}

	if firstGlobalPickupStop != nil {
		poNumbers = firstGlobalPickupStop.PoNumbers
		result.Pickup.ExternalTMSID = strconv.Itoa(firstGlobalPickupStop.ID)
		result.Pickup.ApptNote = firstGlobalPickupStop.Notes
		result.Pickup.RefNumber = joinNonEmptyStrings(firstGlobalPickupStop.PoNumbers, ",")
		result.Pickup.Timezone = firstGlobalPickupStop.Timezone
		result.Pickup.ApptType = firstGlobalPickupStop.SchedulingType.Value

		result.Pickup.ApptStartTime = models.NullTime{
			Time:  firstGlobalPickupStop.Appointment.Date,
			Valid: !firstGlobalPickupStop.Appointment.Date.IsZero(),
		}

		// "The requested date/time represents the date/time in which the customer wants their goods to be delivered.
		// The planned pickup/delivery date is the recommended date/time the goods are to be picked up or delivered
		// based on the requested dates from the different orders on the shipment." https://shorturl.at/syGP5
		// Since the former isn't in the API, we use the latter for Pickup.ReadyTime
		result.Pickup.ReadyTime = models.NullTime{
			Time:  firstGlobalPickupStop.PlannedAppointmentDate.Appointment.From.Date,
			Valid: firstGlobalPickupStop.PlannedAppointmentDate.Appointment.From.HasTime,
		}

		result.Carrier.ExpectedPickupTime = models.NullTime{
			Time:  firstGlobalPickupStop.Appointment.Date,
			Valid: firstGlobalPickupStop.Appointment.HasTime,
		}

		// Set pickup times from attributes if they exist
		if !firstGlobalPickupStop.Attributes.Arrival.Date.IsZero() {
			result.Carrier.PickupStart = models.NullTime{
				Time:  firstGlobalPickupStop.Attributes.Arrival.Date,
				Valid: true,
			}
		}

		if !firstGlobalPickupStop.Attributes.Departed.Date.IsZero() {
			result.Carrier.PickupEnd = models.NullTime{
				Time:  firstGlobalPickupStop.Attributes.Departed.Date,
				Valid: true,
			}
		}

		pickupLocation, err := t.GetLocationByID(ctx, firstGlobalPickupStop.Location.ID)
		if err != nil {
			return models.Load{}, fmt.Errorf("get pickup location failed: %w", err)
		}
		result.Pickup.Name = pickupLocation.Name
		result.Pickup.Contact = pickupLocation.Contact.Name

		var primaryPickupAddress *Address
		for _, address := range pickupLocation.Address {
			if address.IsPrimary {
				tempAddress := address
				primaryPickupAddress = &tempAddress
				break // Exit the loop after finding the first match
			}
		}

		// Fallback: if pickup location has no primary address and isn't empty, use the first one
		if primaryPickupAddress == nil && len(pickupLocation.Address) > 0 {
			primaryPickupAddress = &pickupLocation.Address[0]
		}

		if primaryPickupAddress != nil {
			pickupAddress := *primaryPickupAddress
			result.Pickup.AddressLine1 = pickupAddress.Line1
			result.Pickup.AddressLine2 = pickupAddress.Line2
			result.Pickup.City = pickupAddress.City
			result.Pickup.State = pickupAddress.State
			result.Pickup.Zipcode = pickupAddress.Zip
			result.Pickup.Country = pickupAddress.Country
		}

		if len(pickupLocation.Phone) > 0 {
			result.Pickup.Phone = pickupLocation.Phone[0].Number
		}

		if len(pickupLocation.Email) > 0 {
			result.Pickup.Email = pickupLocation.Email[0].Email
		}
	}

	var firstGlobalDeliveryStop *GlobalRoute
	for _, stop := range turvoShipmentDetails.GlobalRoute {
		if strings.ToLower(stop.StopType.Value) == "delivery" {
			tempStop := stop
			firstGlobalDeliveryStop = &tempStop
			break // Exit the loop after finding the first match
		}
	}

	if firstGlobalDeliveryStop != nil {
		// collect any PO numbers we didn't already get from the firstGlobalPickupStop
		for _, poNum := range firstGlobalDeliveryStop.PoNumbers {
			if !slices.Contains(poNumbers, poNum) {
				poNumbers = append(poNumbers, poNum)
			}
		}
		result.Consignee.ExternalTMSID = strconv.Itoa(firstGlobalDeliveryStop.ID)
		result.Consignee.RefNumber = joinNonEmptyStrings(firstGlobalDeliveryStop.PoNumbers, ",")
		result.Consignee.ApptNote = firstGlobalDeliveryStop.Notes
		result.Consignee.Timezone = firstGlobalDeliveryStop.Timezone
		result.Consignee.ApptType = firstGlobalDeliveryStop.SchedulingType.Value

		result.Consignee.ApptStartTime = models.NullTime{
			Time:  firstGlobalDeliveryStop.Appointment.Date,
			Valid: !firstGlobalDeliveryStop.Appointment.Date.IsZero(), // do not null it when there's a date but no time
		}

		result.Carrier.ExpectedDeliveryTime = models.NullTime{
			Time:  firstGlobalDeliveryStop.Appointment.Date,
			Valid: firstGlobalDeliveryStop.Appointment.HasTime,
		}

		if !firstGlobalDeliveryStop.Attributes.Arrival.Date.IsZero() {
			result.Carrier.DeliveryStart = models.NullTime{
				Time:  firstGlobalDeliveryStop.Attributes.Arrival.Date,
				Valid: true,
			}
		}

		if !firstGlobalDeliveryStop.Attributes.Departed.Date.IsZero() {
			result.Carrier.DeliveryEnd = models.NullTime{
				Time:  firstGlobalDeliveryStop.Attributes.Departed.Date,
				Valid: true,
			}
		}

		// Fallback: If no actual times arrival/departure times, use appointment time
		if !result.Carrier.DeliveryStart.Valid && firstGlobalDeliveryStop.Appointment.HasTime {
			result.Carrier.DeliveryStart = models.NullTime{
				Time:  firstGlobalDeliveryStop.Appointment.Date,
				Valid: true,
			}
		}

		if !result.Carrier.DeliveryEnd.Valid && firstGlobalDeliveryStop.Appointment.HasTime {
			result.Carrier.DeliveryEnd = models.NullTime{
				Time:  firstGlobalDeliveryStop.Appointment.Date,
				Valid: true,
			}
		}

		deliveryLocation, err := t.GetLocationByID(ctx, firstGlobalDeliveryStop.Location.ID)
		if err != nil {
			return models.Load{}, fmt.Errorf("get delivery location failed: %w", err)
		}
		result.Consignee.Name = deliveryLocation.Name
		result.Consignee.Contact = deliveryLocation.Contact.Name

		var primaryDeliveryAddress *Address
		for _, address := range deliveryLocation.Address {
			if address.IsPrimary {
				tempAddress := address
				primaryDeliveryAddress = &tempAddress
				break // Exit the loop after finding the first match
			}
		}

		// Fallback: if delivery location has no primary address and isn't empty, use the first one
		if primaryDeliveryAddress == nil && len(deliveryLocation.Address) > 0 {
			primaryDeliveryAddress = &deliveryLocation.Address[0]
		}

		if primaryDeliveryAddress != nil {
			deliveryAddress := *primaryDeliveryAddress
			result.Consignee.AddressLine1 = deliveryAddress.Line1
			result.Consignee.AddressLine2 = deliveryAddress.Line2
			result.Consignee.City = deliveryAddress.City
			result.Consignee.State = deliveryAddress.State
			result.Consignee.Zipcode = deliveryAddress.Zip
			result.Consignee.Country = deliveryAddress.Country
		}

		if len(deliveryLocation.Email) > 0 {
			result.Consignee.Email = deliveryLocation.Email[0].Email
		}

		if len(deliveryLocation.Phone) > 0 {
			result.Consignee.Phone = deliveryLocation.Phone[0].Number
		}
	}

	// collect all PO numbers from routes above so we can filter out duplicates
	result.PONums = joinNonEmptyStrings(poNumbers, ",")

	return result, nil
}

// parseDistanceValue parses a distance value from json.RawMessage which can be either a float32 or a string
func parseDistanceValue(ctx context.Context, rawValue json.RawMessage) float32 {
	if len(rawValue) == 0 {
		return 0
	}

	var val any
	if err := json.Unmarshal(rawValue, &val); err != nil {
		log.Warn(ctx, "Failed to parse distance value", zap.String("raw_value", string(rawValue)))
		return 0
	}

	switch v := val.(type) {
	case float64:
		return float32(v)
	case float32:
		return v
	case int:
		return float32(v)
	case int64:
		return float32(v)
	case string:
		if v == "" {
			return 0
		}
		if f, err := strconv.ParseFloat(v, 32); err == nil {
			return float32(f)
		}
	}

	log.Warn(ctx, "Unexpected type for distance value", zap.String("raw_value", string(rawValue)))
	return 0
}

// Add additional rate_data and other fields to load from the Turvo App API response that are not in publicapi
// TODO: support loads that have multiple CarrierOrders typically routes with multiple stops
func (t *Turvo) turvoAppShipmentToLoad(
	ctx context.Context,
	load *models.Load,
	appResponse AppTurvoShipmentResponse,
) models.Load {

	load.Specifications.TotalDistance.Val = parseDistanceValue(ctx, appResponse.Details.GlobalRoute.Distance.Value)
	load.Specifications.TotalDistance.Unit = appResponse.Details.GlobalRoute.Distance.Units.Value
	log.Debug(
		ctx,
		"Found total distance",
		zap.Any("distance value", load.Specifications.TotalDistance.Val),
		zap.Any("distance unit", load.Specifications.TotalDistance.Unit),
	)

	currency := "USD"
	if len(appResponse.Details.GlobalRoute.ModeInfo) > 0 {
		currency = appResponse.Details.GlobalRoute.ModeInfo[0].TotalRouteFragmentValue.Currency.Value
	}

	// Loop through CustomerOrder line items to find CustomerLineHaulCharge and accessorial charges
	if len(appResponse.Details.CustomerOrders) > 0 {
		costs := appResponse.Details.CustomerOrders[0].Costs
		if len(costs.LineItems) > 0 {
			var customerLineItems []models.AccessorialCharge
			for _, lineItem := range costs.LineItems {
				if lineItem.Type.Key == FreightFlatCodeKey && lineItem.Type.Value == FreightFlatCodeValue {
					load.RateData.CustomerLineHaulCharge.Val = lineItem.Amount
					load.RateData.CustomerLineHaulCharge.Unit = currency
					if lineItem.Qty > 0 {
						load.RateData.CustomerRateNumUnits = lineItem.Qty
						load.RateData.CustomerLineHaulRate = lineItem.Price
					}
				} else if lineItem.Billable && !lineItem.Deleted {
					// This is an accessorial charge
					accessorial := models.AccessorialCharge{
						Label:          lineItem.Type.Value,
						UnitBasis:      lineItem.RateQualifier.Value,
						Quantity:       lineItem.Qty,
						RatePerUnitUSD: lineItem.Price,
						TotalChargeUSD: lineItem.Amount,
						Note:           lineItem.Notes,
					}
					customerLineItems = append(customerLineItems, accessorial)
				}
			}
			load.RateData.CustomerLineItems = customerLineItems
		}
		load.RateData.CustomerTotalCharge.Val = costs.Amount
		load.RateData.CustomerTotalCharge.Unit = currency
	}

	// Loop through CarrierOrder line items to find CarrierLineHaulCharge and accessorial charges
	if len(appResponse.Details.CarrierOrders) > 0 {
		costs := appResponse.Details.CarrierOrders[0].Costs
		if len(costs.LineItems) > 0 {
			var carrierLineItems []models.AccessorialCharge
			for _, lineItem := range costs.LineItems {
				if lineItem.Type.Key == FreightFlatCodeKey && lineItem.Type.Value == FreightFlatCodeValue {
					load.RateData.CarrierLineHaulCharge.Val = lineItem.Amount
					load.RateData.CarrierLineHaulCharge.Unit = currency
					if lineItem.Qty > 0 {
						load.RateData.CarrierRateNumUnits = lineItem.Qty
						load.RateData.CarrierLineHaulRate = lineItem.Price
					}
				} else if lineItem.Billable && !lineItem.Deleted {
					// This is an accessorial charge
					accessorial := models.AccessorialCharge{
						Label:          lineItem.Type.Value,
						UnitBasis:      lineItem.RateQualifier.Value,
						Quantity:       lineItem.Qty,
						RatePerUnitUSD: lineItem.Price,
						TotalChargeUSD: lineItem.Amount,
						Note:           lineItem.Notes,
					}
					carrierLineItems = append(carrierLineItems, accessorial)
				}
			}
			load.RateData.CarrierLineItems = carrierLineItems
		}
		if appResponse.Details.Margin.TotalPayables != 0 {
			load.RateData.CarrierCost = &appResponse.Details.Margin.TotalPayables
			load.RateData.CarrierCostCurrency = currency
		} else if costs.Amount != 0 {
			load.RateData.CarrierCost = &costs.Amount
			load.RateData.CarrierCostCurrency = currency
		}
	} else if appResponse.Details.Margin.TotalPayables != 0 {
		load.RateData.CarrierCost = &appResponse.Details.Margin.TotalPayables
		load.RateData.CarrierCostCurrency = currency
	}

	load.RateData.NetProfitUSD = appResponse.Details.Margin.ActualMarginVal
	load.RateData.ProfitPercent = appResponse.Details.Margin.ActualMargin

	return *load
}

// Query Turvo shipments with any ID type (customId, poNumber, etc)
func (t Turvo) FetchShipmentID(ctx context.Context, id, idType string) (externalTMSID int, err error) {
	if idType != CustomIDType {
		// Try direct lookup first
		if internalID, convErr := strconv.Atoi(id); convErr == nil {
			queryParams := url.Values{}
			queryParams.Add("fullResponse", "true")

			var response LoadResponse
			directURL := fmt.Sprintf("/v1/shipments/%d", internalID)

			err = t.getWithAuth(ctx, directURL, queryParams, &response, s3backup.TypeLoads)
			if err == nil && !strings.EqualFold(response.Status, "error") {
				return response.Details.ID, nil
			}
			log.Info(
				ctx,
				"Direct shipment lookup failed, falling back to list search",
				zap.String("id", id),
				zap.String("url", directURL),
				zap.Error(err))
		}
	}

	// Fall back to list search
	queryParams := url.Values{}
	queryParams.Add(idType, id)

	var response FilterLoadsResponse
	filterLoadsURL := "/v1/shipments/list"

	err = t.getWithAuth(ctx, filterLoadsURL, queryParams, &response, s3backup.TypeLoads)
	if err != nil {
		return 0, fmt.Errorf("list shipments failed: %w", err)
	}

	if strings.EqualFold(response.Status, "error") {
		return externalTMSID, fmt.Errorf("list shipments failed: %s - %s",
			response.Details.ErrorCode, response.Details.ErrorMessage)
	}

	if len(response.Details.Loads) == 0 {
		return 0, errtypes.EntityNotFoundError(t.tms, id, idType)
	}

	return response.Details.Loads[0].ID, nil
}

func (t *Turvo) GetCustomerByID(ctx context.Context, customerID int) (CustomerDetails, error) {
	queryParams := url.Values{}
	// Flag on wether or not Turvo should return the full response to the request.
	queryParams.Add("fullResponse", "true")

	var response CustomerResponse
	getLoadURL := "/v1/customers/" + strconv.Itoa(customerID)

	err := t.getWithAuth(ctx, getLoadURL, queryParams, &response, s3backup.TypeLoads)
	if err != nil {
		return CustomerDetails{}, err
	}
	if strings.EqualFold(response.Status, "error") { // TODO: use errtypes.HTTPResponseError
		return CustomerDetails{}, fmt.Errorf("%s - %s",
			response.Details.ErrorCode, response.Details.ErrorMessage)
	}

	return response.Details, nil
}

func (t *Turvo) GetCarrierByID(ctx context.Context, carrierID int) (CarrierDetails, error) {
	queryParams := url.Values{}
	// Flag on wether or not Turvo should return the full response to the request.
	queryParams.Add("fullResponse", "true")

	var response CarrierResponse
	getLoadURL := "/v1/carriers/" + strconv.Itoa(carrierID)

	err := t.getWithAuth(ctx, getLoadURL, queryParams, &response, s3backup.TypeLoads)
	if err != nil {
		return CarrierDetails{}, err
	}
	if strings.EqualFold(response.Status, "error") { // TODO: use errtypes.HTTPResponseError
		return CarrierDetails{}, fmt.Errorf("%s - %s",
			response.Details.ErrorCode, response.Details.ErrorMessage)
	}

	return response.Details, nil
}

// helper function to collect notes for GetLocationByID
func collectNotes(response AppTurvoLocationResponse) []string {
	var notes []string
	if response.Details.Basic.SpecialInstructions != "" {
		notes = append(notes, response.Details.Basic.SpecialInstructions)
	}

	if len(response.Notes.Data) > 0 {
		for _, note := range response.Notes.Data {
			if note.Body != "" {
				notes = append(notes, note.Body)
			}
		}
	}
	return notes
}

func (t *Turvo) GetLocationByID(
	ctx context.Context,
	locationID int,
	opts ...models.TMSOption,
) (CustomerDetails, error) {

	options := &models.TMSOptions{}
	options.Apply(opts...)

	// app.turvo.com/api endpoint flow
	if options.ChangeHostName {
		var response AppTurvoLocationResponse

		fullURL := fmt.Sprintf("https://app.turvo.com/api/locations/%d", locationID)
		queryParams := make(url.Values)
		queryParams.Set("card", "details")
		queryParams.Set("types", `["general","permissions"]`)
		queryParams.Set("calendarConfigRequired", "false")

		if err := t.getWithAuth(ctx, fullURL, queryParams, &response, s3backup.TypeLocations); err != nil {
			return CustomerDetails{}, fmt.Errorf("failed to get location details: %w", err)
		}
		details := CustomerDetails{
			ID:                  locationID,
			Name:                response.Details.Basic.Name,
			SpecialInstructions: response.Details.Basic.SpecialInstructions,
		}

		notes := collectNotes(response)

		if len(notes) > 0 {
			details.Notes = strings.Join(notes, "\n")
		}

		return details, nil
	}

	// standard publicapi.turvo.com endpoint flow
	queryParams := url.Values{}
	// Flag on wether or not Turvo should return the full response to the request.
	queryParams.Add("fullResponse", "true")

	var response CustomerResponse
	getLoadURL := "/v1/locations/" + strconv.Itoa(locationID)

	err := t.getWithAuth(ctx, getLoadURL, queryParams, &response, s3backup.TypeLoads)
	if err != nil {
		return CustomerDetails{}, err
	}
	if strings.EqualFold(response.Status, "error") { // TODO: use errtypes.HTTPResponseError
		return CustomerDetails{}, fmt.Errorf("%s - %s",
			response.Details.ErrorCode, response.Details.ErrorMessage)
	}

	return response.Details, nil

}

// strings.Join retains the empty string. This wrapper function does not.
func joinNonEmptyStrings(elems []string, sep string) string {
	filtered := []string{}
	for _, s := range elems {
		if strings.TrimSpace(s) == "" {
			continue
		}
		filtered = append(filtered, s)
	}

	return strings.Join(filtered, sep)
}

func isRefrigeratedEquipment(equipType KeyValuePair) bool {
	return strings.Contains(strings.ToLower(equipType.Value), "reefer") ||
		strings.Contains(strings.ToLower(equipType.Value), "refrigerated")
}
