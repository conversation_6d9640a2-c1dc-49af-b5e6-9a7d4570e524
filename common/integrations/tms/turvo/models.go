package turvo

import (
	"encoding/json"
	"fmt"
	"time"
)

// These constants are specific key-value pairs taken from the Turvo api
// https://app.turvo.com/lobby/documentation#tag/Lookups
const (
	ReeferTemp = float32(32)

	FahrenheitKey   = "1510"
	FahrenheitValue = "°F"

	FiftyThreeFTKey   = "1306"
	FiftyThreeFTValue = "53 ft"

	VanKey       = "1200" // NOTE: Turvo has other key value pairs for van types (Van - sprinter, vented, curtain side)
	VanValue     = "Van"  // https://app.turvo.com/lobby/documentation#tag/Equipment
	ReeferKey    = "24487"
	ReeferValue  = "Reefer"
	FlatbedKey   = "1204"
	FlatbedValue = "Flatbed"

	QuoteStatusKey   = "2100"
	QuoteStatusValue = "Quote active"

	AirModeKey       = "24104"
	AirModeValue     = "Air"
	TLModeKey        = "24105"
	TLModeValue      = "TL"
	DrayageModeKey   = "24106"
	DrayageModeValue = "Drayage"
	LTLModeKey       = "24107"
	LTLModeValue     = "LTL"

	AnyServiceKey   = "24304"
	AnyServiceValue = "Any"

	ReferenceExternalIDTypeKey   = "1401"
	ReferenceExternalIDTypeValue = "Reference #"

	POExternalIDTypeKey   = "1400"
	POExternalIDTypeValue = "Purchase order #"

	LineHaulCodeKey   = "160218"
	LineHaulCodeValue = "Freight - Linehaul"

	FuelFlatCodeKey   = "1609"
	FuelFlatCodeValue = "Fuel - flat"

	USDCurrencyKey   = "1550"
	USDCurrencyValue = "USD"

	DraftStatusKey   = "2120"
	DraftStatusValue = "Draft"

	TenderedStatusKey   = "2101"
	TenderedStatusValue = "Tendered"

	PickupStopTypeKey   = "1500"
	PickupStopTypeValue = "Pickup"

	DeliveryStopTypeKey   = "1501"
	DeliveryStopTypeValue = "Delivery"

	FreightFlatCodeKey   = "1600"
	FreightFlatCodeValue = "Freight - flat"

	OpenState = "OPEN"
)

//
// ---------------------------------------------------------------------------------------------------------
// Error / Auth related structs
// ---------------------------------------------------------------------------------------------------------
//

type (
	// Error related structs
	ErrorResponse struct {
		ErrorMessage string `json:"errorMessage,omitempty"`
		ErrorCode    string `json:"errorCode,omitempty"`
	}

	// Auth related structs
	TokenRequestBody struct {
		GrantType string `json:"grant_type"`
		Username  string `json:"username"`
		Password  string `json:"password"`
		Scope     string `json:"scope"`
		Type      string `json:"type"`
	}

	TokenResponse struct {
		AccessToken  string `json:"access_token"`
		TokenType    string `json:"token_type"`
		ExpiresIn    int    `json:"expires_in"`
		Scope        string `json:"scope"`
		BusID        string `json:"busId"`
		Country      string `json:"country"`
		Distance     string `json:"distance"`
		Offset       int    `json:"offset"`
		Timezone     string `json:"timezone"`
		BusName      string `json:"busName"`
		Language     string `json:"language"`
		Type         string `json:"type"`
		UserID       int    `json:"userId"`
		Version      string `json:"version"`
		TenantRef    string `json:"tenant_ref"`
		UserOffset   int    `json:"userOffset"`
		Currency     string `json:"currency"`
		UserTimezone string `json:"userTimezone"`
		Email        string `json:"email"`
	}

	TokenData struct {
		AccessToken               string    `json:"access_token"`
		AccessTokenExpirationDate time.Time `json:"access_token_expiration_date"`
		TenantRef                 string    `json:"tenant_ref"`
	}
)

//
// ---------------------------------------------------------------------------------------------------------
// Common / Utility structs
// ---------------------------------------------------------------------------------------------------------
//

type (
	// Common fields/structs used in multiple domains
	DateTimeField struct {
		Date     time.Time `json:"date"`
		TimeZone string    `json:"timeZone"`
	}

	Code struct {
		Value string `json:"value"`
		Key   any    `json:"key"`
	}

	CreateLoadStatus struct {
		Code  KeyValuePair `json:"code"`
		Notes string       `json:"notes"`
	}

	Status struct {
		Code        Code   `json:"code"`
		Notes       string `json:"notes"`
		Description string `json:"description"`
		Category    string `json:"category"`
	}

	ContributorUser struct {
		ID int `json:"id"`
	}

	StopType struct {
		Value string `json:"value"`
	}

	ToFrom struct {
		Date     time.Time `json:"date"`
		Timezone string    `json:"timezone"`
		Flex     int       `json:"flex"`
		HasTime  bool      `json:"hasTime"`
	}

	AppointmentRef struct {
		From ToFrom `json:"from"`
		To   ToFrom `json:"to"`
	}

	// https://help.turvo.com/hc/en-us/articles/360054048014-Understanding-dates-and-times-on-a-route
	PlannedAppointmentDate struct {
		SchedulingType KeyValuePair   `json:"schedulingType"`
		Appointment    AppointmentRef `json:"appointment"`
	}

	Transportation struct {
		Mode        KeyValuePair `json:"mode"`
		ServiceType KeyValuePair `json:"serviceType"`
	}

	KeyValuePair struct {
		Key   string `json:"key"`
		Value string `json:"value"`
	}
)

func (dtf *DateTimeField) UnmarshalJSON(data []byte) error {

	type Alias DateTimeField
	aux := &struct {
		Date string `json:"date"`
		*Alias
	}{
		Alias: (*Alias)(dtf),
	}

	if err := json.Unmarshal(data, &aux); err != nil {
		return err
	}

	if aux.Date != "" {
		parsedTime, err := time.Parse(time.RFC3339, aux.Date)
		if err != nil {
			return fmt.Errorf("invalid date format: %w", err)
		}
		dtf.Date = parsedTime
	} else {
		dtf.Date = time.Time{}
	}

	return nil
}

//
// ---------------------------------------------------------------------------------------------------------
// Contributors / Groups / Equipment
// ---------------------------------------------------------------------------------------------------------
//

type (
	Groups struct {
		ID   int    `json:"id"`
		Name string `json:"name"`
	}

	Contributor struct {
		Deleted bool            `json:"deleted"`
		ID      int             `json:"id"`
		Title   KeyValuePair    `json:"title"`
		User    ContributorUser `json:"contributorUser"`
	}

	Equipment struct {
		Type    KeyValuePair `json:"type"`
		Size    KeyValuePair `json:"size"`
		Deleted bool         `json:"deleted"`

		Weight      float32      `json:"weight"`
		WeightUnits KeyValuePair `json:"weightUnits"`
		Temp        float32      `json:"temp"`
		TempUnits   KeyValuePair `json:"tempUnits"`
	}
)

//
// ---------------------------------------------------------------------------------------------------------
// Lane / Route / GlobalRoute related structs
// ---------------------------------------------------------------------------------------------------------
//

type (
	Lane struct {
		Start string `json:"start"`
		End   string `json:"end"`
	}

	Location struct {
		ID int `json:"id"`
	}

	ShipmentAppointment struct {
		Date     time.Time `json:"date"`
		TimeZone string    `json:"timeZone"`
		Flex     int       `json:"flex"`
		HasTime  bool      `json:"hasTime"`
	}

	Appointment struct {
		Start    time.Time `json:"start"`
		Date     time.Time `json:"date"`
		TimeZone string    `json:"timeZone"`
		Flex     int       `json:"flex"`
		HasTime  bool      `json:"hasTime"`
	}

	ShipmentGlobalRoute struct {
		Name                       string              `json:"name"`
		StopType                   KeyValuePair        `json:"stopType"`
		Location                   Location            `json:"location"`
		Sequence                   int                 `json:"sequence"`
		SegmentSequence            int                 `json:"segmentSequence"`
		GlobalShipLocationSourceID any                 `json:"globalShipLocationSourceId"`
		State                      string              `json:"state"`
		SchedulingType             KeyValuePair        `json:"schedulingType"`
		Timezone                   string              `json:"timezone"`
		Appointment                ShipmentAppointment `json:"appointment"`
		CustomerOrder              []CustomerOrderRef  `json:"customerOrder"`
		Transportation             Transportation      `json:"transportation"`
		PoNumbers                  []string            `json:"poNumbers"`
	}

	GlobalRoute struct {
		ID                         int                    `json:"id"`
		Operation                  int                    `json:"_operation"`
		GlobalShipLocationSourceID any                    `json:"globalShipLocationSourceId"`
		Name                       string                 `json:"name"`
		SchedulingType             KeyValuePair           `json:"schedulingType"`
		StopType                   StopType               `json:"stopType"`
		Timezone                   string                 `json:"timezone"`
		Location                   Location               `json:"location"`
		SegmentSequence            float64                `json:"segmentSequence"`
		Sequence                   float64                `json:"sequence"`
		State                      string                 `json:"state"`
		Appointment                Appointment            `json:"appointment"`
		AppointmentConfirmation    bool                   `json:"appointmentConfirmation"`
		PlannedAppointmentDate     PlannedAppointmentDate `json:"plannedAppointmentDate"`
		Services                   []KeyValuePair         `json:"services"`
		PoNumbers                  []string               `json:"poNumbers"`
		Notes                      string                 `json:"notes"`
		CustomerOrder              []CustomerOrderRef     `json:"customerOrder"`
		CarrierOrder               []CarrierOrderRef      `json:"carrierOrder"`
		Transportation             Transportation         `json:"transportation"`
		RecalculateDistance        bool                   `json:"recalculateDistance"`
		Attributes                 GlobalRouteAttributes  `json:"attributes"`
	}

	GlobalRouteAttributes struct {
		Arrival  DateTimeField `json:"arrival"`
		Departed DateTimeField `json:"departed"`
	}

	TotalSegmentValue struct {
		Sync     bool         `json:"sync"`
		Value    json.Number  `json:"value"` // always set to 0 if sync is true
		Currency KeyValuePair `json:"currency"`
	}

	ShipmentModeInfo struct {
		Operation             int          `json:"_operation"`
		SourceSegmentSequence string       `json:"sourceSegmentSequence"`
		Mode                  KeyValuePair `json:"mode"`
		ServiceType           KeyValuePair `json:"serviceType"`
	}
	ModeInfo struct {
		Operation             int          `json:"_operation"`
		SourceSegmentSequence string       `json:"sourceSegmentSequence"`
		Mode                  KeyValuePair `json:"mode"`
		ServiceType           KeyValuePair `json:"serviceType"`

		TotalSegmentValue TotalSegmentValue `json:"totalSegmentValue"`
	}
)

//
// ---------------------------------------------------------------------------------------------------------
// Customer / Orders / Items
// ---------------------------------------------------------------------------------------------------------
//

type (
	Customer struct {
		ID   int    `json:"id"`
		Name string `json:"name"`
	}

	CustomerIDOnly struct {
		ID int `json:"id"`
	}

	Dimensions struct {
		Length float32      `json:"length"`
		Width  float32      `json:"width"`
		Height float32      `json:"height"`
		Units  KeyValuePair `json:"units"`
	}

	PickupLocation struct {
		GlobalShipLocationSourceID any    `json:"globalShipLocationSourceId"`
		Name                       string `json:"name"`
	}

	LocationRef struct {
		GlobalShipLocationSourceID any    `json:"globalShipLocationSourceId"`
		Name                       string `json:"name"`
	}

	Temp struct {
		Temp     float32      `json:"temp"`
		TempUnit KeyValuePair `json:"tempUnit"`
	}

	Weight struct {
		Weight     float32      `json:"weight"`
		WeightUnit KeyValuePair `json:"weight_unit"`
	}

	ShipmentItems struct {
		Name             string        `json:"name"`
		Qty              float32       `json:"qty"`
		PickupLocation   []LocationRef `json:"pickupLocation"`
		DeliveryLocation []LocationRef `json:"deliveryLocation"`
		GrossWeight      Weight        `json:"grossWeight"`
		NetWeight        Weight        `json:"netWeight"`
		Unit             KeyValuePair  `json:"unit"`
	}

	Items struct {
		Name             string        `json:"name"`
		Qty              float32       `json:"qty"`
		PickupLocation   []LocationRef `json:"pickupLocation"`
		DeliveryLocation []LocationRef `json:"deliveryLocation"`
		Unit             KeyValuePair  `json:"unit"`

		Dimensions   Dimensions   `json:"dimensions"`
		ItemCategory KeyValuePair `json:"itemCategory"`
		HandlingQty  float32      `json:"handlingQty"`
		HandlingUnit KeyValuePair `json:"handlingUnit"`
		Notes        string       `json:"notes"`
		ItemNumber   string       `json:"itemNumber"`
		Nmfc         string       `json:"nmfc"`
		NmfcSub      string       `json:"nmfcSub"`
		IsHazmat     bool         `json:"isHazmat"`
		FreightClass KeyValuePair `json:"freightClass"`
		Value        float32      `json:"value"`
		MinTemp      Temp         `json:"minTemp"`
		MaxTemp      Temp         `json:"maxTemp"`
	}

	CustomerOrderRef struct {
		ID                    int    `json:"id"`
		CustomerID            int    `json:"customerId"`
		CustomerOrderSourceID uint32 `json:"customerOrderSourceId"`
	}

	CarrierOrderRef struct {
		ID                   int `json:"id"`
		CarrierID            int `json:"carrierId"`
		CarrierOrderSourceID int `json:"carrierOrderSourceId"`
	}

	LineItem struct {
		Code     Code    `json:"code"`
		Qty      float32 `json:"qty"`
		Price    float32 `json:"price"`
		Amount   float32 `json:"amount"`
		Billable bool    `json:"billable"`
		Notes    string  `json:"notes"`
	}

	Costs struct {
		TotalAmount float32    `json:"totalAmount"`
		LineItem    []LineItem `json:"lineItem"`
	}

	ExternalIDs struct {
		Type               KeyValuePair `json:"type"`
		Value              string       `json:"value"`
		CopyToCarrierOrder bool         `json:"copyToCarrierOrder"`
	}

	RouteStop struct {
		ID                  int         `json:"id"`
		Deleted             bool        `json:"deleted"`
		StopType            StopType    `json:"stopType"`
		Location            Location    `json:"location"`
		Address             Address     `json:"address"`
		Sequence            int         `json:"sequence"`
		State               string      `json:"state"`
		Appointment         Appointment `json:"appointment"`
		AppointmentNo       string      `json:"appointmentNo"`
		OriginalAppointment Appointment `json:"originalAppointment"`
	}

	// Used for creating a quote in Turvo
	CustomerOrder struct {
		ID                    int           `json:"id"`
		CustomerOrderSourceID int           `json:"customerOrderSourceId"`
		Customer              Customer      `json:"customer"`
		Items                 []Items       `json:"items"`
		Costs                 Costs         `json:"costs"`
		ExternalIDs           []ExternalIDs `json:"externalIds"`
		Route                 []RouteStop   `json:"route"`
	}

	// Used in the response for fetching a shipment. Ignores unused Items field
	FetchedCustomerOrder struct {
		ID                    int           `json:"id"`
		CustomerOrderSourceID int           `json:"customerOrderSourceId"`
		Customer              Customer      `json:"customer"`
		Costs                 Costs         `json:"costs"`
		ExternalIDs           []ExternalIDs `json:"externalIds"`
		Route                 []RouteStop   `json:"route"`
	}

	ShipmentLineItem struct {
		Code     KeyValuePair `json:"code"`
		Qty      float32      `json:"qty"`
		Price    float32      `json:"price"`
		Amount   float32      `json:"amount"`
		Billable bool         `json:"billable"`
		Notes    string       `json:"notes"`
	}

	ShipmentCosts struct {
		TotalAmount         float32            `json:"totalAmount"`
		TransactionCurrency KeyValuePair       `json:"transactionCurrency"`
		LineItem            []ShipmentLineItem `json:"lineItem"`
	}

	ShipmentCustomerOrder struct {
		CustomerOrderSourceID uint32          `json:"customerOrderSourceId"`
		Customer              Customer        `json:"customer"`
		Items                 []ShipmentItems `json:"items"`
		Costs                 ShipmentCosts   `json:"costs"`
		ExternalIDs           []ExternalIDs   `json:"externalIds"`
	}
)

//
// ---------------------------------------------------------------------------------------------------------
// Carrier / Carrier Orders
// ---------------------------------------------------------------------------------------------------------
//

type (
	Carrier struct {
		Name string `json:"name"`
		ID   int    `json:"id"`
	}

	Drivers struct {
		DriverID        int    `json:"driverId"`
		Name            string `json:"name"`
		SegmentSequence int    `json:"segmentSequence"`
		Phone           Phone  `json:"phone"`
	}

	CarrierOrder struct {
		ID                   int       `json:"id"`
		Operation            int       `json:"_operation"`
		CarrierOrderSourceID int       `json:"carrierOrderSourceId"`
		Carrier              Carrier   `json:"carrier"`
		Drivers              []Drivers `json:"drivers"`
	}
)

//
// ---------------------------------------------------------------------------------------------------------
// Load / FilterLoads / CustomerList responses
// ---------------------------------------------------------------------------------------------------------
//

type (
	GroupsWithOperation struct {
		ID        int    `json:"id"`
		Name      string `json:"name"`
		Operation int    `json:"_operation"`
	}

	// Top-level load creation/updating
	CreateLoadRequest struct {
		LTLShipment         bool                    `json:"ltlShipment"`
		StartDate           DateTimeField           `json:"startDate"`
		EndDate             DateTimeField           `json:"endDate"`
		Status              CreateLoadStatus        `json:"status"`
		Lane                Lane                    `json:"lane"`
		Equipment           []ShipmentEquipment     `json:"equipment"`
		ShipmentGlobalRoute []ShipmentGlobalRoute   `json:"globalRoute"`
		CustomerOrder       []ShipmentCustomerOrder `json:"customerOrder"`
		ModeInfo            []ShipmentModeInfo      `json:"modeInfo"`
		Groups              []GroupsWithOperation   `json:"groups,omitempty"`
	}

	// Used for most Turvo API shipment responses, not used for creating a shipment
	LoadResponse struct {
		Status  string      `json:"Status"` // Request status; can be "Success" or "Error"
		Details LoadDetails `json:"details"`
	}

	AppTurvoLocationResponse struct {
		Details AppTurvoLocationDetails `json:"details"`
		Notes   AppTurvoLocationNotes   `json:"notes"`
	}

	AppTurvoLocationDetails struct {
		Basic AppTurvoLocationBasic `json:"Basic"`
	}

	AppTurvoLocationBasic struct {
		Name                string                    `json:"name"`
		LocationID          int                       `json:"locationId"`
		SpecialInstructions string                    `json:"special_instructions"`
		Timezone            string                    `json:"timezone"`
		Addresses           []AppTurvoLocationAddress `json:"addresses"`
		Emails              []AppTurvoLocationEmail   `json:"emails"`
		Phones              []AppTurvoLocationPhone   `json:"phones"`
	}

	AppTurvoLocationAddress struct {
		Primary bool                    `json:"primary"`
		Line1   string                  `json:"line1"`
		Line2   string                  `json:"line2"`
		City    AppTurvoLocationCity    `json:"city"`
		State   AppTurvoLocationState   `json:"state"`
		Zip     string                  `json:"zip"`
		Country AppTurvoLocationCountry `json:"country"`
	}

	AppTurvoLocationCity struct {
		Name string `json:"name"`
	}

	AppTurvoLocationState struct {
		Name string `json:"name"`
	}

	AppTurvoLocationCountry struct {
		Name string `json:"name"`
		Code string `json:"code"`
	}

	AppTurvoLocationEmail struct {
		Email   string `json:"email"`
		Primary bool   `json:"primary"`
	}

	AppTurvoLocationPhone struct {
		Number  string `json:"number"`
		Primary bool   `json:"primary"`
	}

	AppTurvoLocationNotes struct {
		Data []AppTurvoLocationNote `json:"data"`
	}

	AppTurvoLocationNote struct {
		Body string `json:"body"`
	}

	FilterLoadsResponse struct {
		Status  string             `json:"Status"` // Request status; can be "Success" or "Error"
		Details FilterLoadsDetails `json:"details"`
	}

	CustomerListResponse struct {
		Status  string              `json:"Status"` // Request status; can be "Success" or "Error"
		Details CustomerListDetails `json:"details"`
	}

	FilterLoadsDetails struct {
		ErrorResponse
		Loads []CompactLoadDetails `json:"shipments"`
	}

	PaginationDetails struct {
		Start         int  `json:"start"`
		PageSize      int  `json:"pageSize"`
		TotalRecords  int  `json:"totalRecordsInPage"`
		MoreAvailable bool `json:"moreAvailable"`
	}

	CustomerListDetails struct {
		ErrorResponse
		Pagination PaginationDetails       `json:"pagination"`
		Customers  []ListedCustomerDetails `json:"customers"`
	}

	ListedCustomerDetails struct {
		ID      int       `json:"id"`
		Name    string    `json:"name"`
		Created time.Time `json:"created"`
		Updated time.Time `json:"updated"`
		// Turvo API Note: the docs have this json key listed as
		// 'addresses' but the actual API response uses the key 'address'
		Address       []Address            `json:"address"`
		Addresses     []Address            `json:"addresses"` // just in case turvo changes the API again
		ParentAccount ListedParentAccount  `json:"parentAccount"`
		Status        ListedCustomerStatus `json:"status"`
		Owner         Owner                `json:"owner"`
	}

	ListedParentAccount struct {
		ID   int    `json:"id"`
		Name string `json:"name"`
		Type string `json:"type"`
	}

	ListedCustomerStatus struct {
		Code        int    `json:"code"`
		Description string `json:"description"`
	}
)

//
// ---------------------------------------------------------------------------------------------------------
// Flex / Misc. attributes used in Loads
// ---------------------------------------------------------------------------------------------------------
//

type (
	FlexAttribute struct {
		Type      KeyValuePair `json:"type"`
		Value     string       `json:"value"`
		Name      string       `json:"name"`
		Account   string       `json:"account"`
		Shareable bool         `json:"shareable"`
	}
)

//
// ---------------------------------------------------------------------------------------------------------
// Load Details
// ---------------------------------------------------------------------------------------------------------
//

type (
	LoadDetails struct {
		ErrorResponse

		ID              int                    `json:"id"`
		CustomID        string                 `json:"customId"`
		LtlShipment     bool                   `json:"ltlShipment"`
		Phase           KeyValuePair           `json:"phase"`
		Services        []KeyValuePair         `json:"services"`
		StartDate       DateTimeField          `json:"startDate"`
		EndDate         DateTimeField          `json:"endDate"`
		Transportation  Transportation         `json:"transportation"`
		Status          Status                 `json:"status"`
		Tracking        Tracking               `json:"tracking"`
		Margin          Margin                 `json:"margin"`
		Equipment       []Equipment            `json:"equipment"`
		Contributors    []Contributor          `json:"contributors"`
		Lane            Lane                   `json:"lane"`
		GlobalRoute     []GlobalRoute          `json:"globalRoute"`
		ModeInfo        []ModeInfo             `json:"modeInfo"`
		CustomerOrder   []FetchedCustomerOrder `json:"customerOrder"`
		CarrierOrder    []CarrierOrder         `json:"carrierOrder"`
		UseRoutingGuide bool                   `json:"use_routing_guide"`
	}

	CompactLoadDetails struct {
		ID       int    `json:"id"`
		CustomID string `json:"customId"`
	}

	Tracking struct {
		IsTrackable bool       `json:"isTrackable"`
		Deleted     bool       `json:"deleted"`
		IsTracking  bool       `json:"isTracking"`
		Description string     `json:"description"`
		Source      string     `json:"source"`
		Frequency   float32    `json:"frequency"`
		RouteSteps  RouteSteps `json:"routeSteps"`
	}

	RouteSteps struct {
		VisitedGeoWayPoints string `json:"visitedGeoWayPoints"`
		CountGeoWayPoints   int    `json:"countGeoWayPoints"`
		StepsPolyline       string `json:"stepsPolyline"`
	}

	Margin struct {
		MinPay float32 `json:"minPay"`
		MaxPay float32 `json:"maxPay"`
	}
)

//
// ---------------------------------------------------------------------------------------------------------
// Customer-related top-level responses
// ---------------------------------------------------------------------------------------------------------
//

type (
	CustomerResponse struct {
		Status  string          `json:"Status"`
		Details CustomerDetails `json:"details"`
	}

	Group struct {
		ID        int    `json:"id"`
		Name      string `json:"name"`
		Operation int    `json:"_operation"`
	}

	Billing struct {
		ID                    string   `json:"id"`
		ToName                *string  `json:"toName"` // can be null
		CreditLimit           float32  `json:"creditLimit"`
		ThirdParty            bool     `json:"thirdParty"`
		Careof                *string  `json:"careof"` // can be null
		PayTerms              PayTerms `json:"payTerms"`
		Address               Address  `json:"address"`
		Phone                 Phone    `json:"phone"`
		Instructions          *string  `json:"instructions"` // can be null
		CustomerPaysUnloading bool     `json:"customerPaysUnloading"`
		Contact               Contact  `json:"contact"`
		Deleted               bool     `json:"deleted"`
	}

	Contact struct {
		ID   int    `json:"id"`
		Name string `json:"name"`
	}

	CustomerDetails struct {
		ErrorResponse

		ID                     int                   `json:"id"`
		Name                   string                `json:"name"`
		TaxID                  string                `json:"taxId"`
		Status                 CustomerStatus        `json:"status"`
		PaysUnloading          bool                  `json:"paysUnloading"`
		AutoInvoice            bool                  `json:"autoInvoice"`
		InvoiceWithoutDocument bool                  `json:"invoiceWithoutDocument"`
		SpecialInstructions    string                `json:"specialInstructions"`
		Notes                  string                `json:"notes"`
		ParentAccount          ParentAccount         `json:"parentAccount"`
		Commission             []Commission          `json:"commission"`
		Groups                 []Group               `json:"groups"`
		Billings               []Billing             `json:"billings"`
		Contact                Contact               `json:"contact"`
		Owner                  Owner                 `json:"owner"`
		Address                []Address             `json:"address"`
		Email                  []Email               `json:"email"`
		Phone                  []Phone               `json:"phone"`
		AccountDistribution    []AccountDistribution `json:"accountDistribution"`
		AllowedCurrencies      []AllowedCurrency     `json:"allowedCurrencies"`
	}

	CustomerStatus struct {
		Notes       string `json:"notes"`
		Description string `json:"description"`
		Code        CSCode `json:"code"`
	}

	CSCode struct {
		ID    int    `json:"id"`
		Key   string `json:"key"`
		Value string `json:"value"`
	}

	ParentAccount struct {
		ID   int    `json:"id"`
		Name string `json:"name"`
	}

	Commission struct {
		ID         int     `json:"id"`
		User       User    `json:"user"`
		Commission float32 `json:"commission"`
		Deleted    bool    `json:"deleted"`
	}

	PayTerms struct {
		Key       string  `json:"key"`
		DaysToPay float32 `json:"daysToPay"`
		Name      string  `json:"name"`
		Category  string  `json:"category"`
	}

	Owner struct {
		ID           int    `json:"id"`
		Name         string `json:"name"`
		IsOfferOwner bool   `json:"isOfferOwner"`
	}

	Email struct {
		ID        string       `json:"id"`
		Email     string       `json:"email"`
		IsPrimary bool         `json:"isPrimary"`
		Type      KeyValuePair `json:"type"`
		Deleted   bool         `json:"deleted"`
	}

	AccountDistribution struct {
		ID               any          `json:"id"`
		AccountingSystem KeyValuePair `json:"accountingSystem"`
		ExternalIDs      []ExternalID `json:"externalIds"`
		Deleted          bool         `json:"deleted"`
	}

	AllowedCurrency struct {
		ID              int    `json:"id"`
		Key             int    `json:"key"`
		Value           string `json:"value"`
		DisplayName     string `json:"displayName"`
		DefaultCurrency bool   `json:"defaultCurrency"`
	}
)

//
// ---------------------------------------------------------------------------------------------------------
// LocationList-related top-level responses
// ---------------------------------------------------------------------------------------------------------
//

type (
	// Root response object
	LocationListResponse struct {
		Status  string               `json:"Status"`
		Details LocationsListDetails `json:"details"`
	}

	// Holds the pagination info and the list of locations
	LocationsListDetails struct {
		ErrorResponse
		Pagination Pagination         `json:"pagination"`
		Locations  []LocationOverview `json:"locations"`
	}

	// Pagination details
	Pagination struct {
		Start              int  `json:"start"`
		PageSize           int  `json:"pageSize"`
		TotalRecordsInPage int  `json:"totalRecordsInPage"`
		MoreAvailable      bool `json:"moreAvailable"`
	}

	// A single location entry
	LocationOverview struct {
		ID      int       `json:"id"`
		Name    string    `json:"name"`
		Created time.Time `json:"created"`
		Updated time.Time `json:"updated"`
		// Turvo API Note: the docs have this json key listed as
		// 'addresses' but the actual API response uses the key 'address'
		Address   []PartialAddress `json:"address"`
		Addresses []PartialAddress `json:"addresses"` // just in case turvo changes the API again

		Phones []FullPhone `json:"phones"`
	}

	// Physical address details
	PartialAddress struct {
		Line1 string `json:"line1"`
		Line2 string `json:"line2"`
		City  string `json:"city"`
		State string `json:"state"`
		Zip   string `json:"zip"`
	}

	// Phone details
	FullPhone struct {
		CountryCode string `json:"countryCode"`
		Number      string `json:"number"`
		Extension   string `json:"extension"`
	}
)

//
// ---------------------------------------------------------------------------------------------------------
// app.turvo LocationList-related top-level responses
// ---------------------------------------------------------------------------------------------------------
//

type (
	// AppTurvoLocationListResponse represents the response from app.turvo.com API
	AppTurvoLocationListResponse struct {
		Status         string                   `json:"status"`
		Message        string                   `json:"message"`
		CurrentPage    int                      `json:"currentPage"`
		TotalPages     int                      `json:"totalPages"`
		TotalLocations int                      `json:"totalLocations"`
		Locations      []AppTurvoLocationDetail `json:"locations"`
		Pagination     Pagination               `json:"pagination"`
	}

	// AppTurvoLocationDetail represents location structure from app.turvo.com API
	AppTurvoLocationDetail struct {
		ProjectFields ProjectFields `json:"projectFields"`
		Basic         BasicFields   `json:"Basic"`
		ID            int           `json:"id"`
	}

	ProjectFields struct {
		Title string `json:"title"`
		ID    int    `json:"id"`
	}

	BasicFields struct {
		Addresses  []LocationDetailAddress `json:"addresses"`
		Phones     []LocationDetailPhone   `json:"phones"`
		Tags       []LocationDetailTag     `json:"tags"`
		Emails     []LocationDetailEmail   `json:"emails"`
		LocationID int                     `json:"locationId"`
		Name       string                  `json:"name"`
		Account    LocationDetailAccount   `json:"account"`
	}

	LocationDetailAddress struct {
		Primary bool         `json:"primary"`
		GPS     LocationGPS  `json:"gps"`
		Line1   string       `json:"line1"`
		Line2   string       `json:"line2"`
		City    NameField    `json:"city"`
		State   NameField    `json:"state"`
		Zip     string       `json:"zip"`
		Country CountryField `json:"country"`
	}

	LocationGPS struct {
		Coordinates []float64 `json:"coordinates"` // [longitude, latitude]
	}

	LocationDetailPhone struct {
		Primary   bool   `json:"primary"`
		Extension string `json:"extension"`
		Number    string `json:"number"`
	}

	LocationDetailTag struct {
		TagName string `json:"tagName"`
	}

	LocationDetailEmail struct {
		Email   string `json:"email"`
		Primary bool   `json:"primary"`
		Active  bool   `json:"active"`
		Deleted bool   `json:"deleted"`
	}

	LocationDetailAccount struct {
		ID   int    `json:"id"`
		Name string `json:"name"`
		Type string `json:"type"`
	}

	NameField struct {
		Name string `json:"name"`
	}

	CountryField struct {
		Name string `json:"name"`
		Code string `json:"code"`
	}
)

// ---------------------------------------------------------------------------------------------------------
// Location-related top-level responses
// ---------------------------------------------------------------------------------------------------------
//

type (
	LocationResponse struct {
		Status  string          `json:"Status"`
		Details LocationDetails `json:"details"`
	}

	LocationDetails struct {
		ID                  int                  `json:"id"`
		Name                string               `json:"name"`
		Timezone            string               `json:"timezone"`
		Account             Account              `json:"account"`
		Geofence            Geofence             `json:"geofence"`
		DeliveryLink        DeliveryLink         `json:"deliveryLink"`
		ExternalIDs         []LocExternalID      `json:"externalIds"`
		HoursOfOperation    Hours                `json:"hoursOfOperation"`
		ShippingHours       Hours                `json:"shippingHours"`
		ReceivingHours      Hours                `json:"receivingHours"`
		CrossDock           bool                 `json:"crossDock"`
		ColdStorage         bool                 `json:"coldStorage"`
		Warehouse           bool                 `json:"warehouse"`
		UseApptAsDwellTime  bool                 `json:"useApptAsDwellTime"`
		Locode              string               `json:"locode"`
		DwellTime           DwellTime            `json:"dwellTime"`
		SpecialInstructions string               `json:"specialInstructions"`
		Directions          string               `json:"directions"`
		SchedulingType      KeyValuePair         `json:"schedulingType"`
		SchedulingInfo      SchedulingInfo       `json:"schedulingInfo"`
		Address             []Address            `json:"address"`
		Email               []LocationEmail      `json:"email"`
		Phone               []Phone              `json:"phone"`
		LTLShipmentService  []LTLShipmentService `json:"ltlShipmentService"`
		Contact             Contact              `json:"contact"`
	}

	Account struct {
		ID int `json:"id"`
	}

	Geofence struct {
		Type    string   `json:"type"`
		Polygon []string `json:"polygon"`
		Radius  struct {
			Units KeyValuePair `json:"units"`
		} `json:"radius"`
	}

	DeliveryLink struct {
		Status   DeliveryLinkStatus `json:"status"`
		LateType DeliveryLinkLate   `json:"lateType"`
		Time     DeliveryLinkTime   `json:"time"`
	}

	DeliveryLinkStatus struct {
		Enabled bool         `json:"enabled"`
		Code    KeyValuePair `json:"code"`
	}

	DeliveryLinkLate struct {
		Enabled bool         `json:"enabled"`
		Type    KeyValuePair `json:"type"`
	}

	DeliveryLinkTime struct {
		Enabled  bool     `json:"enabled"`
		Duration Duration `json:"duration"`
	}

	Duration struct {
		Value int          `json:"value"`
		Units KeyValuePair `json:"units"`
	}

	LocExternalID struct {
		Deleted bool         `json:"deleted"`
		Type    KeyValuePair `json:"type"`
		ID      int          `json:"id"`
		Value   string       `json:"value"`
		Account Account      `json:"account"`
	}

	Hours struct {
		Start string   `json:"start"`
		End   string   `json:"end"`
		Days  []string `json:"days"`
	}

	DwellTime struct {
		Value int          `json:"value"`
		Units KeyValuePair `json:"units"`
	}

	SchedulingInfo struct {
		AppointmentCutoffTimeConfig AppointmentCutoffTimeConfig `json:"appointmentCutoffTimeConfig"`
		AppointmentDateConfig       AppointmentDateConfig       `json:"appointmentDateConfig"`
		Enabled                     bool                        `json:"enabled"`
		Warehouse                   bool                        `json:"warehouse"`
	}

	AppointmentCutoffTimeConfig struct {
		IsCutoffTimeForDeleteOn               bool            `json:"isCutoffTimeForDeleteOn"`
		UpdateAppointmentInfoCutoffTimeConfig map[string]bool `json:"updateAppointmentInfoCutoffTimeConfig"`
	}

	AppointmentDateConfig struct {
		CutoffTime CutoffTime `json:"cutoffTime"`
		Range      struct {
			Value int `json:"value"`
		} `json:"range"`
	}

	CutoffTime struct {
		ScheduleDaysCount int    `json:"scheduleDaysCount"`
		InboundTime       string `json:"inboundTime"`
		OutboundTime      string `json:"outboundTime"`
		IsCutoffTimeOn    bool   `json:"isCutoffTimeOn"`
		SkipDayCount      int    `json:"skipDayCount"`
	}

	Address struct {
		ID        string       `json:"id"`
		Line1     string       `json:"line1"`
		Line2     string       `json:"line2"`
		City      string       `json:"city"`
		State     string       `json:"state"`
		Zip       string       `json:"zip"`
		Type      KeyValuePair `json:"type"`
		IsPrimary bool         `json:"isPrimary"`
		Country   string       `json:"country"`
		Lon       float64      `json:"lon"`
		Lat       float64      `json:"lat"`
		Deleted   bool         `json:"deleted"`
	}

	LocationEmail struct {
		ID                string       `json:"id"`
		Email             string       `json:"email"`
		ReceiveTurvoLinks bool         `json:"receiveTurvoLinks"`
		IsPrimary         bool         `json:"isPrimary"`
		Type              KeyValuePair `json:"type"`
		Deleted           bool         `json:"deleted"`
	}

	Phone struct {
		ID                string       `json:"id"`
		Number            string       `json:"number"`
		Extension         string       `json:"extension"`
		IsPrimary         bool         `json:"isPrimary"`
		ReceiveTurvoLinks bool         `json:"receiveTurvoLinks"`
		Type              KeyValuePair `json:"type"`
		Country           KeyValuePair `json:"country"`
		Deleted           bool         `json:"deleted"`
	}

	LTLShipmentService struct {
		ID    int    `json:"id"`
		Value string `json:"value"`
	}
)

//
// ---------------------------------------------------------------------------------------------------------
// Carrier-related top-level responses
// ---------------------------------------------------------------------------------------------------------
//

type (
	CarrierResponse struct {
		Status  string         `json:"Status"` // Request status; can be "Success" or "Error"
		Details CarrierDetails `json:"details"`
	}
)

//
// ---------------------------------------------------------------------------------------------------------
// Carrier support data (SCAC, Insurance, Payment, etc.)
// ---------------------------------------------------------------------------------------------------------
//

type (
	Scac struct {
		ID            string    `json:"id"`
		SCAC          string    `json:"scac"`
		Deleted       bool      `json:"deleted"`
		Active        bool      `json:"active"`
		CreatedOn     time.Time `json:"createdOn"`
		LastUpdatedOn time.Time `json:"lastUpdatedOn"`
	}

	Claims struct {
		Email   ContactDetail `json:"email"`
		Phone   ContactDetail `json:"phone"`
		Fax     ContactDetail `json:"fax"`
		Contact Contact       `json:"contact"`
	}

	ContactDetail struct {
		Primary   string  `json:"primary"`
		Number    string  `json:"number"`
		Extension int     `json:"extension"`
		Country   Country `json:"country"`
	}

	Insurance struct {
		ID             string       `json:"id"`
		Type           KeyValuePair `json:"type"`
		Amount         float32      `json:"amount"`
		ExpirationDate string       `json:"expirationDate"`
		Provider       string       `json:"provider"`
		Producer       string       `json:"producer"`
		Notes          string       `json:"notes"`
		PolicyNo       string       `json:"policyNo"`
		State          string       `json:"state"`
		RefCoverage    bool         `json:"refCoverage"`
		Deleted        bool         `json:"deleted"`
	}

	PaymentMethod struct {
		ID            string          `json:"id"`
		Type          KeyValuePair    `json:"type"`
		PayTo         string          `json:"payTo"`
		BankName      string          `json:"bankName"`
		RoutingNumber json.RawMessage `json:"routingNumber"`
		AccountNumber json.RawMessage `json:"accountNumber"`
		Factor        Factor          `json:"factor"`
		AccountType   KeyValuePair    `json:"accountType"`
		Address       Address         `json:"address"`
		Deleted       bool            `json:"deleted"`
	}

	Factor struct {
		ID   json.RawMessage `json:"id"`
		Name string          `json:"name"`
	}

	PaymentTerm struct {
		ID        string          `json:"id"`
		Type      KeyValuePair    `json:"type"`
		Default   bool            `json:"default"`
		Name      string          `json:"name"`
		Method    []PaymentMethod `json:"method"`
		DaysToPay float32         `json:"daysToPay"`
		Deleted   bool            `json:"deleted"`
		Active    bool            `json:"active"`
	}

	Authority struct {
		CommonAuthority   string `json:"commonAuthority"`
		ContractAuthority string `json:"contractAuthority"`
	}
)

//
// ---------------------------------------------------------------------------------------------------------
// Payment / Billing / Address / Contact
// ---------------------------------------------------------------------------------------------------------
//

type (
	User struct {
		ID   int    `json:"id"`
		Name string `json:"name"`
	}

	Country struct {
		Value string `json:"value"`
		Key   string `json:"key"`
	}

	ExternalID struct {
		ID      int          `json:"id"`
		Deleted bool         `json:"deleted"`
		Type    KeyValuePair `json:"type"`
		Value   string       `json:"value"`
	}
)

//
// ---------------------------------------------------------------------------------------------------------
// LoadUpdate related structs
// NOTE: This is a temporary solution until we can update all fields
// ---------------------------------------------------------------------------------------------------------
//

type (
	LoadUpdateRequest struct {
		StartDate   *LoadUpdateDateTimeField `json:"startDate,omitempty"`
		EndDate     *LoadUpdateDateTimeField `json:"endDate,omitempty"`
		GlobalRoute []LoadUpdateGlobalRoute  `json:"globalRoute,omitempty"`
	}

	LoadUpdateGlobalRoute struct {
		ID        int `json:"id"`
		Operation int `json:"_operation"` // 0=insert, 1=update, 2=remove

		Appointment            LoadUpdateAppointment            `json:"appointment"`
		PlannedAppointmentDate LoadUpdatePlannedAppointmentDate `json:"plannedAppointmentDate"`
		Attributes             StopAttributes                   `json:"attributes"`
		Notes                  string                           `json:"notes"`
		PONumbers              []string                         `json:"poNumbers"`
		State                  string                           `json:"state"` // OPEN or COMPLETED
		SchedulingType         KeyValuePair                     `json:"schedulingType"`
	}

	StopAttributes struct {
		Arrival  DateTimeField `json:"arrival"`
		Departed DateTimeField `json:"departed"`
	}

	LoadUpdateAppointment struct {
		Date     time.Time `json:"date"`
		TimeZone string    `json:"timezone"`
		HasTime  bool      `json:"hasTime"`
	}

	LoadUpdateAppointmentRef struct {
		From LoadUpdateAppointment `json:"from"`
	}

	LoadUpdatePlannedAppointmentDate struct {
		Appointment LoadUpdateAppointmentRef `json:"appointment"`
	}

	LoadUpdateDateTimeField struct {
		Date     time.Time `json:"date"`
		TimeZone string    `json:"timezone"`
	}
)

//
// ---------------------------------------------------------------------------------------------------------
// Check calls related structs
// ---------------------------------------------------------------------------------------------------------
//

type (
	UpdateCheckCallsRequest struct {
		ID     int `json:"id"`
		Status struct {
			GlobalShipLocationID int          `json:"globalShipLocationId,omitempty"`
			Code                 KeyValuePair `json:"code"`
			Timezone             string       `json:"timezone,omitempty"`
			Notes                string       `json:"notes,omitempty"`
			FragmentID           string       `json:"fragmentId,omitempty"`
			StatusDate           struct {
				LocalDateTime string    `json:"localDateTime,omitempty"`
				Date          time.Time `json:"date"`
				Timezone      string    `json:"timezone,omitempty"`
			} `json:"statusDate"`
			Location struct {
				City                 string  `json:"city"`
				Name                 string  `json:"name"`
				Lon                  float64 `json:"lon,omitempty"`
				Lat                  float64 `json:"lat,omitempty"`
				GlobalShipLocationID int     `json:"globalShipLocationId,omitempty"`
			} `json:"location"`
			PostType struct {
				Value string `json:"value"`
				Key   int    `json:"key"`
			} `json:"postType,omitempty"`
		} `json:"status"`
	}

	GetCheckCallResp struct {
		Status  string `json:"Status"` // Request status; can be "Success" or "Error"
		Details struct {
			ErrorResponse

			ID          int    `json:"id"`
			CustomID    string `json:"customId"`
			LtlShipment bool   `json:"ltlShipment"`

			Status struct {
				Code        KeyValuePair `json:"code"`
				Notes       string       `json:"notes"`
				Description string       `json:"description"`
				StatusDate  struct {
					Date     time.Time `json:"date"`
					Timezone string    `json:"timezone"`
				} `json:"statusDate"`
				DeliveredDate struct {
					LocalDateTime string    `json:"localDateTime"`
					Date          time.Time `json:"date"`
					Timezone      string    `json:"timezone"`
				} `json:"deliveredDate"`
				DispatchedDate struct {
					LocalDateTime string    `json:"localDateTime"`
					Date          time.Time `json:"date"`
					Timezone      string    `json:"timezone"`
				} `json:"dispatchedDate"`
				Location struct {
					City            string    `json:"city"`
					Lon             float64   `json:"lon"`
					Lat             float64   `json:"lat"`
					NextEta         string    `json:"nextEta"`
					NextEtaCalVal   string    `json:"nextEtaCalVal"`
					NextArrivalTime int       `json:"nextArrivalTime"`
					NextMiles       int       `json:"nextMiles"`
					CurrentDate     time.Time `json:"currentDate"`
				} `json:"location"`
				RunningLate struct {
					LateType             string `json:"lateType"`
					LateDuration         int    `json:"lateDuration"`
					NextLocationType     string `json:"nextLocationType"`
					LateDurationString   string `json:"lateDurationString"`
					NextLocationEta      string `json:"nextLocationEta"`
					NextLocationTimeZone string `json:"nextLocationTimeZone"`
					NextLocationName     string `json:"nextLocationName"`
					LateThresholds       struct {
						Approaching struct {
							IsEnabled bool `json:"isEnabled"`
							Value     int  `json:"value"`
						} `json:"approaching"`
						Running struct {
							IsEnabled bool `json:"isEnabled"`
							Value     int  `json:"value"`
						} `json:"running"`
					} `json:"lateThresholds"`
					NextLocationEtaDate         string `json:"nextLocationEtaDate"`
					IsApproachingLate           bool   `json:"isApproachingLate"`
					StatusKey                   string `json:"statusKey"`
					NextLocationAppointmentDate string `json:"nextLocationAppointmentDate"`
					NextLocationAppointment     string `json:"nextLocationAppointment"`
					LastUpdatedOn               string `json:"lastUpdatedOn"`
					NextLocationID              int    `json:"nextLocationId"`
				} `json:"runningLate"`
				LocationID       int    `json:"locationId"`
				Category         string `json:"category"`
				NextLocationType string `json:"nextLocationType"`
				NextLocationID   int    `json:"nextLocationId"`
				GlobalLocationID int    `json:"globalLocationId"`
			} `json:"status"`
		} `json:"details"`
	}
)

//
// ---------------------------------------------------------------------------------------------------------
// Quote related structs
// ---------------------------------------------------------------------------------------------------------
//

type (
	CreateQuoteRequest struct {
		LTLShipment bool `json:"ltlShipment"`
		StartDate   struct {
			Date     string `json:"date"`
			TimeZone string `json:"timeZone"`
		} `json:"startDate"`
		EndDate struct {
			Date     string `json:"date"`
			TimeZone string `json:"timeZone"`
		} `json:"endDate"`
		Status QuoteStatus `json:"status"`
		Lane   struct {
			Start string `json:"start"`
			End   string `json:"end"`
		} `json:"lane"`
		ModeInfo      []ModeInfo          `json:"modeInfo"`
		Equipment     []ShipmentEquipment `json:"equipment"`
		CustomerOrder []CustomerOrder     `json:"customerOrder"`
		Margin        Margin              `json:"margin"`
		Groups        []Group             `json:"groups,omitempty"`
	}

	CreateQuoteResponse struct {
		Status  string `json:"Status"`
		Details struct {
			ErrorResponse
			ID               int          `json:"id"`
			CustomID         string       `json:"customId"`
			LTLShipment      bool         `json:"ltlShipment"`
			Phase            KeyValuePair `json:"phase"`
			CustomAttributes struct {
				PublicAPI struct {
					SourceID string `json:"sourceId"`
				} `json:"public_api"`
			} `json:"custom_attributes"`
			StartDate struct {
				Date     string `json:"date"`
				TimeZone string `json:"timeZone"`
			} `json:"startDate"`
			EndDate struct {
				Date     string `json:"date"`
				TimeZone string `json:"timeZone"`
			} `json:"endDate"`
			Transportation struct {
				Mode        KeyValuePair `json:"mode"`
				ServiceType KeyValuePair `json:"serviceType"`
			} `json:"transportation"`
			Status   QuoteStatus `json:"status"`
			Tracking struct {
				IsTrackable bool   `json:"isTrackable"`
				Deleted     bool   `json:"deleted"`
				IsTracking  bool   `json:"isTracking"`
				Description string `json:"description"`
				Source      string `json:"source"`
				Frequency   int    `json:"frequency"`
			} `json:"tracking"`
			Equipment []struct {
				Deleted bool         `json:"deleted"`
				ID      int          `json:"id"`
				Type    KeyValuePair `json:"type"`
			} `json:"equipment"`
			Contributors []struct {
				Deleted         bool `json:"deleted"`
				ID              int  `json:"id"`
				ContributorUser struct {
					Name string `json:"name"`
					ID   int    `json:"id"`
				} `json:"contributorUser"`
				Title KeyValuePair `json:"title"`
			} `json:"contributors"`
		} `json:"details"`
	}

	ShipmentEquipment struct {
		Size      KeyValuePair `json:"size"`
		Type      KeyValuePair `json:"type"`
		Temp      float32      `json:"temp,omitempty"`
		TempUnits KeyValuePair `json:"tempUnits,omitempty"`
	}

	QuoteStatus struct {
		Code        KeyValuePair `json:"code"`
		Notes       string       `json:"notes,omitempty"`
		Description string       `json:"description,omitempty"`
		Category    string       `json:"category,omitempty"`
	}
)

// ---------------------------------------------------------------------------------------------------------
// Order-related structs
// ---------------------------------------------------------------------------------------------------------

type (
	OrderResponse struct {
		Status  string       `json:"Status"`
		Details OrderDetails `json:"details"`
	}

	OrderDetails struct {
		CreatedBy       OrderUser         `json:"createdBy"`
		UpdatedBy       OrderUser         `json:"updatedBy"`
		Direction       KeyValuePair      `json:"direction"`
		Origin          OrderStop         `json:"origin"`
		OriginFlex      []OrderFlexAttr   `json:"origin_flex_attributes"`
		Destination     OrderStop         `json:"destination"`
		DestinationFlex []OrderFlexAttr   `json:"destination_flex_attributes"`
		Customer        OrderAccount      `json:"customer"`
		Status          KeyValuePair      `json:"status"`
		Phase           KeyValuePair      `json:"phase"`
		Carrier         OrderAccount      `json:"carrier"`
		Shipments       []OrderShipment   `json:"shipments"`
		Items           []OrderItem       `json:"items"`
		Costs           OrderCosts        `json:"costs"`
		Equipment       []OrderEquipment  `json:"equipment"`
		LTLServices     []OrderLTLService `json:"ltlShipmentServices"`
		UserGroups      []OrderUserGroup  `json:"user_groups"`
		Date            string            `json:"date"`
		LastUpdatedOn   string            `json:"lastUpdatedOn"`
		CustomID        string            `json:"custom_id"`
		OrderType       OrderType         `json:"order_type"`
		StartDate       OrderDate         `json:"start_date"`
		EndDate         OrderDate         `json:"end_date"`
		ShipmentCount   int               `json:"shipment_count"`
		LineItemNumber  int               `json:"line_item_number_counter"`
		ExternalIDs     []OrderExternalID `json:"external_ids"`
		FlexAttributes  []OrderFlexAttr   `json:"flex_attributes"`
		ID              string            `json:"id"`
	}

	OrderUser struct {
		ID    int    `json:"id"`
		Name  string `json:"name"`
		Photo string `json:"photo"`
	}

	OrderStop struct {
		Location    Location  `json:"location"`
		Appointment OrderAppt `json:"appointment"`
	}

	OrderAppt struct {
		Date     string `json:"date"`
		Timezone string `json:"timezone"`
		HasTime  bool   `json:"hasTime"`
	}

	OrderFlexAttr struct {
		Active  bool             `json:"active"`
		Deleted bool             `json:"deleted"`
		ID      string           `json:"id"`
		Field   OrderFlexField   `json:"field"`
		Value   any              `json:"value"`
		Account OrderFlexAccount `json:"account"`
	}

	OrderFlexField struct {
		ID         string `json:"id"`
		Key        string `json:"key"`
		Name       string `json:"name"`
		Scope      string `json:"scope"`
		Type       string `json:"type"`
		Shareable  bool   `json:"shareable"`
		Filterable bool   `json:"filterable"`
		IsMultiUse bool   `json:"isMultiUse"`
	}

	OrderFlexAccount struct {
		Active  bool   `json:"active"`
		Deleted bool   `json:"deleted"`
		Name    string `json:"name"`
	}

	OrderAccount struct {
		ID            int                 `json:"id"`
		Name          string              `json:"name"`
		ParentAccount *OrderParentAccount `json:"parentAccount,omitempty"`
	}

	OrderParentAccount struct {
		Name string `json:"name"`
		Type string `json:"type"`
		ID   int    `json:"id"`
	}

	OrderShipment struct {
		Deleted bool `json:"deleted"`
		ID      int  `json:"id"`
	}

	OrderItem struct {
		Deleted          bool                `json:"deleted"`
		ID               string              `json:"id"`
		Quantity         OrderQuantity       `json:"quantity"`
		HandlingQuantity OrderQuantity       `json:"handling_quantity"`
		Category         KeyValuePair        `json:"category"`
		Name             string              `json:"name"`
		MinTemp          OrderTemp           `json:"minTemp"`
		MaxTemp          OrderTemp           `json:"maxTemp"`
		Dimensions       OrderDimensions     `json:"dimensions"`
		Value            OrderValue          `json:"value"`
		Attributes       OrderItemAttributes `json:"attributes"`
		Nmfc             string              `json:"nmfc"`
		Ref              string              `json:"ref"`
		Notes            string              `json:"notes"`
		ShippingName     string              `json:"shippingName"`
		HazardClass      string              `json:"hazardClass"`
		Identification   string              `json:"identification"`
		PackingGroup     KeyValuePair        `json:"packingGroup"`
		HasExceptions    bool                `json:"hasExceptions"`
		LotNumber        string              `json:"lot_number"`
		ItemID           string              `json:"item_id"`
		ItemDetails      []OrderItemDetail   `json:"item_details"`
		FreightClass     KeyValuePair        `json:"freight_class"`
		NmfcSub          string              `json:"nmfc_sub"`
		LineItemNumber   string              `json:"line_item_number"`
		NetWeight        OrderWeight         `json:"net_weight"`
		GrossWeight      OrderWeight         `json:"gross_weight"`
		UnitNetWeight    OrderWeight         `json:"unit_net_weight"`
		UnitGrossWeight  OrderWeight         `json:"unit_gross_weight"`
		Volume           OrderVolume         `json:"volume"`
		StackDimLimit    OrderStackDimLimit  `json:"stackDimensionsLimit"`
		LoadBearingCap   OrderLoadBearingCap `json:"loadBearingCapacity"`
		MaxStackCount    int                 `json:"maxStackCount"`
	}

	OrderQuantity struct {
		Quantity float32      `json:"quantity"`
		Unit     KeyValuePair `json:"unit"`
	}

	OrderTemp struct {
		Temp float32      `json:"temp"`
		Unit KeyValuePair `json:"unit"`
	}

	OrderDimensions struct {
		Active  bool         `json:"active"`
		Deleted bool         `json:"deleted"`
		Length  float32      `json:"length"`
		Width   float32      `json:"width"`
		Height  float32      `json:"height"`
		Units   KeyValuePair `json:"units"`
	}

	OrderValue struct {
		Active   bool         `json:"active"`
		Deleted  bool         `json:"deleted"`
		Currency KeyValuePair `json:"currency"`
		Amount   float32      `json:"amount"`
	}

	OrderItemAttributes struct {
		IsHazmat  bool `json:"is_hazmat"`
		Stackable bool `json:"stackable"`
	}

	OrderItemDetail struct {
		Active      bool          `json:"active"`
		Deleted     bool          `json:"deleted"`
		ID          string        `json:"id"`
		Value       string        `json:"value"`
		Type        KeyValuePair  `json:"type"`
		StringValue string        `json:"string_value"`
		LookupValue *KeyValuePair `json:"lookup_value,omitempty"`
	}

	OrderWeight struct {
		ID    string       `json:"id"`
		Value float32      `json:"value"`
		Unit  KeyValuePair `json:"unit"`
	}

	OrderVolume struct {
		Value float32      `json:"value"`
		Unit  KeyValuePair `json:"unit"`
	}

	OrderStackDimLimit struct {
		Height float32      `json:"height"`
		Width  float32      `json:"width"`
		Unit   KeyValuePair `json:"unit"`
	}

	OrderLoadBearingCap struct {
		Value float32      `json:"value"`
		Unit  KeyValuePair `json:"unit"`
	}

	OrderCosts struct {
		Active    bool            `json:"active"`
		Deleted   bool            `json:"deleted"`
		ID        string          `json:"id"`
		Amount    float32         `json:"amount"`
		Notes     string          `json:"notes"`
		LineItems []OrderLineItem `json:"line_items"`
	}

	OrderLineItem struct {
		Deleted  bool         `json:"deleted"`
		ID       string       `json:"id"`
		Type     KeyValuePair `json:"type"`
		Amount   float32      `json:"amount"`
		Qty      float32      `json:"qty"`
		Price    float32      `json:"price"`
		Billable bool         `json:"billable"`
		Notes    string       `json:"notes"`
	}

	OrderEquipment struct {
		Deleted       bool            `json:"deleted"`
		ID            string          `json:"id"`
		Attributes    OrderEquipAttrs `json:"attributes"`
		EquipmentType KeyValuePair    `json:"equipment_type"`
		EquipmentSize KeyValuePair    `json:"equipment_size"`
	}

	OrderEquipAttrs struct {
		Active  bool `json:"active"`
		Deleted bool `json:"deleted"`
		Weight  struct {
			Active  bool    `json:"active"`
			Deleted bool    `json:"deleted"`
			Weight  float32 `json:"weight"`
		} `json:"weight"`
		Temp struct {
			Temp float32 `json:"temp"`
		} `json:"temp"`
		Description string `json:"description"`
	}

	OrderLTLService struct {
		LTLShipmentService struct {
			ID    int    `json:"id"`
			Key   string `json:"key"`
			Value string `json:"value"`
		} `json:"ltlShipmentService"`
		Operation int `json:"_operation"`
	}

	OrderUserGroup struct {
		Active       bool   `json:"active"`
		Deleted      bool   `json:"deleted"`
		ID           int    `json:"id"`
		Name         string `json:"name"`
		DefaultGroup bool   `json:"defaultGroup"`
		Description  string `json:"description"`
	}

	OrderType struct {
		ID    int    `json:"id"`
		Value string `json:"value"`
		Key   string `json:"key"`
	}

	OrderDate struct {
		Date     string `json:"date"`
		Timezone string `json:"timezone"`
		HasTime  bool   `json:"hasTime"`
		UserDate string `json:"userDate"`
	}

	OrderExternalID struct {
		Active  bool          `json:"active"`
		Deleted bool          `json:"deleted"`
		ID      string        `json:"id"`
		Account *OrderAccount `json:"account,omitempty"`
		IDValue string        `json:"id_value"`
		IDType  KeyValuePair  `json:"id_type"`
	}
)

//
// ---------------------------------------------------------------------------------------------------------
// app.turvo.com API Shipment Response Structures
// ---------------------------------------------------------------------------------------------------------
//

type AppTurvoShipmentResponse struct {
	Details AppTurvoShipmentDetails `json:"details"`
}

type AppTurvoShipmentDetails struct {
	Date                      time.Time                  `json:"date"`
	EndDate                   AppTurvoDateTime           `json:"end_date"`
	Miles                     float32                    `json:"miles"`
	PoNumber                  string                     `json:"po_number"`
	LastUpdatedOn             time.Time                  `json:"lastUpdatedOn"`
	NeedAppointment           bool                       `json:"need_appointment"`
	AppointmentForNextStop    *AppTurvoDateTime          `json:"appointmentForNextStop"`
	Lane                      AppTurvoLane               `json:"lane"`
	StartDate                 AppTurvoDateTime           `json:"start_date"`
	BusID                     int                        `json:"busId"`
	GlobalRoute               AppTurvoGlobalRoute        `json:"global_route"`
	CustomID                  string                     `json:"custom_id"`
	LtlShipment               bool                       `json:"ltlShipment"`
	CustomerOrders            []AppTurvoCustomerOrder    `json:"customer_orders"`
	CreatedBy                 int                        `json:"createdBy"`
	ShipmentID                int                        `json:"shipmentId"`
	Status                    AppTurvoStatus             `json:"status"`
	Groups                    []int                      `json:"groups"`
	IndexedAttributes         []AppTurvoIndexedAttribute `json:"indexed_attributes"`
	CarrierOrders             []AppTurvoCarrierOrder     `json:"carrier_orders"`
	PlannedDateForNextStop    *AppTurvoDateTime          `json:"plannedDateForNextStop"`
	Margin                    AppTurvoMargin             `json:"margin"`
	NeedsAppointmentScheduled bool                       `json:"needs_appointment_scheduled"`
	IsDriverShipment          bool                       `json:"isDriverShipment"`
	IsFavorite                bool                       `json:"is_favorite"`
}

type AppTurvoGPS struct {
	Coordinates []float32 `json:"coordinates"`
}

type AppTurvoDateTime struct {
	Date     time.Time  `json:"date"`
	Flex     *int       `json:"flex,omitempty"`
	Timezone string     `json:"timezone"`
	HasTime  bool       `json:"hasTime"`
	UserDate *time.Time `json:"userDate,omitempty"`
	Time     *string    `json:"time,omitempty"`
}

type AppTurvoSimpleDateTime struct {
	Date     time.Time `json:"date"`
	Timezone string    `json:"timezone"`
}

type AppTurvoName struct {
	Name string `json:"name"`
}

type AppTurvoCountry struct {
	Name string  `json:"name"`
	Code *string `json:"code,omitempty"`
}

type AppTurvoNestedAddress struct {
	ID            *string         `json:"id,omitempty"`
	Active        *bool           `json:"active,omitempty"`
	Primary       *bool           `json:"primary,omitempty"`
	Line1         string          `json:"line1"`
	Line2         *string         `json:"line2,omitempty"`
	Line3         *string         `json:"line3,omitempty"`
	City          AppTurvoName    `json:"city"`
	State         AppTurvoName    `json:"state"`
	Zip           string          `json:"zip"`
	Country       AppTurvoCountry `json:"country"`
	GPS           *AppTurvoGPS    `json:"gps,omitempty"`
	UserAddress   *bool           `json:"userAddress,omitempty"`
	ValidAddress  *bool           `json:"validAddress,omitempty"`
	RealLocation  *bool           `json:"realLocation,omitempty"`
	CountryISO    *string         `json:"countryISO,omitempty"`
	Deleted       *bool           `json:"deleted,omitempty"`
	CreatedOn     *time.Time      `json:"createdOn,omitempty"`
	LastUpdatedOn *time.Time      `json:"lastUpdatedOn,omitempty"`
	SourceID      *string         `json:"sourceId,omitempty"`
}

type AppTurvoPhoneDetail struct {
	ID            *string    `json:"id,omitempty"`
	Active        *bool      `json:"active,omitempty"`
	Primary       bool       `json:"primary"`
	Extension     *string    `json:"extension,omitempty"`
	Number        string     `json:"number"`
	Deleted       *bool      `json:"deleted,omitempty"`
	Source        *string    `json:"source,omitempty"`
	SourceID      *string    `json:"sourceId,omitempty"`
	CreatedOn     *time.Time `json:"createdOn,omitempty"`
	LastUpdatedOn *time.Time `json:"lastUpdatedOn,omitempty"`
}

type AppTurvoEmailDetail struct {
	ID            *string    `json:"id,omitempty"`
	Active        *bool      `json:"active,omitempty"`
	Primary       bool       `json:"primary"`
	Email         string     `json:"email"`
	Deleted       *bool      `json:"deleted,omitempty"`
	Source        *string    `json:"source,omitempty"`
	SourceID      *string    `json:"sourceId,omitempty"`
	CreatedOn     *time.Time `json:"createdOn,omitempty"`
	LastUpdatedOn *time.Time `json:"lastUpdatedOn,omitempty"`
}

type AppTurvoCostType struct {
	ID   int    `json:"id"`
	Type string `json:"type"`
}

type AppTurvoLineItem struct {
	ID                int                  `json:"id"`
	Date              time.Time            `json:"date"`
	Amount            float32              `json:"amount"`
	Active            bool                 `json:"active"`
	SubTotal          float32              `json:"subTotal"`
	Type              AppTurvoLineItemType `json:"type"`
	Billable          bool                 `json:"billable"`
	Deleted           bool                 `json:"deleted"`
	Price             float32              `json:"price"`
	Qty               float32              `json:"qty"`
	GlobalRouteStopID *string              `json:"globalRouteStopId,omitempty"`
	Location          *AppTurvoLocationRef `json:"location,omitempty"`
}

type AppTurvoLineItemType struct {
	ID    int    `json:"id"`
	Key   string `json:"key"`
	Value string `json:"value"`
}

type AppTurvoLocationRef struct {
	ID   int    `json:"id"`
	Name string `json:"name"`
}

type AppTurvoCostDetails struct {
	Date                  *time.Time         `json:"date,omitempty"`
	Amount                float32            `json:"amount"`
	Active                bool               `json:"active"`
	LineItems             []AppTurvoLineItem `json:"line_items"`
	SubTotal              float32            `json:"subTotal"`
	TotalTax              float32            `json:"total_tax"`
	Type                  AppTurvoCostType   `json:"type"`
	Deleted               bool               `json:"deleted"`
	Contract              any                `json:"contract"`
	MarkupProfile         any                `json:"markup_profile"`
	IsCostPlus            *bool              `json:"isCostPlus,omitempty"`
	RecalculateAllAmounts *bool              `json:"recalculateAllAmounts,omitempty"`
	Fragments             []any              `json:"fragments,omitempty"`
}

type AppTurvoPaginationInfo struct {
	Start              int  `json:"start"`
	PageSize           int  `json:"pageSize"`
	TotalRecordsInPage int  `json:"totalRecordsInPage"`
	TotalRecords       int  `json:"totalRecords"`
	MoreAvailable      bool `json:"moreAvailable"`
	LastObjectKey      any  `json:"lastObjectKey"`
}

// --- Details Sub-structures ---

type AppTurvoPolylineProvider struct {
	Name    string `json:"name"`
	Version string `json:"version"`
}

type AppTurvoLane struct {
	PolylineProviders []AppTurvoPolylineProvider `json:"polylineProviders"`
	StartGPS          AppTurvoGPS                `json:"start_gps"`
	Start             string                     `json:"start"`
	MapData           string                     `json:"map_data"`
	End               string                     `json:"end"`
	EndGPS            AppTurvoGPS                `json:"end_gps"`
}

type AppTurvoCarrierOrderRef struct {
	Deleted        bool `json:"deleted"`
	CarrierOrderID int  `json:"carrier_order_id"`
	Active         bool `json:"active"`
	CarrierID      int  `json:"carrierId"`
}

type AppTurvoGeofenceRadius struct {
	Value float32 `json:"value"`
}

type AppTurvoGeofence struct {
	Type   string                  `json:"type"`
	Radius *AppTurvoGeofenceRadius `json:"radius,omitempty"`
}

type AppTurvoShipmentLocationDetail struct {
	ID                 int                   `json:"id"`
	Name               string                `json:"name"`
	Address            AppTurvoNestedAddress `json:"address"`
	TimeZoneName       string                `json:"timeZoneName"`
	Geofence           *AppTurvoGeofence     `json:"geofence,omitempty"`
	Timezone           string                `json:"timezone"`
	Photo              string                `json:"photo"`
	UseApptAsDwellTime bool                  `json:"useApptAsDwellTime"`
	FromGoogleLocation bool                  `json:"fromGoogleLocation"`
	Warehouse          bool                  `json:"warehouse"`
	Phone              []AppTurvoPhoneDetail `json:"phone"`
	Phones             []AppTurvoPhoneDetail `json:"phones"`
	GPS                *AppTurvoGPS          `json:"gps,omitempty"`
	Emails             []AppTurvoEmailDetail `json:"emails"`
}

type AppTurvoStopAttributeDetail struct {
	Date         *time.Time `json:"date,omitempty"`
	UserDateTime *time.Time `json:"userDateTime,omitempty"`
	Delay        any        `json:"delay"`
	Timezone     *string    `json:"timezone,omitempty"`
	UpdatedOn    *time.Time `json:"updatedOn,omitempty"`
	UserDate     *time.Time `json:"userDate,omitempty"`
	UpdateType   *string    `json:"updateType,omitempty"`
}

type AppTurvoStopAttributes struct {
	Arrival     *AppTurvoStopAttributeDetail `json:"arrival,omitempty"`
	Departed    *AppTurvoStopAttributeDetail `json:"departed,omitempty"`
	RunningLate map[string]any               `json:"running_late"`
	WaitingTime any                          `json:"waiting_time"`
}

type AppTurvoContactRef struct {
	ID     int                   `json:"id"`
	Name   string                `json:"name"`
	Phones []AppTurvoPhoneDetail `json:"phones"`
	Emails []AppTurvoEmailDetail `json:"emails"`
	Roles  any                   `json:"roles"`
	Title  *string               `json:"title,omitempty"`
}

type AppTurvoLayover struct {
	TotalLayover struct {
		Value int `json:"value"`
	} `json:"totalLayover"`
	LastUpdatedOn time.Time `json:"lastUpdatedOn"`
}

type AppTurvoEmailRef struct {
	Value  string `json:"value"`
	Source string `json:"source"`
}

type AppTurvoPhoneRef struct {
	ID        *string `json:"id,omitempty"`
	Active    *bool   `json:"active,omitempty"`
	Primary   bool    `json:"primary"`
	Extension *string `json:"extension,omitempty"`
	Number    string  `json:"number"`
	Source    string  `json:"source"`
}

type AppTurvoShipLocation struct {
	Date                      time.Time                      `json:"date"`
	Timezone                  string                         `json:"timezone"`
	Appointment               AppTurvoDateTime               `json:"appointment"`
	Miles                     float32                        `json:"miles"`
	IsShipmentDwellTimeEdited bool                           `json:"isShipmentDwellTimeEdited"`
	Eta                       string                         `json:"eta"`
	FragmentSequence          int                            `json:"fragment_sequence"`
	FragmentID                string                         `json:"fragment_id"`
	LastUpdatedOn             time.Time                      `json:"lastUpdatedOn"`
	MilesValue                string                         `json:"miles_value"`
	State                     string                         `json:"state"`
	CarrierOrders             []AppTurvoCarrierOrderRef      `json:"carrier_orders"`
	Address                   AppTurvoNestedAddress          `json:"address"`
	CustomID                  string                         `json:"custom_id"`
	GlobalShipLocationID      int                            `json:"global_ship_location_id"`
	Active                    bool                           `json:"active"`
	GlobalRouteStopID         string                         `json:"globalRouteStopId"`
	Location                  AppTurvoShipmentLocationDetail `json:"location"`
	Attributes                AppTurvoStopAttributes         `json:"attributes"`
	EtaVal                    string                         `json:"eta_val"`
}

type AppTurvoFragment struct {
	Date                time.Time                 `json:"date"`
	Deleted             bool                      `json:"deleted"`
	FragmentSequence    int                       `json:"fragment_sequence"`
	FragmentID          string                    `json:"fragment_id"`
	ShipLocations       []int                     `json:"ship_locations"`
	Active              bool                      `json:"active"`
	CarrierOrders       []AppTurvoCarrierOrderRef `json:"carrier_orders"`
	LastUpdatedOn       time.Time                 `json:"last_updated_on"`
	RecalculateDistance bool                      `json:"recalculateDistance"`
}

type AppTurvoTotalRouteFragmentValue struct {
	Currency AppTurvoCurrency `json:"currency"`
	Sync     bool             `json:"sync"`
	Value    float32          `json:"value"`
}

type AppTurvoCurrency struct {
	Value string `json:"value"`
	Key   string `json:"key"`
}

type AppTurvoModeInfo struct {
	ID                      int                             `json:"id"`
	Date                    time.Time                       `json:"date"`
	Deleted                 bool                            `json:"deleted"`
	FragmentID              string                          `json:"fragment_id"`
	Active                  bool                            `json:"active"`
	LastUpdatedOn           time.Time                       `json:"lastUpdatedOn"`
	TotalRouteFragmentValue AppTurvoTotalRouteFragmentValue `json:"totalRouteFragmentValue"`
	TotalLinearFeet         any                             `json:"total_linear_feet"`
	PaymentTerm             any                             `json:"payment_term"`
}

type AppTurvoGlobalRoute struct {
	ShipLocations []AppTurvoShipLocation `json:"ship_locations"`
	Fragments     []AppTurvoFragment     `json:"fragments"`
	ModeInfo      []AppTurvoModeInfo     `json:"mode_info"`
	Distance      AppTurvoDistance       `json:"distance"`
}

type AppTurvoDistance struct {
	Units AppTurvoUnits   `json:"units"`
	Value json.RawMessage `json:"value"` // turvo sends value as float32 or string occasionally
}

type AppTurvoUnits struct {
	ID    int    `json:"id"`
	Value string `json:"value"`
	Key   string `json:"key"`
}

type AppTurvoBillingTerms struct {
	ThirdParty          bool                  `json:"third_party"`
	BillTo              string                `json:"bill_to"`
	Address             AppTurvoNestedAddress `json:"address"`
	AutoInvoice         bool                  `json:"auto_invoice"`
	ID                  string                `json:"id"`
	Active              bool                  `json:"active"`
	Deleted             bool                  `json:"deleted"`
	CreatedOn           time.Time             `json:"createdOn"`
	LastUpdatedOn       time.Time             `json:"lastUpdatedOn"`
	BillingInstructions any                   `json:"billing_instructions"`
	Contact             AppTurvoContactRef    `json:"contact"`
	CreditLimit         float32               `json:"credit_limit"`
	Emails              []AppTurvoEmailDetail `json:"emails"`
}

type AppTurvoThreshold struct {
	IsEnabled bool `json:"isEnabled"`
	Value     int  `json:"value"`
}

type AppTurvoCustomerSettings struct {
	Late struct {
		AppointmentThresholds struct {
			Running     AppTurvoThreshold `json:"running"`
			Approaching AppTurvoThreshold `json:"approaching"`
		} `json:"appointmentThresholds"`
		NonAppointmentThresholds struct {
			Running     AppTurvoThreshold `json:"running"`
			Approaching AppTurvoThreshold `json:"approaching"`
		} `json:"nonAppointmentThresholds"`
	} `json:"late"`
}

type AppTurvoBilledWeight struct {
	WeightUnit KeyValuePair `json:"weight_unit"`
	Weight     float32      `json:"weight"`
}

type AppTurvoCustomerOrderContact struct {
	Date          time.Time          `json:"date"`
	Deleted       bool               `json:"deleted"`
	Contact       AppTurvoContactRef `json:"contact"`
	ShipContactID int                `json:"ship_contact_id"`
	Active        bool               `json:"active"`
	LastUpdatedOn time.Time          `json:"lastUpdatedOn"`
}

type AppTurvoCustomerOrder struct {
	Date               time.Time        `json:"date"`
	CustomID           string           `json:"custom_id"`
	CustomerOrderID    int              `json:"customer_order_id"`
	Active             bool             `json:"active"`
	Miles              float32          `json:"miles"`
	PreferredStartDate AppTurvoDateTime `json:"preferred_start_date"`
	Deleted            bool             `json:"deleted"`
	Terms              struct {
		BillTo AppTurvoBillingTerms `json:"bill_to"`
	} `json:"terms"`
	ShipLocations    []AppTurvoShipLocation `json:"ship_locations"`
	PreferredEndDate AppTurvoDateTime       `json:"preferred_end_date"`
	Costs            AppTurvoCostDetails    `json:"costs"`
	ActualStartDate  AppTurvoDateTime       `json:"actual_start_date"`
	BilledWeight     AppTurvoBilledWeight   `json:"billedWeight"`
}

type AppTurvoStatusAttributes struct {
	Address               *AppTurvoNestedAddress `json:"address,omitempty"`
	City                  *AppTurvoName          `json:"city,omitempty"`
	GPS                   *AppTurvoGPS           `json:"gps,omitempty"`
	GPSBaseTime           *time.Time             `json:"gps_base_time,omitempty"`
	GPSSystemTime         *time.Time             `json:"gps_system_time,omitempty"`
	EtaValUtc             any                    `json:"etaValUtc"`
	NextArrivalTime       *int                   `json:"next_arrival_time,omitempty"`
	NextEta               any                    `json:"next_eta"`
	NextEtaCalVal         *string                `json:"next_eta_cal_val,omitempty"`
	NextEtaCalValTimezone *string                `json:"next_eta_cal_val_timezone,omitempty"`
	NextMiles             *int                   `json:"next_miles,omitempty"`
	ReportedTimeUsed      any                    `json:"reportedTimeUsed"`
}

type AppTurvoStatus struct {
	Notes                string                   `json:"notes"`
	Description          string                   `json:"description"`
	LastUpdatedOn        time.Time                `json:"lastUpdatedOn"`
	Attributes           AppTurvoStatusAttributes `json:"attributes"`
	Category             string                   `json:"category"`
	BillingStatus        []any                    `json:"billing_status"`
	Auxiliaries          []any                    `json:"auxiliaries"`
	DispatchedDate       *AppTurvoDateTime        `json:"dispatched_date,omitempty"`
	FragmentID           string                   `json:"fragment_id"`
	GlobalShipLocationID int                      `json:"global_ship_location_id"`
	ShipLocationID       int                      `json:"ship_location_id"`
	NextLocationID       *int                     `json:"nextLocationId"`
	NextLocationType     *string                  `json:"nextLocationType"`
}

type AppTurvoIndexedAttribute struct {
	BusID int    `json:"busId"`
	Value any    `json:"value"`
	Key   string `json:"key"`
}

type AppTurvoCarrierOrderMode struct {
	Deleted          bool      `json:"deleted"`
	FragmentID       string    `json:"fragment_id"`
	CreatedAsDefault bool      `json:"createdAsDefault"`
	CarrierModeID    string    `json:"carrier_mode_id"`
	Active           bool      `json:"active"`
	LastUpdatedOn    time.Time `json:"lastUpdatedOn"`
	ID               string    `json:"id"`
	CreatedOn        time.Time `json:"createdOn"`
}

type AppTurvoSharing struct {
	IsEnabled     bool      `json:"is_enabled"`
	LastProcessed time.Time `json:"last_processed"`
	BusLinkedID   *string   `json:"bus_linked_id,omitempty"`
}

type AppTurvoCarrierContact struct {
	Email   string `json:"email"`
	Contact struct {
		LookupID   string `json:"lookup_id"`
		LookupName string `json:"lookup_name"`
	} `json:"contact"`
}

type AppTurvoThresholdSettings struct {
	Running     AppTurvoThreshold `json:"running"`
	Approaching AppTurvoThreshold `json:"approaching"`
}

type AppTurvoLateSettings struct {
	AppointmentThresholds    AppTurvoThresholdSettings `json:"appointmentThresholds"`
	NonAppointmentThresholds AppTurvoThresholdSettings `json:"nonAppointmentThresholds"`
}

type AppTurvoShipmentPostingSettings struct {
	IncludeInNetwork bool `json:"include_in_network"`
}

type AppTurvoLtlSettings struct {
	IsFakGroupEnabled bool           `json:"is_fak_group_enabled"`
	ShowInResults     bool           `json:"show_in_results"`
	Smc3              map[string]any `json:"smc3"`
	FakGroups         []any          `json:"fak_groups"`
}

type AppTurvoCarrierDetail struct {
	DotNumber      json.Number                `json:"dot_number"`
	McNumber       *string                    `json:"mc_number"`
	StatusID       int                        `json:"status_id"`
	IsSelfAccount  bool                       `json:"is_self_account"`
	BillContact    AppTurvoCarrierContact     `json:"bill_contact"`
	Name           string                     `json:"name"`
	Photo          string                     `json:"photo"`
	ID             int                        `json:"id"`
	Sharing        AppTurvoSharing            `json:"sharing"`
	Phone          []AppTurvoPhoneDetail      `json:"phone"`
	Advances       any                        `json:"advances"`
	Emails         []AppTurvoEmailDetail      `json:"emails"`
	Modes          []AppTurvoCarrierOrderMode `json:"modes"`
	BillToEntities any                        `json:"bill_to_entities"`
	Addresses      []AppTurvoNestedAddress    `json:"addresses"`
	Active         bool                       `json:"active"`
	Deleted        bool                       `json:"deleted"`
}

type AppTurvoInvoice struct {
	Date                time.Time `json:"date"`
	Timezone            string    `json:"timezone"`
	Description         string    `json:"description"`
	IsFullyPaid         bool      `json:"isFullyPaid"`
	InvoiceName         string    `json:"invoice_name"`
	InvoiceDate         time.Time `json:"invoice_date"`
	DeductionAmount     float32   `json:"deductionAmount"`
	InvoiceDateUtc      time.Time `json:"invoice_date_utc"`
	DueDateUtc          time.Time `json:"due_date_utc"`
	ID                  string    `json:"id"`
	InvoiceApprovedDate time.Time `json:"invoice_approved_date"`
	Amount              float32   `json:"amount"`
	InvoiceNo           string    `json:"invoice_no"`
	DueDate             time.Time `json:"due_date"`
	Active              bool      `json:"active"`
	DateUtc             time.Time `json:"date_utc"`
	Deleted             bool      `json:"deleted"`
	PaidAmount          float32   `json:"paid_amount"`
	Name                string    `json:"name"`
	DocumentID          string    `json:"documentId"`
	InvoiceID           string    `json:"invoiceId"`
}

type AppTurvoCarrierOrder struct {
	Date                               time.Time                  `json:"date"`
	Modes                              []AppTurvoCarrierOrderMode `json:"modes"`
	CustomID                           string                     `json:"custom_id"`
	ShouldFinishShipmentInsideGeofence bool                       `json:"shouldFinishShipmentInsideGeofence"`
	Active                             bool                       `json:"active"`
	ShipmentLevelStartShipmentTime     int                        `json:"shipmentLevelStartShipmentTime"`
	Sharing                            AppTurvoSharing            `json:"sharing"`
	OfferFragmentID                    any                        `json:"offer_fragment_id"`
	Carrier                            AppTurvoCarrierDetail      `json:"carrier"`
	Deleted                            bool                       `json:"deleted"`
	CarrierOrderID                     int                        `json:"carrier_order_id"`
	LastUpdatedOn                      time.Time                  `json:"lastUpdatedOn"`
	EquipmentSameAsNeeded              map[string]bool            `json:"equipment_same_as_needed"`
	Costs                              AppTurvoCostDetails        `json:"costs"`
	DueDate                            *time.Time                 `json:"due_date,omitempty"`
	DueDateTime                        *time.Time                 `json:"due_date_time,omitempty"`
	DueDateUtc                         *time.Time                 `json:"due_date_utc,omitempty"`
	InvoiceDate                        *time.Time                 `json:"invoice_date,omitempty"`
	InvoiceDateTime                    *time.Time                 `json:"invoice_date_time,omitempty"`
	InvoiceDateUtc                     *time.Time                 `json:"invoice_date_utc,omitempty"`
	InvoiceApprovedOnce                *bool                      `json:"invoiceApprovedOnce,omitempty"`
	Invoices                           []AppTurvoInvoice          `json:"invoices,omitempty"`
}

type AppTurvoMargin struct {
	Active           bool    `json:"active"`
	ActualMargin     float32 `json:"actual_margin"`
	ActualMarginVal  float32 `json:"actual_margin_val"`
	Deleted          bool    `json:"deleted"`
	TargetMargin     float32 `json:"target_margin"`
	TargetMarginVal  float32 `json:"target_margin_val"`
	TotalPayables    float32 `json:"total_payables"`
	TotalReceivables float32 `json:"total_receivables"`
}

// Routing Guide Models
type RoutingGuideRequest struct {
	Customer *struct {
		ID   int    `json:"id,omitempty"`
		Name string `json:"name,omitempty"`
	} `json:"customer,omitempty"`
	OriginID      int    `json:"origin_id"`
	DestinationID int    `json:"destination_id"`
	StartDate     string `json:"start_date"`
	EndDate       string `json:"end_date"`
	Name          string `json:"name,omitempty"`
	Status        *struct {
		Key   string `json:"key,omitempty"`
		Value string `json:"value,omitempty"`
	} `json:"status,omitempty"`
	Equipment *struct {
		Key   string `json:"key,omitempty"`
		Value string `json:"value,omitempty"`
	} `json:"equipment,omitempty"`
	Mode struct {
		Key   string `json:"key"`
		Value string `json:"value"`
	} `json:"mode"`
	ServiceType *struct {
		Key   string `json:"key,omitempty"`
		Value string `json:"value,omitempty"`
	} `json:"service_type,omitempty"`
	Size *struct {
		Key   string `json:"key,omitempty"`
		Value string `json:"value,omitempty"`
	} `json:"size,omitempty"`
	IsHazmat          bool              `json:"is_hazmat,omitempty"`
	LowCostPrecedence bool              `json:"low_cost_precedence,omitempty"`
	RoutingSequences  []RoutingSequence `json:"routing_sequences"`
	VisibilityGroups  []string          `json:"visibility_groups,omitempty"`
}

type RoutingSequence struct {
	Operation           int     `json:"operation"`
	Sequence            int     `json:"sequence"`
	Duration            int     `json:"duration"`
	RoutingSequenceType string  `json:"routing_sequence_type"`
	Rate                float64 `json:"rate,omitempty"`
	Currency            struct {
		Key   string `json:"key"`
		Value string `json:"value"`
	} `json:"currency,omitempty"`
	Emails []struct {
		Email string `json:"email"`
	} `json:"emails,omitempty"`
	Carriers []struct {
		ID int64 `json:"id"`
	} `json:"carriers"`
	SendTo []struct {
		Enabled bool   `json:"enabled"`
		Type    string `json:"type"`
	} `json:"send_to,omitempty"`
	ContractID     string `json:"contract_id,omitempty"`
	ContractItemID string `json:"contract_item_id,omitempty"`
}

type RoutingGuideResponse struct {
	Status  string `json:"status"`
	Details struct {
		ID           string `json:"id"` // Changed from int to string
		ErrorMessage string `json:"errorMessage"`
	} `json:"details"`
}

// CarrierSearchResponse represents the response from Turvo's carrier search endpoint
type CarrierSearchResponse struct {
	Status  string `json:"Status"`
	Details struct {
		ErrorResponse
		Pagination struct {
			Start              int  `json:"start"`
			PageSize           int  `json:"pageSize"`
			TotalRecordsInPage int  `json:"totalRecordsInPage"`
			MoreAvailable      bool `json:"moreAvailable"`
		} `json:"pagination"`
		Carriers []CarrierDetails `json:"carriers"`
	} `json:"details"`
}

// CarrierDetails is the canonical struct for a carrier in Turvo's system
type CarrierDetails struct {
	ErrorResponse

	ID   int    `json:"id"`
	Name string `json:"name"`
	Scac []Scac `json:"scac"`

	Status    Status    `json:"status"`
	Address   []Address `json:"address"`
	Email     []Email   `json:"email"`
	Phone     []Phone   `json:"phone"`
	Contact   Contact   `json:"contact"`
	Type      string    `json:"type"`
	McNumber  string    `json:"mcNumber"`
	DotNumber any       `json:"dotNumber"`
	// Removing unused fields that can cause unnecessary JSON marshalling errors
	// FederalEIN          string                `json:"federalEIN"`
	// Arb                 string                `json:"arb"`
	// ParentAccount       ParentAccount         `json:"parentAccount"`
	// Owner               Owner                 `json:"owner"`
	// ExternalIDs         []ExternalID          `json:"externalIds"`
	// AccountDistribution []AccountDistribution `json:"accountDistribution"`
	// Billing             Billing               `json:"billing"`
	// Claims              Claims                `json:"claims"`
	// Equipment           []Equipment           `json:"equipment"`
	// Insurance           []Insurance           `json:"insurance"`
	// PaymentMethod       []PaymentMethod       `json:"paymentMethod"`
	// PaymentTerm         []PaymentTerm         `json:"paymentTerm"`
	// Mode                []KeyValuePair        `json:"mode"`
	// Service             []KeyValuePair        `json:"service"`
	// Authority           Authority             `json:"authority"`
}

type (
	// OfferRequest represents the request body for creating an offer
	OfferRequest struct {
		OfferType         string         `json:"offerType"`
		AccountDetails    AccountDetails `json:"accountDetails"`
		ContextData       ContextData    `json:"contextData"`
		Currency          Currency       `json:"currency"`
		Uncounterable     bool           `json:"uncounterable"`
		ExpiryDate        string         `json:"expiryDate"`
		Quotes            []Quote        `json:"quotes"`
		Comments          string         `json:"comments"`
		Tags              []Tag          `json:"tags"`
		RequestSourceType string         `json:"requestSourceType"`
	}

	AccountDetails struct {
		ID            int            `json:"id"`
		Type          string         `json:"type"`
		Emails        []OfferEmail   `json:"emails"`
		Name          string         `json:"name"`
		McNumber      string         `json:"mc_number"`
		DotNumber     string         `json:"dot_number"`
		Sharing       Sharing        `json:"sharing"`
		OfferContacts []OfferContact `json:"offer_contacts"`
	}

	OfferEmail struct {
		Email string `json:"email"`
		Name  string `json:"name,omitempty"`
	}

	Sharing struct {
		IsShareable bool `json:"isShareable"`
	}

	OfferContact struct {
		ID   string `json:"id"`
		Name string `json:"name"`
	}

	ContextData struct {
		ContextID          string             `json:"contextId"`
		ContextType        string             `json:"contextType"`
		CustomID           string             `json:"customId"`
		TransportationType TransportationType `json:"transportationType"`
		GlobalRouteID      string             `json:"globalRouteId"`
		Origin             OfferLocation      `json:"origin"`
		Destination        OfferLocation      `json:"destination"`
		PickupDate         DateTime           `json:"pickupDate"`
		DeliveryDate       DateTime           `json:"deliveryDate"`
	}

	TransportationType struct {
		Mode Mode `json:"mode"`
		Type Type `json:"type"`
	}

	Mode struct {
		ID    int    `json:"id"`
		Key   string `json:"key"`
		Value string `json:"value"`
	}

	Type struct {
		ID    int    `json:"id"`
		Key   string `json:"key"`
		Value string `json:"value"`
	}

	OfferLocation struct {
		ID       int          `json:"id"`
		Timezone string       `json:"timezone"`
		Name     string       `json:"name"`
		Address  OfferAddress `json:"address"`
	}

	OfferAddress struct {
		City    OfferCity    `json:"city"`
		State   OfferState   `json:"state"`
		Country OfferCountry `json:"country"`
		GPS     GPS          `json:"gps"`
		Line1   string       `json:"line1"`
		Line2   string       `json:"line2"`
		Zip     string       `json:"zip"`
	}

	OfferCity struct {
		Name string `json:"name"`
	}

	OfferState struct {
		Name string `json:"name"`
	}

	OfferCountry struct {
		Name string `json:"name"`
		Code string `json:"code"`
	}

	GPS struct {
		Coordinates []float64 `json:"coordinates"`
	}

	DateTime struct {
		Date     string `json:"date"`
		Timezone string `json:"timezone"`
		HasTime  bool   `json:"hasTime"`
	}

	Currency struct {
		Key   string `json:"key"`
		Value string `json:"value"`
	}

	Quote struct {
		OwnerRequest OwnerRequest `json:"ownerRequest"`
	}

	OwnerRequest struct {
		Amount    float64         `json:"amount"`
		LineItems []OfferLineItem `json:"line_items"`
	}

	OfferLineItem struct {
		Price         float64       `json:"price"`
		Type          LineItemType  `json:"type"`
		Qty           float64       `json:"qty"`
		Amount        float64       `json:"amount"`
		RateQualifier RateQualifier `json:"rateQualifier"`
	}

	LineItemType struct {
		Key   string `json:"key"`
		Value string `json:"value"`
	}

	RateQualifier struct {
		Type Type `json:"type"`
	}

	Tag struct {
		TagID   int    `json:"tagId"`
		Scope   string `json:"scope"`
		TagName string `json:"tagName"`
		Type    string `json:"type"`
	}

	OfferResponse struct {
		Status  string `json:"status"`
		Details struct {
			ID           string `json:"id"`
			ErrorMessage string `json:"errorMessage,omitempty"`
		} `json:"details"`
	}
)
