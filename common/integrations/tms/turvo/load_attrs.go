package turvo

import (
	tmsutil "github.com/drumkitai/drumkit/common/integrations/tms/util"
	"github.com/drumkitai/drumkit/common/models"
)

var (
	DefaultLoadAttributes = models.LoadAttributes{
		// Internals
		Model:     models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
		ServiceID: models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
		Service:   models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},

		ExternalTMSID:     models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
		FreightTrackingID: models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
		Commodities:       models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},

		LoadCoreInfoAttributes: models.LoadCoreInfoAttributes{
			Mode:             models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
			MoreThanTwoStops: models.FieldAttributes{IsNotSupported: true},
			Status:           models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
			// TODO: double-check our handling of this field and how it's propagated to Customer and Carrier Refs
			PONums:   models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
			Operator: models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
			RateData: models.InitUnsupportedRateData,
			Customer: models.CustomerAttributes{
				// Read-only because the whole object mut be updated; we need to add support for searching &
				// autofilling entities. Same goes for BillTo, Pickup.CompanyCoreInfoAttributes,
				// and Consignee.CompanyCoreInfoAttributes
				CompanyCoreInfoAttributes: models.CompanyCoreInfoAttributes{
					ExternalTMSID: models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					Name:          models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					AddressLine1:  models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					AddressLine2:  models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					City:          models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					State:         models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					Zipcode:       models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					Country:       models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					Contact:       models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					Phone:         models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					Email:         models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
				},
				RefNumber: models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
			},
			BillTo: models.BillToAttributes{
				CompanyCoreInfoAttributes: models.CompanyCoreInfoAttributes{
					ExternalTMSID: models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					Name:          models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					AddressLine1:  models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					AddressLine2:  models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					City:          models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					State:         models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					Zipcode:       models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					Country:       models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					Contact:       models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					Phone:         models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					Email:         models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
				},
			},
			Pickup: models.PickupAttributes{
				CompanyCoreInfoAttributes: models.CompanyCoreInfoAttributes{
					ExternalTMSID: models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					Name:          models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					AddressLine1:  models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					AddressLine2:  models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					City:          models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					State:         models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					Zipcode:       models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					Country:       models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					Contact:       models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					Phone:         models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					Email:         models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
				},
				ExternalTMSStopID: models.FieldAttributes{IsNotSupported: true},
				ApptRequired:      models.FieldAttributes{IsNotSupported: true},
				ApptType:          models.FieldAttributes{IsNotSupported: false, IsReadOnly: false},
				BusinessHours:     models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				RefNumber:         models.FieldAttributes{IsNotSupported: false, IsReadOnly: false},
				ReadyTime:         models.FieldAttributes{IsNotSupported: false, IsReadOnly: false},
				ApptStartTime:     models.FieldAttributes{IsNotSupported: false, IsReadOnly: false},
				ApptEndTime:       models.FieldAttributes{IsNotSupported: true},
				ApptNote:          models.FieldAttributes{IsNotSupported: false, IsReadOnly: false},
			},
			Consignee: models.ConsigneeAttributes{
				CompanyCoreInfoAttributes: models.CompanyCoreInfoAttributes{
					ExternalTMSID: models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					Name:          models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					AddressLine1:  models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					AddressLine2:  models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					City:          models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					State:         models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					Zipcode:       models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					Country:       models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					Contact:       models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					Phone:         models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					Email:         models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
				},
				ExternalTMSStopID: models.FieldAttributes{IsNotSupported: true},
				ApptRequired:      models.FieldAttributes{IsNotSupported: true},
				ApptType:          models.FieldAttributes{IsNotSupported: false, IsReadOnly: false},
				BusinessHours:     models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				RefNumber:         models.FieldAttributes{IsNotSupported: false, IsReadOnly: false},
				MustDeliver:       models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				ApptStartTime:     models.FieldAttributes{IsNotSupported: false, IsReadOnly: false},
				ApptEndTime:       models.FieldAttributes{IsNotSupported: true},
				ApptNote:          models.FieldAttributes{IsNotSupported: false, IsReadOnly: false},
			},
			Carrier: models.CarrierAttributes{
				MCNumber:                 models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
				DOTNumber:                models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
				Name:                     models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
				Phone:                    models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
				Dispatcher:               models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
				Notes:                    models.FieldAttributes{IsNotSupported: true},
				SealNumber:               models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				SCAC:                     models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
				FirstDriverName:          models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
				FirstDriverPhone:         models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
				SecondDriverName:         models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
				SecondDriverPhone:        models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
				Email:                    models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
				DispatchSource:           models.FieldAttributes{IsNotSupported: true},
				DispatchCity:             models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
				DispatchState:            models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
				ExternalTMSTruckID:       models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				ExternalTMSTrailerID:     models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				RateConfirmationSent:     models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				ConfirmationSentTime:     models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				ConfirmationReceivedTime: models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				DispatchedTime:           models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				// Always equals Pickup.ApptStartTime
				ExpectedPickupTime: models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
				PickupStart:        models.FieldAttributes{IsNotSupported: false, IsReadOnly: false},
				PickupEnd:          models.FieldAttributes{IsNotSupported: false, IsReadOnly: false},
				// Always equals Consignee.ApptStartTime
				ExpectedDeliveryTime: models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
				DeliveryStart:        models.FieldAttributes{IsNotSupported: false, IsReadOnly: false},
				DeliveryEnd:          models.FieldAttributes{IsNotSupported: false, IsReadOnly: false},
				SignedBy:             models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
			},
			Specifications: models.SpecificationsAttributes{
				OrderType:           models.FieldAttributes{IsNotSupported: true},
				ServiceType:         models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
				TotalInPalletCount:  models.FieldAttributes{IsNotSupported: true},
				TotalOutPalletCount: models.FieldAttributes{IsNotSupported: true},
				TotalPieces:         models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
				Commodities:         models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
				NumCommodities:      models.FieldAttributes{IsNotSupported: true},
				TotalWeight:         models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
				NetWeight:           models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
				TransportWeight:     models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
				TransportSize:       models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
				BillableWeight:      models.FieldAttributes{IsNotSupported: true},
				TotalDistance:       models.FieldAttributes{IsNotSupported: true},

				MinTempFahrenheit: models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
				MaxTempFahrenheit: models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				LiftgatePickup:    models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				LiftgateDelivery:  models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				InsidePickup:      models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				InsideDelivery:    models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				Tarps:             models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				Oversized:         models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				Hazmat:            models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				Straps:            models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				Permits:           models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				Escorts:           models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				Seal:              models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				CustomBonded:      models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				Labor:             models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
			},
		},
	}
)

func (t *Turvo) GetDefaultLoadAttributes() models.LoadAttributes {
	attrs := DefaultLoadAttributes
	tmsutil.ApplyTMSFeatureFlags(&t.tms, &attrs)

	return attrs
}
