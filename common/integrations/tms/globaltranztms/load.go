package globaltranztms

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"
	"unicode"

	"go.opentelemetry.io/otel/attribute"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/timezone"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

type GlobalTranzOrderStatus struct {
	Code int    `json:"code"`
	Name string `json:"name"`
}

const missingOrderBKError = "OrderBk = %s missing"

func (gt GlobalTranz) GetLoad(ctx context.Context, orderBK string) (
	res models.Load,
	attrs models.LoadAttributes,
	err error,
) {
	spanAttrs := append(otel.IntegrationAttrs(gt.tms),
		attribute.String("orderBK", orderBK))
	ctx, metaSpan := otel.StartSpan(ctx, "GetLoadGlobalTranzTMS", spanAttrs)
	defer func() { metaSpan.End(err) }()

	defaultAttrs := gt.GetDefaultLoadAttributes()

	orderDetailsResponse, err := gt.GetOrderDetails(ctx, orderBK)
	if err != nil {
		respErr := fmt.Errorf("GetOrderDetails failed: %w", err)

		if strings.Contains(err.Error(), fmt.Sprintf(missingOrderBKError, orderBK)) {
			respErr = errtypes.HTTPResponseError{
				IntegrationName: gt.tms.Name,
				IntegrationType: gt.tms.Type,
				AxleTSPID:       gt.tms.ID,
				ServiceID:       gt.tms.ServiceID,
				HTTPMethod:      http.MethodGet,
				URL:             fmt.Sprintf("%s?%s", gt.tmsHost, fmt.Sprintf("orderBK=%s", orderBK)),
				StatusCode:      http.StatusNotFound,
			}
		}

		return models.Load{}, defaultAttrs, respErr
	}

	return gt.globaltranzOrderToLoad(ctx, *orderDetailsResponse), defaultAttrs, nil
}

func (gt GlobalTranz) UpdateLoad(
	ctx context.Context,
	_ *models.Load,
	load *models.Load,
) (models.Load, models.LoadAttributes, error) {
	var err error

	spanAttrs := append(otel.IntegrationAttrs(gt.tms), otel.LoadAttrs(*load)...)
	ctx, metaSpan := otel.StartSpan(ctx, "UpdateLoadAscend", spanAttrs)
	defer func() { metaSpan.End(err) }()

	// Fetch load from TMS and apply updates to it before submitting, since GLobalTranz
	// has a multitude of fields that we don't store and don't want to overwrite with empty values
	tmsLoad, err := gt.GetOrderDetails(ctx, load.FreightTrackingID)
	if err != nil {
		return models.Load{}, gt.GetDefaultLoadAttributes(), fmt.Errorf("get load failed: %w", err)
	}

	attrs := gt.GetDefaultLoadAttributes()
	reqBody := gt.loadToGlobalTranzUpdateOrder(ctx, *tmsLoad, *load)
	if err != nil {
		return models.Load{}, attrs, err
	}

	updatedOrder, err := gt.UpdateOrder(ctx, reqBody)
	if err != nil {
		return models.Load{}, attrs, fmt.Errorf("updating Load failed: %w", err)
	}

	return gt.globaltranzOrderToLoad(ctx, updatedOrder), models.LoadAttributes{}, nil
}

func (gt GlobalTranz) globaltranzOrderToLoad(ctx context.Context, globalTranzOrderDetails OrderDetails) models.Load {

	loadStatus, err := gt.mapOrderBoardStatusToLoadStatus(globalTranzOrderDetails.StatusID)
	if err != nil {
		log.Warn(ctx, "unknown gtz status code", zap.Int("statusCode", globalTranzOrderDetails.StatusID))
	}

	loadID := strconv.Itoa(globalTranzOrderDetails.OrderBK)
	result := models.Load{
		FreightTrackingID: loadID,
		ExternalTMSID:     loadID,
		ServiceID:         gt.tms.ServiceID,
		TMSID:             gt.tms.ID,
		LoadCoreInfo: models.LoadCoreInfo{
			Status: loadStatus,
			// PONums: getIdentifierTypeparseIdentifiers(globalTranzOrderDetails.Identifiers),
			Mode: gt.mapServiceTypeToLoadMode(globalTranzOrderDetails.TLEquipment.TLServiceTypeID),
		},
	}

	for _, identifier := range globalTranzOrderDetails.Identifiers {
		identifierType := gt.mapIdentifierCodeToIdentifierType(identifier.TypeID)
		if identifierType == "" {
			continue
		}

		identifierValues := parseIdentifiers(identifier.Value)
		if len(identifierValues) == 0 {
			continue
		}

		identifierValuesCommaSeparated := strings.Join(identifierValues, ", ")

		switch identifierType {
		case PoNumberType:
			result.PONums = identifierValuesCommaSeparated
		case CustomerRefNumberType:
			result.Customer.RefNumber = identifierValuesCommaSeparated
		case PickupNumberType:
			result.Pickup.RefNumber = identifierValuesCommaSeparated
		case DeliveryNumberType:
			result.Consignee.RefNumber = identifierValuesCommaSeparated
		}
	}

	// Convert float64 to *float32 for CarrierCost
	if globalTranzOrderDetails.Cost.Total != 0 {
		carrierCost := float32(globalTranzOrderDetails.Cost.Total)
		result.RateData.CarrierCost = &carrierCost
		result.RateData.CarrierCostCurrency = "USD"
	}

	result.RateData.NetProfitUSD = float32(globalTranzOrderDetails.Cost.TargetMargin)
	result.RateData.CarrierMaxRate = float32(globalTranzOrderDetails.Cost.MaxBuy)

	result.Specifications.TransportType = string(
		gt.mapEquipmentTypeToTransportType(globalTranzOrderDetails.TLEquipment.TruckTypeID),
	)

	result.Specifications.TotalDistance = models.ValueUnit{
		Val:  float32(globalTranzOrderDetails.Miles),
		Unit: models.MilesUnit,
	}

	// Map customer information
	result.Customer.ExternalTMSID = strconv.Itoa(globalTranzOrderDetails.Customer.CustomerBK)
	result.Customer.Name = globalTranzOrderDetails.Customer.CustomerName
	if globalTranzOrderDetails.Customer.Phone != nil {
		result.Customer.Phone = *globalTranzOrderDetails.Customer.Phone
	}

	if globalTranzOrderDetails.Customer.Email != nil {
		result.Customer.Email = *globalTranzOrderDetails.Customer.Email
	}

	// Map bill to information
	for _, addr := range globalTranzOrderDetails.Address {
		if addr.StopType == "Pickup" {
			result.Pickup.ExternalTMSID = strconv.Itoa(addr.ID)
			result.Pickup.Name = addr.Company

			locationTimezone, err := timezone.GetTimezone(ctx, addr.Address.City, addr.Address.State, "")
			if err != nil {
				log.WarnNoSentry(ctx, "error getting timezone", zap.Error(err))
			}

			result.Pickup.Timezone = locationTimezone
			result.Pickup.ApptStartTime = parseTime(addr.FreightDateTime, addr.FreightOpenTime, locationTimezone)
			result.Pickup.ApptEndTime = parseTime(addr.FreightDateTime, addr.FreightCloseTime, locationTimezone)
			if addr.Remarks != nil {
				result.Pickup.ApptNote = *addr.Remarks
			}

			result.Pickup.ApptRequired = addr.IsPickupAppointmentRequired
			result.Pickup.AddressLine1 = addr.Address.Street1
			if addr.Address.Street2 != nil {
				result.Pickup.AddressLine2 = *addr.Address.Street2
			}

			result.Pickup.City = addr.Address.City
			result.Pickup.State = addr.Address.State
			result.Pickup.Zipcode = addr.Address.Zip
			result.Pickup.Country = strconv.Itoa(addr.Address.Country)

			result.Pickup.Contact = addr.Contact.ContactPerson
			result.Pickup.Phone = addr.Contact.Phone
			result.Pickup.Email = addr.Contact.Email
		}

		if addr.StopType == "Drop" {
			result.Consignee.ExternalTMSID = strconv.Itoa(addr.ID)
			result.Consignee.Name = addr.Company

			locationTimezone, err := timezone.GetTimezone(ctx, addr.Address.City, addr.Address.State, "")
			if err != nil {
				log.WarnNoSentry(ctx, "error getting timezone", zap.Error(err))
			}

			result.Consignee.Timezone = locationTimezone
			result.Consignee.ApptStartTime = parseTime(addr.FreightDateTime, addr.FreightOpenTime, locationTimezone)
			result.Consignee.ApptEndTime = parseTime(addr.FreightDateTime, addr.FreightCloseTime, locationTimezone)
			if addr.Remarks != nil {
				result.Consignee.ApptNote = *addr.Remarks
			}

			result.Consignee.ApptRequired = addr.IsDeliveryAppointmentRequired
			result.Consignee.AddressLine1 = addr.Address.Street1
			if addr.Address.Street2 != nil {
				result.Consignee.AddressLine2 = *addr.Address.Street2
			}

			result.Consignee.City = addr.Address.City
			result.Consignee.State = addr.Address.State
			result.Consignee.Zipcode = addr.Address.Zip
			result.Consignee.Country = strconv.Itoa(addr.Address.Country)

			result.Consignee.Contact = addr.Contact.ContactPerson
			result.Consignee.Phone = addr.Contact.Phone
			result.Consignee.Email = addr.Contact.Email
		}

		if addr.StopType == "Bill To" {
			result.BillTo.ExternalTMSID = strconv.Itoa(addr.ID)
			result.BillTo.Name = addr.Company

			result.BillTo.AddressLine1 = addr.Address.Street1
			if addr.Address.Street2 != nil {
				result.BillTo.AddressLine2 = *addr.Address.Street2
			}
			result.BillTo.City = addr.Address.City
			result.BillTo.State = addr.Address.State
			result.BillTo.Zipcode = addr.Address.Zip
			result.BillTo.Country = strconv.Itoa(addr.Address.Country)

			result.BillTo.Contact = addr.Contact.ContactPerson
			result.BillTo.Phone = addr.Contact.Phone
			result.BillTo.Email = addr.Contact.Email
		}
	}

	// Map carrier information
	if globalTranzOrderDetails.Carrier != nil {
		result.Carrier.MCNumber = globalTranzOrderDetails.Carrier.MCNumber
		result.Carrier.Name = globalTranzOrderDetails.Carrier.CarrierName

		if (globalTranzOrderDetails.Carrier.CarrierContact != Contact{}) {
			result.Carrier.Dispatcher = globalTranzOrderDetails.Carrier.CarrierContact.ContactPerson
			result.Carrier.Phone = globalTranzOrderDetails.Carrier.CarrierContact.Phone
			result.Carrier.Email = globalTranzOrderDetails.Carrier.CarrierContact.Email
		}
	}

	return result
}

func (gt GlobalTranz) loadToGlobalTranzUpdateOrder(
	ctx context.Context,
	tmsLoad OrderDetails,
	dbLoad models.Load,
) OrderDetails {

	newAddresses := []Address{}

	// TODO: Expand list of editable fields
	for _, addr := range tmsLoad.Address {
		if addr.StopType == "Pickup" {
			timezone, err := helpers.Timezone(dbLoad.Pickup.Timezone)
			if err != nil {
				log.WarnNoSentry(ctx, "error getting timezone", zap.Error(err))
			}

			dbLoadDate := fmt.Sprintf("%sT00:00:00", dbLoad.Pickup.ApptStartTime.Time.Format("2006-01-02"))
			dbLoadStartTime := dbLoad.Pickup.ApptStartTime.Time.In(timezone).Format("15:04")
			dbLoadEndTime := dbLoad.Pickup.ApptEndTime.Time.In(timezone).Format("15:04")

			addr.IsAppointmentModified = true
			addr.FreightDateTime = dbLoadDate
			addr.FreightOpenTime = dbLoadStartTime
			addr.FreightCloseTime = dbLoadEndTime

			newAddresses = append(newAddresses, addr)
		}

		if addr.StopType == "Drop" {
			timezone, err := helpers.Timezone(dbLoad.Consignee.Timezone)
			if err != nil {
				log.WarnNoSentry(ctx, "error getting timezone", zap.Error(err))
			}

			dbLoadDate := dbLoad.Consignee.ApptStartTime.Time.Format("2006-01-02")
			dbLoadStartTime := dbLoad.Consignee.ApptStartTime.Time.In(timezone).Format("15:04")
			dbLoadEndTime := dbLoad.Consignee.ApptEndTime.Time.In(timezone).Format("15:04")

			addr.IsAppointmentModified = true
			addr.FreightDateTime = dbLoadDate
			addr.FreightOpenTime = dbLoadStartTime
			addr.FreightCloseTime = dbLoadEndTime

			newAddresses = append(newAddresses, addr)
		}
	}

	tmsLoad.Address = newAddresses
	// Field required by UpdateLoad but not returned by GetLoad
	tmsLoad.Routes = tmsLoad.Address

	return tmsLoad
}

// parseTime converts a string time to time.Time
func parseTime(dateStr, timeStr, timezone string) models.NullTime {
	dateStrSplit := strings.Split(dateStr, "T")

	invalidNullTime := models.NullTime{
		Time:  time.Time{},
		Valid: false,
	}

	tz, err := helpers.Timezone(timezone)
	if err != nil {
		return invalidNullTime
	}

	t, err := time.ParseInLocation("2006-01-02 15:04", fmt.Sprintf("%s %s", dateStrSplit[0], timeStr), tz)
	if err == nil {
		return models.NullTime{
			Time:  t,
			Valid: true,
		}
	}

	return invalidNullTime
}

func (gt GlobalTranz) GetLoadIDs(ctx context.Context, query models.SearchLoadsQuery) (
	[]string,
	error,
) {
	loadIDMap := make(map[string]struct{}) // Use map for deduplication

	// Fetch loads based on update date
	loadIDs, err := gt.GetAllTlOrderLoadIDs(ctx, query.FromDate.Time, query.ToDate.Time, 10)
	if err != nil {
		return mapKeysToSlice(loadIDMap), fmt.Errorf("error fetching loads by update date: %w", err)
	}

	for _, loadID := range loadIDs {
		loadIDMap[loadID] = struct{}{}
	}

	allLoadIDs := mapKeysToSlice(loadIDMap)
	log.Info(ctx, "Finished fetching all pages of load IDs", zap.Int("totalCount", len(allLoadIDs)))
	return allLoadIDs, nil
}

func (gt GlobalTranz) GetLoadsByIDType(
	ctx context.Context,
	id string,
	idType string,
) (_ []models.Load, _ models.LoadAttributes, err error) {

	spanAttrs := append(otel.IntegrationAttrs(gt.tms),
		attribute.String("id", id), attribute.String("idType", idType))

	ctx, metaSpan := otel.StartSpan(ctx, "GetLoadsByIDTypeGlobalTranzTMS", spanAttrs)
	defer func() { metaSpan.End(err) }()

	switch idType {
	case BOLNumberType:
		load, attrs, err := gt.GetLoad(ctx, id)
		return []models.Load{load}, attrs, err

	default:
		return nil, gt.GetDefaultLoadAttributes(), fmt.Errorf("unrecognized ID type: %s", idType)
	}
}

func (gt GlobalTranz) GetTestLoads() map[string]bool {
	return map[string]bool{}
}

func (gt GlobalTranz) mapServiceTypeToLoadMode(serviceTypeCode int) models.LoadMode {
	switch serviceTypeCode {
	case 0:
		return models.NoneMode
	case 1:
		return models.TLMode
	case 2:
		return models.PartialMode
	case 3:
		return models.BothMode
	case 4:
		return models.VolumeMode
	default:
		return models.TLMode
	}
}

func (gt GlobalTranz) mapIdentifierCodeToIdentifierType(identifierType int) string {
	switch identifierType {
	case 2:
		return PoNumberType
	case 3:
		return CustomerRefNumberType
	case 4:
		return PickupNumberType
	case 8:
		return DeliveryNumberType
	default:
		return ""
	}
}

func parseIdentifiers(input string) []string {
	var result []string
	input = strings.TrimSpace(input)

	// Split based on spaces, slashes, and parentheses
	parts := strings.FieldsFunc(input, func(r rune) bool {
		return r == '/' || r == ',' || r == ' ' || r == '(' || r == ')'
	})

	// Add non-empty parts to result only if they contain numbers
	for _, part := range parts {
		if part != "" && containsNumbers(part) {
			result = append(result, part)
		}
	}

	return result
}

// Helper function to check if a string contains any digits
func containsNumbers(s string) bool {
	for _, r := range s {
		if unicode.IsDigit(r) {
			return true
		}
	}
	return false
}

func mapKeysToSlice(m map[string]struct{}) []string {
	result := make([]string, 0, len(m))
	for k := range m {
		result = append(result, k)
	}
	return result
}
