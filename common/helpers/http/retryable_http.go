package httputil

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"strings"
	"time"

	retryhttp "github.com/hashicorp/go-retryablehttp"
	"go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/httplog"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/fn/api/aws"
)

var retryableClient = createCustomClient()

func GetBytesWithToken(
	ctx context.Context,
	integration models.Integration,
	addr url.URL,
	headerMap map[string]string,
	authorization *string,
	dataType s3backup.DataType,
) ([]byte, *http.Response, error) {

	// NOTE: we require a URL parameter instead of a string to avoid possible injection attacks
	req, err := retryhttp.NewRequestWithContext(ctx, http.MethodGet, addr.String(), nil)
	if err != nil {
		response := &http.Response{StatusCode: http.StatusInternalServerError}
		return nil, response, fmt.Errorf("failed to build GET request: %w", err)
	}

	// Set first so that caller can override with custom "Accept" header
	req.Header.Set("Accept", "application/json")

	for key, value := range headerMap {
		if strings.TrimSpace(key) != "" {
			req.Header.Set(key, value)
		}
	}

	if authorization != nil {
		req.Header.Set("Authorization", *authorization)
	}

	// Not initialized in createCustomClient because we don't have integration object at that point
	retryableClient.ResponseLogHook = func(logger retryhttp.Logger, resp *http.Response) {
		body := httplog.GetHTTPLogJSON(ctx, integration, resp.StatusCode)
		if len(body) > 0 {
			logger.Printf(string(body))
		}
	}

	resp, err := retryableClient.Do(req)
	if err != nil {
		httplog.LogHTTPRequestFailed(ctx, integration, err)
		return nil, resp, fmt.Errorf("failed to send GET request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, resp, fmt.Errorf("failed to read response body: %w", err)
	}

	if aws.S3Uploader != nil && dataType != "" {
		if _, err = aws.S3Uploader.TMSResponse(ctx, integration, dataType,
			helpers.APIResponse{Method: http.MethodGet, Status: resp.StatusCode, Body: string(body)}); err != nil {
			// fail-open: continue processing even if S3 archive failed
			log.Warn(ctx, "s3 archive failed", zap.Error(err), zap.String("dataType", string(dataType)))
		}
	}

	if resp.StatusCode >= http.StatusBadRequest {
		return nil, resp,
			errtypes.NewHTTPResponseError(integration, &http.Request{Method: req.Method, URL: req.URL}, resp, body)
	}

	return body, resp, nil
}

func PostBytesWithToken(
	ctx context.Context,
	integration models.Integration,
	addr url.URL,
	reqBody any,
	headerMap map[string]string,
	authorization *string,
	dataType s3backup.DataType,
) (respBody []byte, resp *http.Response, err error) {

	return writeBytesWithToken(ctx, integration, http.MethodPost, addr, reqBody, headerMap, authorization, dataType)
}

func PutBytesWithToken(
	ctx context.Context,
	integration models.Integration,
	addr url.URL,
	reqBody any,
	headerMap map[string]string,
	authorization *string,
	dataType s3backup.DataType,
) (respBody []byte, resp *http.Response, err error) {

	return writeBytesWithToken(ctx, integration, http.MethodPut, addr, reqBody, headerMap, authorization, dataType)
}

func writeBytesWithToken(
	ctx context.Context,
	integration models.Integration,
	method string,
	addr url.URL,
	reqBody any,
	headerMap map[string]string,
	authorization *string,
	dataType s3backup.DataType,
) (respBody []byte, resp *http.Response, err error) {

	var bodyBytes []byte
	if reqBody != nil {
		bodyBytes, err = json.Marshal(reqBody)
		if err != nil {
			return respBody, nil, fmt.Errorf("error marshaling %s body: %w", method, err)
		}
	}

	log.Debug(
		ctx,
		"retryableClient request",
		zap.ByteString("reqBody", bodyBytes),
		zap.String("endpoint", addr.String()),
	)

	req, err := retryhttp.NewRequestWithContext(ctx, method, addr.String(), bytes.NewReader(bodyBytes))
	if err != nil {
		return respBody, nil, fmt.Errorf("error building %s request: %w", method, err)
	}

	if authorization != nil {
		req.Header.Set("Authorization", *authorization)
	}

	req.Header.Set("Accept", "application/json")
	req.Header.Set("Content-Type", "application/json")

	for key, value := range headerMap {
		if strings.TrimSpace(key) != "" {
			req.Header.Set(key, value)
		}
	}

	// Not initialized in createCustomClient because we don't have integration object at that point
	retryableClient.ResponseLogHook = func(logger retryhttp.Logger, resp *http.Response) {
		body := httplog.GetHTTPLogJSON(ctx, integration, resp.StatusCode)
		if len(body) > 0 {
			logger.Printf(string(body))
		}

		// TODO: For debugging, remove (it's fine to flush the response body because
		// retryable behavior is to return an error, not a response body, if all attempts fail)
		if resp.StatusCode >= http.StatusInternalServerError {
			respBody, err = io.ReadAll(resp.Body)
			if err != nil {
				logger.Printf("error reading response body: %w\n", err)
				return
			}

			logger.Printf("retryable client response body: %s\n", string(respBody))
		}
	}

	resp, err = retryableClient.Do(req)
	if err != nil {
		httplog.LogHTTPRequestFailed(ctx, integration, err)
		return respBody, nil, fmt.Errorf("error making %s request: %w", method, err)
	}
	defer resp.Body.Close()

	respBody, err = io.ReadAll(resp.Body)
	if err != nil {
		return respBody, resp, fmt.Errorf("error reading response body: %w", err)
	}

	if aws.S3Uploader != nil && dataType != "" {
		if _, err = aws.S3Uploader.TMSResponse(ctx, integration, dataType,
			helpers.APIResponse{Method: method, Status: resp.StatusCode, Body: string(respBody)}); err != nil {
			// fail-open: continue processing even if S3 archive failed
			log.Warn(ctx, "s3 archive failed", zap.Error(err), zap.String("dataType", string(dataType)))
		}
	}

	log.Debug(
		ctx,
		"retryableClient response",
		zap.ByteString("respBody", respBody),
		zap.String("endpoint", req.URL.String()),
	)

	if resp.StatusCode >= http.StatusBadRequest {
		return respBody, resp,
			errtypes.NewHTTPResponseError(integration, &http.Request{Method: req.Method, URL: req.URL}, resp, respBody)
	}

	return respBody, resp, nil
}

func GetIPAddress() (string, error) {
	resp, err := otel.TracingHTTPClient().Get("https://ident.me") //nolint
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	return string(body), nil
}

// Builds custom retryable HTTP client with tracing
func createCustomClient() *retryhttp.Client {
	client := retryhttp.NewClient()
	client.HTTPClient.Timeout = 30 * time.Second
	client.HTTPClient.Transport = otelhttp.NewTransport(
		http.DefaultTransport,
		otelhttp.WithSpanNameFormatter(otel.CustomSpanNameFormatter),
	)

	if os.Getenv("DEBUG") == "true" || os.Getenv("DEBUG") == "1" {
		client.RetryMax = 0 // max number of retries
	} else {
		client.RetryMax = 2                   // max number of retries
		client.RetryWaitMin = 2 * time.Second // min wait time between retries
	}

	return client
}
