package warehouse

import (
	"context"
	"fmt"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

func Upsert(ctx context.Context, warehouse *models.Warehouse) error {
	return rds.WithContext(ctx).Clauses(
		clause.Returning{},
		clause.OnConflict{
			Columns: []clause.Column{
				{Name: "warehouse_id"},
				{Name: "source"},
			},
			UpdateAll: true,
		},
	).Create(warehouse).Error
}

func OnboardWarehouses(ctx context.Context, warehouses []models.Warehouse) error {
	return rds.WithContext(ctx).CreateInBatches(&warehouses, 1000).
		Clauses(clause.Returning{}, clause.OnConflict{
			Columns:   []clause.Column{{Name: "source"}, {Name: "warehouse_id"}},
			UpdateAll: true,
		}).Error
}

// CreateWarehouseAddress creates an address association from a load
func CreateWarehouseAddress(
	ctx context.Context,
	warehouse models.Warehouse,
	load models.Load,
	requestType models.RequestType,
) error {

	var address models.WarehouseAddress

	switch requestType {
	case models.RequestTypePickup:
		address = models.WarehouseAddress{
			Address: models.ToAddressModel(load.Pickup.CompanyCoreInfo),
		}

	case models.RequestTypeDropoff:
		address = models.WarehouseAddress{
			Address: models.ToAddressModel(load.Consignee.CompanyCoreInfo),
		}

	default:
		return fmt.Errorf("invalid request type: %s", requestType)
	}

	return rds.WithContext(ctx).
		Model(&warehouse).
		Association("Addresses").
		Append(&address)
}

// CreateWarehouseWithAddress creates a new warehouse with address lines and address associations
func CreateWarehouseWithAddress(
	ctx context.Context,
	warehouseID string,
	warehouseName string,
	addressLine1 string,
	addressLine2 string,
	source models.WarehouseSource,
	address models.Address,
) (*models.Warehouse, error) {

	// Create the warehouse with address lines populated
	warehouse := &models.Warehouse{
		WarehouseID:             warehouseID,
		WarehouseName:           warehouseName,
		WarehouseAddressLine1:   addressLine1,
		WarehouseAddressLine2:   addressLine2,
		WarehouseFullAddress:    fmt.Sprintf("%s %s", addressLine1, addressLine2),
		WarehouseFullIdentifier: fmt.Sprintf("%s %s %s", warehouseName, addressLine1, addressLine2),
		Source:                  source,
	}

	// Create warehouse address for association
	warehouseAddress := models.WarehouseAddress{
		Address: address,
	}

	// Use transaction to ensure both warehouse and address association are created
	return warehouse, rds.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Create the warehouse first
		if err := tx.Create(warehouse).Error; err != nil {
			return fmt.Errorf("failed to create warehouse: %w", err)
		}

		// Create the address association
		if err := tx.Model(warehouse).Association("Addresses").Append(&warehouseAddress); err != nil {
			return fmt.Errorf("failed to create warehouse address association: %w", err)
		}

		return nil
	})
}
