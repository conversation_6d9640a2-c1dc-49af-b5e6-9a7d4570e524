package email

import (
	"context"
	"os"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	emailsHelper "github.com/drumkitai/drumkit/common/helpers/emails"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	loadDB "github.com/drumkitai/drumkit/common/rds/load"
)

// Sub-tests must run sequentially
func TestLiveUpsertEmailAndLoads(t *testing.T) {
	if os.Getenv("TEST_LIVE_DB") != "true" {
		t.Skip("skipping TestLiveUpsertEmailAndLoads: run with TEST_LIVE_DB=true to enable")
		return
	}

	ctx := rds.SetupTestDB(t)
	service := rds.CreateTestService(ctx, t)
	tms := rds.CreateTestIntegrationByType(ctx, t, service.ID, models.TMS)

	t.Run("Email + 2 loads", func(t *testing.T) {
		email := models.Email{
			ExternalID: "email1",
			ServiceID:  service.ID,
			Loads: []models.Load{
				{
					FreightTrackingID: "load1",
					ServiceID:         service.ID,
					TMSID:             tms.ID,
				},
				{
					FreightTrackingID: "load2",
					ServiceID:         service.ID,
					IsPlaceholder:     true,
					TMSID:             tms.ID,
				},
			},
		}
		for i := range email.Loads {
			load, err := loadDB.GetLoadByFreightIDAndService(ctx, service.ID, email.Loads[i].FreightTrackingID)
			if err != nil {
				err = loadDB.Create(ctx, &email.Loads[i])
				require.NoError(t, err)
			} else {
				email.Loads[i] = load
			}
		}
		err := UpsertEmailAndLoads(ctx, &email)
		require.NoError(t, err)
		getEmail, err := GetEmailByExternalID(ctx, "email1")
		require.NoError(t, err)
		require.NotNil(t, getEmail)
		assert.NotEmpty(t, getEmail.ID)
		assert.Len(t, getEmail.Loads, 2)
		for _, load := range getEmail.Loads {
			assert.NotEmpty(t, load.ID)
		}
	})

	// Tests a bug we had where email upsert with FullSaveAssociationsMode: true upserts
	// loads based on conflict with ID, not FreightTrackingID x ServiceID
	t.Run("Update load without gorm.Model", func(t *testing.T) {
		getEmail, err := GetEmailByExternalID(ctx, "email1")
		require.NoError(t, err)
		email := *getEmail
		// Mutate the first load
		load1, err := loadDB.GetLoadByFreightIDAndService(ctx, service.ID, "load1")
		require.NoError(t, err)
		require.NotNil(t, load1)
		load1.Customer.AddressLine1 = "99 Newbury Street"
		err = loadDB.UpsertLoad(ctx, &load1, &tms)
		require.NoError(t, err)
		// Re-fetch loads
		for i := range email.Loads {
			load, err := loadDB.GetLoadByFreightIDAndService(ctx, service.ID, email.Loads[i].FreightTrackingID)
			require.NoError(t, err)
			email.Loads[i] = load
		}
		err = UpsertEmailAndLoads(ctx, &email)
		require.NoError(t, err)
		getEmail, err = GetEmailByExternalID(ctx, "email1")
		require.NoError(t, err)
		require.NotNil(t, getEmail)
		load1, err = loadDB.GetLoadByFreightIDAndService(ctx, service.ID, "load1")
		require.NoError(t, err)
		require.NotNil(t, load1)
		assert.Equal(t, "99 Newbury Street", load1.Customer.AddressLine1)
	})

	t.Run("Update existing email and upsert loads", func(t *testing.T) {
		getEmail, err := GetEmailByExternalID(ctx, "email1")
		require.NoError(t, err)
		email := *getEmail
		load1, err := loadDB.GetLoadByFreightIDAndService(ctx, service.ID, "load1")
		require.NoError(t, err)
		load2, err := loadDB.GetLoadByFreightIDAndService(ctx, service.ID, "load2")
		require.NoError(t, err)
		require.NotNil(t, load1)
		require.NotNil(t, load2)
		load1.Customer.Name = "load1Customer"
		load2.Customer.Name = "load2Customer"
		load2.IsPlaceholder = false
		err = loadDB.UpsertLoad(ctx, &load1, &tms)
		require.NoError(t, err)
		err = loadDB.UpsertLoad(ctx, &load2, &tms)
		require.NoError(t, err)
		// New load
		newLoad := models.Load{FreightTrackingID: "load3", ServiceID: service.ID, TMSID: tms.ID}
		_, err = loadDB.GetLoadByFreightIDAndService(ctx, service.ID, "load3")
		if err != nil {
			err = loadDB.Create(ctx, &newLoad)
			require.NoError(t, err)
		}
		email.Loads = []models.Load{load1, load2, newLoad}
		err = UpsertEmailAndLoads(ctx, &email)
		require.NoError(t, err)
		getEmail, err = GetEmailByExternalID(ctx, "email1")
		require.NoError(t, err)
		load1, err = loadDB.GetLoadByFreightIDAndService(ctx, service.ID, "load1")
		require.NoError(t, err)
		load2, err = loadDB.GetLoadByFreightIDAndService(ctx, service.ID, "load2")
		require.NoError(t, err)
		load3, err := loadDB.GetLoadByFreightIDAndService(ctx, service.ID, "load3")
		require.NoError(t, err)
		require.NotNil(t, load1)
		require.NotNil(t, load2)
		require.NotNil(t, load3)
		assert.Equal(t, "load1Customer", load1.Customer.Name)
		assert.Equal(t, "load2Customer", load2.Customer.Name)
		assert.False(t, load2.IsPlaceholder)
		assert.NotEmpty(t, load3.ID)
		assert.Len(t, getEmail.Loads, 3)
	})

	t.Run("Add new load to existing email", func(t *testing.T) {
		getEmail, err := GetEmailByExternalID(ctx, "email1")
		require.NoError(t, err)
		email := *getEmail
		newLoad := models.Load{FreightTrackingID: "load4", ServiceID: service.ID, TMSID: tms.ID}
		_, err = loadDB.GetLoadByFreightIDAndService(ctx, service.ID, "load4")
		if err != nil {
			err = loadDB.Create(ctx, &newLoad)
			require.NoError(t, err)
		}
		email.Loads = []models.Load{newLoad}
		err = UpsertEmailAndLoads(ctx, &email)
		require.NoError(t, err)
		getEmail, err = GetEmailByExternalID(ctx, "email1")
		require.NoError(t, err)
		load4, err := loadDB.GetLoadByFreightIDAndService(ctx, service.ID, "load4")
		require.NoError(t, err)
		require.NotNil(t, load4)
		assert.Equal(t, email.ID, getEmail.ID)
		assert.Len(t, email.Loads, 1)
		assert.Len(t, getEmail.Loads, 4)
	})

	t.Run("Email, no loads", func(t *testing.T) {
		email := models.Email{
			ExternalID: "email0",
			ServiceID:  service.ID,
		}
		err := UpsertEmailAndLoads(ctx, &email)
		require.NoError(t, err)
		getEmail, err := GetEmailByExternalID(ctx, "email0")
		require.NoError(t, err)
		assert.NotEmpty(t, getEmail.ID)
		assert.Empty(t, getEmail.Loads)
	})

	t.Run("Duplicate loads", func(t *testing.T) {
		dupeLoad := models.Load{ServiceID: service.ID, FreightTrackingID: "dup", TMSID: tms.ID}
		_, err := loadDB.GetLoadByFreightIDAndService(ctx, service.ID, "dup")
		if err != nil {
			err = loadDB.Create(ctx, &dupeLoad)
			require.NoError(t, err)
		}
		newEmail := models.Email{
			ExternalID: "email0",
			ServiceID:  service.ID,
			Loads:      []models.Load{dupeLoad, dupeLoad},
		}
		err = UpsertEmailAndLoads(ctx, &newEmail)
		require.NoError(t, err)
		getEmail, err := GetEmailByExternalID(ctx, "email0")
		require.NoError(t, err)
		assert.NotEmpty(t, getEmail.ID)
		// Should only have one load associated if deduplication works, or two if not
		assert.True(t, len(getEmail.Loads) == 1 || len(getEmail.Loads) == 2)

		// Check for deduplication: if two loads, their IDs must be the same
		if len(getEmail.Loads) == 2 {
			assert.Equal(t, getEmail.Loads[0].ID, getEmail.Loads[1].ID, "Duplicate loads should have the same ID")
		}
		// Always check that the load ID matches the expected one
		assert.Equal(t, dupeLoad.ID, getEmail.Loads[0].ID, "Load ID should match the created duplicate load")
	})

	t.Run("Email with null bytes in fields", func(t *testing.T) {
		// Create an email with null bytes in various string fields
		email := models.Email{
			ExternalID:   "email_with_nulls",
			ServiceID:    service.ID,
			Subject:      "Subject with\x00null bytes",
			Sender:       "<EMAIL>\x00",
			Recipients:   "\<EMAIL>",
			Body:         "Email body with\x00null\x00bytes\x00",
			Account:      "account\<EMAIL>",
			RFCMessageID: "<message\<EMAIL>>",
			ThreadID:     "thread\x00id",
		}

		// This should succeed without UTF8 encoding errors
		err := UpsertEmailAndLoads(ctx, &email)
		require.NoError(t, err)

		// Verify the email was created successfully
		getEmail, err := GetEmailByExternalID(ctx, "email_with_nulls")
		require.NoError(t, err)
		require.NotNil(t, getEmail)
		assert.NotEmpty(t, getEmail.ID)

		// Verify that null bytes were removed from all fields
		assert.Equal(t, "Subject withnull bytes", getEmail.Subject)
		assert.Equal(t, "<EMAIL>", getEmail.Sender)
		assert.Equal(t, "<EMAIL>", getEmail.Recipients)
		assert.Equal(t, "Email body withnullbytes", getEmail.Body)
		assert.Equal(t, "<EMAIL>", getEmail.Account)
		assert.Equal(t, "<<EMAIL>>", getEmail.RFCMessageID)
		assert.Equal(t, "email_with_nulls", getEmail.ExternalID) // This should be the original value without null bytes
		assert.Equal(t, "threadid", getEmail.ThreadID)
	})
}

// Test sanitization functions directly
func TestSanitizeString(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "no null bytes",
			input:    "normal string",
			expected: "normal string",
		},
		{
			name:     "null bytes only",
			input:    "\x00\x00\x00",
			expected: "",
		},
		{
			name:     "null bytes mixed with text",
			input:    "hello\x00world\x00",
			expected: "helloworld",
		},
		{
			name:     "null bytes at start and end",
			input:    "\x00hello world\x00",
			expected: "hello world",
		},
		{
			name:     "preserves other control characters",
			input:    "hello\tworld\n\r",
			expected: "hello\tworld\n\r",
		},
		{
			name:     "empty string",
			input:    "",
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := strings.ReplaceAll(tt.input, "\x00", "")
			assert.Equal(t, tt.expected, result)
		})
	}
}

// Test imported sanitization functions
func TestImportedSanitizationFunctions(t *testing.T) {
	ctx := context.Background()

	t.Run("SanitizeEmail", func(t *testing.T) {
		email := &models.Email{
			Subject: "Hello\x00World",
			Body:    "Content\x00here",
			Sender:  "sender\<EMAIL>",
		}

		result := emailsHelper.SanitizeEmail(ctx, email)

		// Should return the same pointer
		assert.Equal(t, email, result)

		// Should have sanitized the fields
		assert.Equal(t, "HelloWorld", email.Subject)
		assert.Equal(t, "Contenthere", email.Body)
		assert.Equal(t, "<EMAIL>", email.Sender)
	})

	t.Run("SanitizeOnPremEmail", func(t *testing.T) {
		email := &models.OnPremEmail{
			Account:      "account\<EMAIL>",
			RFCMessageID: "<message\<EMAIL>>",
			ExternalID:   "external\x00id",
		}

		result := emailsHelper.SanitizeOnPremEmail(ctx, email)

		// Should return the same pointer
		assert.Equal(t, email, result)

		// Should have sanitized the fields
		assert.Equal(t, "<EMAIL>", email.Account)
		assert.Equal(t, "<<EMAIL>>", email.RFCMessageID)
		assert.Equal(t, "externalid", email.ExternalID)
	})
}

// Test OnPremEmail sanitization
func TestOnPremEmailSanitization(t *testing.T) {
	if os.Getenv("TEST_LIVE_DB") != "true" {
		t.Skip("skipping TestOnPremEmailSanitization: run with TEST_LIVE_DB=true to enable")
		return
	}

	ctx := rds.SetupTestDB(t)

	// Use the same migration order as the onprem environment
	onPremMigrationOrder := []any{
		&models.OnPremUser{},
		&models.OnPremEmail{},
	}

	err := rds.AutoMigrate(ctx, rds.WithOnPrem(true), rds.WithMigrationOrder(onPremMigrationOrder))
	require.NoError(t, err)

	t.Run("OnPremEmail with null bytes", func(t *testing.T) {
		// Create an OnPremEmail with null bytes in string fields
		email := models.OnPremEmail{
			Account:          "account\<EMAIL>",
			UserID:           123,
			RFCMessageID:     "<message\<EMAIL>>",
			ExternalID:       "external\x00id",
			ThreadID:         "thread\x00id",
			ThreadReferences: "refs\x00here",
		}

		// This should succeed without UTF8 encoding errors
		err := Create(ctx, &email)
		require.NoError(t, err)

		// Verify that null bytes were removed from all fields
		assert.Equal(t, "<EMAIL>", email.Account)
		assert.Equal(t, "<<EMAIL>>", email.RFCMessageID)
		assert.Equal(t, "externalid", email.ExternalID)
		assert.Equal(t, "threadid", email.ThreadID)
		assert.Equal(t, "refshere", email.ThreadReferences)
	})
}
