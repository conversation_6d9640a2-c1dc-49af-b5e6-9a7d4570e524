package rds

import (
	"context"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
)

// Close gracefully closes all database connections
func Close(ctx context.Context) error {
	var errs []error

	// Close main database connection
	if db != nil {
		if sqlDB, err := db.DB(); err == nil {
			if err := sqlDB.Close(); err != nil {
				log.Error(ctx, "failed to close main database connection", zap.Error(err))
				errs = append(errs, err)
			} else {
				log.Info(ctx, "main database connection closed successfully")
			}
		}
		db = nil
	}

	// Close reader database connection
	if dbReader != nil {
		if sqlDB, err := dbReader.DB(); err == nil {
			if err := sqlDB.Close(); err != nil {
				log.Error(ctx, "failed to close reader database connection", zap.Error(err))
				errs = append(errs, err)
			} else {
				log.Info(ctx, "reader database connection closed successfully")
			}
		}
		dbReader = nil
	}

	// Return first error if any occurred
	if len(errs) > 0 {
		return errs[0]
	}

	log.Info(ctx, "all database connections closed successfully")
	return nil
}

// CloseWithTimeout closes database connections with a timeout context
func CloseWithTimeout(ctx context.Context, timeout time.Duration) error {
	ctx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	return Close(ctx)
}
